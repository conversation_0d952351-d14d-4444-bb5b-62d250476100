# 10 - Database Management

**Section:** 10-database-management  
**Version:** 1.0  
**Last Updated:** July 2025  
**Prerequisites:** [Component Models](090-component-models.md) completed  
**Estimated Reading Time:** 30 minutes  

## Overview

The Ultimate Electrical Designer uses a comprehensive database management system with SQLite for development and PostgreSQL for production. This section covers database schema, migration procedures, real database testing requirements, backup strategies, and performance optimization that maintain engineering-grade data integrity and reliability.

## Table of Contents

- [Database Architecture](#database-architecture)
- [Schema Design](#schema-design)
- [Migration Management](#migration-management)
- [Real Database Testing](#real-database-testing)
- [Backup and Recovery](#backup-and-recovery)
- [Performance Optimization](#performance-optimization)
- [Data Integrity](#data-integrity)
- [Monitoring and Maintenance](#monitoring-and-maintenance)

## Database Architecture

### Database Configuration

#### Development Database (SQLite)
**Location:** `server/data/app_dev.db`  
**Purpose:** Development, testing, and local development  
**Features:**
- WAL (Write-Ahead Logging) mode for concurrent access
- Foreign key constraint enforcement
- Full-text search capabilities
- JSON support for flexible data storage
- Automatic backup on schema changes

```python
# config/database.py - Development configuration
DEVELOPMENT_DATABASE_CONFIG = {
    "url": "sqlite:///./data/app_dev.db",
    "connect_args": {
        "check_same_thread": False,
        "isolation_level": None,
    },
    "engine_options": {
        "echo": False,  # Set to True for SQL debugging
        "pool_pre_ping": True,
        "pool_recycle": 300,
    },
    "sqlite_pragmas": {
        "journal_mode": "WAL",
        "cache_size": -1 * 64000,  # 64MB cache
        "foreign_keys": 1,
        "ignore_check_constraints": 0,
        "synchronous": 0,
    }
}
```

#### Production Database (PostgreSQL)
**Purpose:** Production deployment with advanced features  
**Features:**
- ACID compliance with full transaction support
- Advanced indexing and query optimization
- Concurrent access with connection pooling
- Automated backup and point-in-time recovery
- Performance monitoring and analytics

```python
# config/database.py - Production configuration
PRODUCTION_DATABASE_CONFIG = {
    "url": "postgresql://user:password@localhost:5432/ultimate_electrical_designer",
    "engine_options": {
        "echo": False,
        "pool_size": 20,
        "max_overflow": 30,
        "pool_pre_ping": True,
        "pool_recycle": 3600,
    },
    "connection_options": {
        "application_name": "ultimate_electrical_designer",
        "connect_timeout": 10,
        "command_timeout": 30,
    }
}
```

### Database Session Management

#### Session Factory Configuration
```python
# core/database/session.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator

from config.settings import get_settings

settings = get_settings()

# Create database engine
if settings.DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        settings.DATABASE_URL,
        connect_args=DEVELOPMENT_DATABASE_CONFIG["connect_args"],
        poolclass=StaticPool,
        **DEVELOPMENT_DATABASE_CONFIG["engine_options"]
    )
    
    # Configure SQLite pragmas
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        for pragma, value in DEVELOPMENT_DATABASE_CONFIG["sqlite_pragmas"].items():
            cursor.execute(f"PRAGMA {pragma}={value}")
        cursor.close()
        
else:
    engine = create_engine(
        settings.DATABASE_URL,
        **PRODUCTION_DATABASE_CONFIG["engine_options"]
    )

# Create session factory
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

def get_db() -> Generator[Session, None, None]:
    """Get database session with automatic cleanup."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """Get database session with context manager."""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()
```

## Schema Design

### Core Database Schema

The database schema follows a normalized design with clear relationships and comprehensive constraints:

```sql
-- Core schema structure
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE projects (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    voltage_level FLOAT NOT NULL CHECK (voltage_level > 0),
    environment_type VARCHAR(20) NOT NULL CHECK (environment_type IN ('indoor', 'outdoor', 'hazardous')),
    ambient_temperature_min FLOAT NOT NULL CHECK (ambient_temperature_min >= -50 AND ambient_temperature_min <= 100),
    ambient_temperature_max FLOAT NOT NULL CHECK (ambient_temperature_max >= -50 AND ambient_temperature_max <= 100),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'on_hold', 'completed', 'cancelled')),
    client_name VARCHAR(255),
    project_location VARCHAR(500),
    design_standards JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id),
    CONSTRAINT ck_temperature_range CHECK (ambient_temperature_max > ambient_temperature_min)
);

CREATE TABLE components (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    component_type VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(255),
    model_number VARCHAR(255),
    description TEXT,
    voltage_rating FLOAT,
    current_rating FLOAT,
    power_rating FLOAT,
    frequency_rating FLOAT,
    dimensions_length FLOAT,
    dimensions_width FLOAT,
    dimensions_height FLOAT,
    weight FLOAT,
    temperature_min FLOAT,
    temperature_max FLOAT,
    ip_rating VARCHAR(10),
    standards_compliance JSON,
    certifications JSON,
    unit_cost FLOAT,
    currency VARCHAR(3) DEFAULT 'USD',
    lead_time_days INTEGER,
    supplier VARCHAR(255),
    specifications JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id)
);

CREATE TABLE pipes (
    id INTEGER PRIMARY KEY,
    project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    tag VARCHAR(100) NOT NULL,
    diameter FLOAT NOT NULL CHECK (diameter > 0),
    length FLOAT NOT NULL CHECK (length > 0),
    insulation_thickness FLOAT CHECK (insulation_thickness >= 0),
    insulation_type VARCHAR(100),
    pipe_material VARCHAR(100),
    fluid_type VARCHAR(100),
    design_temperature FLOAT,
    operating_temperature FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id),
    UNIQUE(project_id, tag)
);

CREATE TABLE heat_tracing_circuits (
    id INTEGER PRIMARY KEY,
    pipe_id INTEGER REFERENCES pipes(id) ON DELETE CASCADE,
    tank_id INTEGER REFERENCES tanks(id) ON DELETE CASCADE,
    circuit_type VARCHAR(50) NOT NULL CHECK (circuit_type IN ('self_regulating', 'series_resistance')),
    cable_type VARCHAR(100),
    power_per_meter FLOAT CHECK (power_per_meter > 0),
    total_length FLOAT CHECK (total_length > 0),
    voltage_supply FLOAT CHECK (voltage_supply > 0),
    control_method VARCHAR(50),
    thermostat_setting FLOAT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id),
    CONSTRAINT ck_circuit_parent CHECK (
        (pipe_id IS NOT NULL AND tank_id IS NULL) OR 
        (pipe_id IS NULL AND tank_id IS NOT NULL)
    )
);

CREATE TABLE electrical_nodes (
    id INTEGER PRIMARY KEY,
    project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    tag VARCHAR(100) NOT NULL,
    node_type VARCHAR(50) NOT NULL,
    voltage_level FLOAT NOT NULL CHECK (voltage_level > 0),
    location_x FLOAT,
    location_y FLOAT,
    location_z FLOAT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id),
    UNIQUE(project_id, tag)
);

CREATE TABLE cable_routes (
    id INTEGER PRIMARY KEY,
    project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    from_node_id INTEGER NOT NULL REFERENCES electrical_nodes(id),
    to_node_id INTEGER NOT NULL REFERENCES electrical_nodes(id),
    cable_type VARCHAR(100),
    cable_size VARCHAR(50),
    length FLOAT NOT NULL CHECK (length > 0),
    installation_method VARCHAR(100),
    routing_path JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id),
    CONSTRAINT ck_different_nodes CHECK (from_node_id != to_node_id)
);
```

### Indexes for Performance

```sql
-- Performance indexes
CREATE INDEX idx_projects_name ON projects(name);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_environment ON projects(environment_type);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_deleted_at ON projects(deleted_at);

CREATE INDEX idx_components_type ON components(component_type);
CREATE INDEX idx_components_category ON components(category);
CREATE INDEX idx_components_manufacturer ON components(manufacturer);
CREATE INDEX idx_components_deleted_at ON components(deleted_at);

CREATE INDEX idx_pipes_project_id ON pipes(project_id);
CREATE INDEX idx_pipes_tag ON pipes(tag);
CREATE INDEX idx_pipes_deleted_at ON pipes(deleted_at);

CREATE INDEX idx_ht_circuits_pipe_id ON heat_tracing_circuits(pipe_id);
CREATE INDEX idx_ht_circuits_tank_id ON heat_tracing_circuits(tank_id);
CREATE INDEX idx_ht_circuits_active ON heat_tracing_circuits(is_active);
CREATE INDEX idx_ht_circuits_deleted_at ON heat_tracing_circuits(deleted_at);

CREATE INDEX idx_electrical_nodes_project_id ON electrical_nodes(project_id);
CREATE INDEX idx_electrical_nodes_type ON electrical_nodes(node_type);
CREATE INDEX idx_electrical_nodes_deleted_at ON electrical_nodes(deleted_at);

CREATE INDEX idx_cable_routes_project_id ON cable_routes(project_id);
CREATE INDEX idx_cable_routes_from_node ON cable_routes(from_node_id);
CREATE INDEX idx_cable_routes_to_node ON cable_routes(to_node_id);
CREATE INDEX idx_cable_routes_deleted_at ON cable_routes(deleted_at);
```

## Migration Management

### Alembic Configuration

#### Migration Environment Setup
```python
# alembic/env.py
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from src.core.models.base import Base
from config.settings import get_settings

# Import all models to ensure they're registered
from src.core.models.user import User
from src.core.models.project import Project
from src.core.models.component import Component
from src.core.models.pipe import Pipe
from src.core.models.tank import Tank
from src.core.models.heat_tracing_circuit import HeatTracingCircuit
from src.core.models.electrical_node import ElectricalNode
from src.core.models.cable_route import CableRoute

config = context.config
settings = get_settings()

# Set database URL from settings
config.set_main_option("sqlalchemy.url", settings.DATABASE_URL)

if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True,
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
```

### Migration Commands

#### Essential Migration Operations
```bash
# Create new migration
make create-migration MESSAGE="Add component specifications table"
# Equivalent to: alembic revision --autogenerate -m "Add component specifications table"

# Apply migrations
make migrate-db
# Equivalent to: alembic upgrade head

# Check migration status
alembic current
# Shows current migration version

# Show migration history
alembic history --verbose
# Shows complete migration history

# Downgrade migration (use with caution)
alembic downgrade -1
# Downgrades one migration

# Show SQL for migration (dry run)
alembic upgrade head --sql
# Shows SQL that would be executed without running it
```

#### Migration Best Practices
```python
# Example migration file structure
"""Add heat tracing circuit monitoring

Revision ID: abc123def456
Revises: def456ghi789
Create Date: 2025-07-05 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'abc123def456'
down_revision = 'def456ghi789'
branch_labels = None
depends_on = None

def upgrade() -> None:
    """Add heat tracing circuit monitoring capabilities."""
    # Add new columns with proper constraints
    op.add_column('heat_tracing_circuits', 
        sa.Column('monitoring_enabled', sa.Boolean(), nullable=False, server_default='false')
    )
    op.add_column('heat_tracing_circuits',
        sa.Column('alarm_high_temp', sa.Float(), nullable=True)
    )
    op.add_column('heat_tracing_circuits',
        sa.Column('alarm_low_temp', sa.Float(), nullable=True)
    )
    
    # Add check constraint for alarm temperatures
    op.create_check_constraint(
        'ck_alarm_temperature_range',
        'heat_tracing_circuits',
        'alarm_high_temp IS NULL OR alarm_low_temp IS NULL OR alarm_high_temp > alarm_low_temp'
    )
    
    # Create index for monitoring queries
    op.create_index('idx_ht_circuits_monitoring', 'heat_tracing_circuits', ['monitoring_enabled'])

def downgrade() -> None:
    """Remove heat tracing circuit monitoring capabilities."""
    # Remove index
    op.drop_index('idx_ht_circuits_monitoring', table_name='heat_tracing_circuits')
    
    # Remove check constraint
    op.drop_constraint('ck_alarm_temperature_range', 'heat_tracing_circuits', type_='check')
    
    # Remove columns
    op.drop_column('heat_tracing_circuits', 'alarm_low_temp')
    op.drop_column('heat_tracing_circuits', 'alarm_high_temp')
    op.drop_column('heat_tracing_circuits', 'monitoring_enabled')
```

## Real Database Testing

### No Mocks Policy Implementation

The project maintains strict "NO MOCKS" policy for database testing:

#### Test Database Configuration
```python
# tests/conftest.py - Real database testing setup
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.core.models.base import Base

TEST_DATABASE_URL = "sqlite:///./test_app.db"

@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine with real database."""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=False  # Set to True for SQL debugging
    )
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup
    Base.metadata.drop_all(bind=engine)
    import os
    if os.path.exists("./test_app.db"):
        os.remove("./test_app.db")

@pytest.fixture(scope="function")
def db_session(test_engine):
    """Create database session for each test with real database."""
    TestingSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_engine
    )
    
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.rollback()
        session.close()
```

#### Real Database Test Examples
```python
# tests/integration/test_database_integrity.py
import pytest
from sqlalchemy.exc import IntegrityError
from src.core.models.project import Project
from src.core.models.pipe import Pipe

@pytest.mark.database
@pytest.mark.integration
class TestDatabaseIntegrity:
    """Test database integrity constraints with real database."""
    
    def test_project_name_uniqueness_constraint(self, db_session):
        """Test project name uniqueness is enforced by database."""
        # Create first project
        project1 = Project(
            name="Test Project",
            voltage_level=480.0,
            environment_type="indoor",
            ambient_temperature_min=0.0,
            ambient_temperature_max=40.0
        )
        db_session.add(project1)
        db_session.commit()
        
        # Attempt to create duplicate name
        project2 = Project(
            name="Test Project",  # Same name
            voltage_level=600.0,
            environment_type="outdoor",
            ambient_temperature_min=-10.0,
            ambient_temperature_max=50.0
        )
        db_session.add(project2)
        
        # Should raise integrity error
        with pytest.raises(IntegrityError) as exc_info:
            db_session.commit()
        
        assert "unique constraint" in str(exc_info.value).lower()
    
    def test_temperature_range_check_constraint(self, db_session):
        """Test temperature range check constraint."""
        # Attempt to create project with invalid temperature range
        project = Project(
            name="Invalid Temperature Project",
            voltage_level=480.0,
            environment_type="indoor",
            ambient_temperature_min=50.0,  # Higher than max
            ambient_temperature_max=40.0   # Lower than min
        )
        db_session.add(project)
        
        # Should raise integrity error
        with pytest.raises(IntegrityError) as exc_info:
            db_session.commit()
        
        assert "temperature_range" in str(exc_info.value).lower()
    
    def test_foreign_key_cascade_deletion(self, db_session):
        """Test cascade deletion with real database."""
        # Create project
        project = Project(
            name="Cascade Test Project",
            voltage_level=480.0,
            environment_type="indoor",
            ambient_temperature_min=0.0,
            ambient_temperature_max=40.0
        )
        db_session.add(project)
        db_session.commit()
        
        # Create pipe associated with project
        pipe = Pipe(
            project_id=project.id,
            tag="P-001",
            diameter=0.1,
            length=100.0,
            insulation_thickness=0.05
        )
        db_session.add(pipe)
        db_session.commit()
        
        # Verify pipe exists
        assert db_session.query(Pipe).filter_by(project_id=project.id).count() == 1
        
        # Delete project
        db_session.delete(project)
        db_session.commit()
        
        # Verify pipe was cascade deleted
        assert db_session.query(Pipe).filter_by(project_id=project.id).count() == 0
```

---

**Navigation:**  
← [Previous: Component Models](090-component-models.md) | [Handbook Home](001-cover.md) | [Next: Docker Dev Deployment](./110-docker-dev-deployment.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Migration Scripts](../../server/alembic/versions/)
- [Performance Optimization](./backend/performance-optimization.md/)
