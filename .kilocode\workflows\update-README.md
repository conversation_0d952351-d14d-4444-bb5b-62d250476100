### 13. Workflow: Update Main README.md

This workflow defines the process for keeping the project's primary overview document, `README.md`, current with the latest project status, features, and key information, ensuring it reflects the "Current Project Phase" and "Revision History" for both backend and frontend.

* **13.1. Discovery & Analysis:**
    * **Task:** Review the `README.md` file for any outdated information, including project status, feature lists, technology stack versions (backend and frontend), or instructions that no longer apply based on recent sprints or major changes.
    * **Guidance for AI:** Compare the content of the `README.md` with the "Current Project Phase" (implemented/verified, in-progress, planned, not started sections), "Key Features", "Technology Stack", and "Technical Debt & Quality Metrics" sections to identify discrepancies across the full stack.
* **13.2. Task Planning:**
    * **Task:** Plan the specific updates required for the `README.md`, outlining which sections need modification.
    * **Guidance for AI:** Create a list of `README.md` sections to update (e.g., Development Status Overview, Active Development Areas, Key Features, Technology Stack, Technical Debt & Quality Metrics, Revision History). Ensure the technology stack lists all relevant versions for both backend and frontend (e.g., **FastAPI-0.115+**, **Next.js-15.3+**, **.NET 8.0+**, **Python 3.13+**).
* **13.3. Implementation:**
    * **Task:** Update the relevant sections of the `README.md` to accurately reflect the current project state, recent achievements, upcoming plans, and any changes to key information for both backend and frontend.
    * **Guidance for AI:** Modify the markdown content of `README.md`. Ensure clarity, conciseness, and accuracy. Update version numbers for all listed technologies.
* **13.4. Verification:**
    * **Task:** Verify that all updates align with the actual project state, documented changes, and project vision. Ensure no new errors or inconsistencies are introduced.
    * **Guidance for AI:** Cross-reference the updated `README.md` against other primary project documentation (e.g., sprint reports, architectural diagrams, frontend specification) to confirm consistency across the entire project.
* **13.5. Documentation & Handover:**
    * **Task:** Commit the updated `README.md` to the main branch.
    * **Guidance for AI:** Ensure the changes are committed with a conventional commit message that clearly indicates the nature of the update (e.g., `docs: update README with latest project status and frontend spec details`).