# src/alembic/env.py
"""Alembic Database Migration Environment Configuration.

This module configures the Alembic environment for database migrations in the
Ultimate Electrical Designer backend application. It handles database connection
setup, migration context configuration, and migration execution.
"""

import logging
from logging.config import fileConfig
from typing import Any, Optional

from alembic import context

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set up logger for this module
logger = logging.getLogger(__name__)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata

import os
import sys

# Add the project root directory to the sys.path to import project modules
# This env.py is in src/alembic, so we need to go up two directories to reach the project root
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, project_root)

# Import all models to register them with SQLAlchemy metadata
import src.core.models  # This imports all models

# Import your Base object and settings
from src.config.settings import settings
from src.core.models.base import Base

target_metadata = Base.metadata


def _get_db_type_from_url(url: str) -> str:
    """Extract database type from URL for logging purposes."""
    url_str = str(url)
    if url_str.startswith("sqlite"):
        return "SQLite"
    if url_str.startswith("mssql") or url_str.startswith("sqlserver"):
        return "SQL Server"
    if url_str.startswith("postgresql"):
        return "PostgreSQL"
    if url_str.startswith("mysql"):
        return "MySQL"
    return "Unknown"


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    # Use the effective database URL from settings with fallback logic
    url = settings.effective_database_url
    logger.info(f"Running offline migrations with URL: {_get_db_type_from_url(url)}")

    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def compare_type(
    context: Any, inspected_column: Any, metadata_column: Any, inspected_type: Any, metadata_type: Any
) -> Optional[bool]:
    """Custom type comparison function to handle SQLite type differences."""
    # For SQLite, ignore VARCHAR vs TEXT differences and VARCHAR vs FlexibleJSON
    if context.dialect.name == "sqlite":
        # Convert types to string for comparison
        inspected_str = str(inspected_type).upper()
        metadata_str = str(metadata_type).upper()

        # Ignore VARCHAR vs TEXT differences
        if ("VARCHAR" in inspected_str and "TEXT" in metadata_str) or (
            "TEXT" in inspected_str and "VARCHAR" in metadata_str
        ):
            return False

        # Ignore VARCHAR vs FlexibleJSON differences (both are text in SQLite)
        if ("VARCHAR" in inspected_str and "FLEXIBLEJSON" in metadata_str) or (
            "FLEXIBLEJSON" in inspected_str and "VARCHAR" in metadata_str
        ):
            return False

    # Use default comparison for other cases
    return None


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Import the database engine creation function
    from src.core.database.engine import create_engine

    try:
        # Create engine using our database layer with fallback logic
        connectable = create_engine(echo=settings.DB_ECHO)
        logger.info(
            f"Running online migrations with: {_get_db_type_from_url(connectable.url)}"
        )

        with connectable.connect() as connection:
            # Enable batch mode for SQLite to support ALTER operations
            context.configure(
                connection=connection,
                target_metadata=target_metadata,
                compare_type=compare_type,
                render_as_batch=connection.dialect.name == "sqlite",
            )

            with context.begin_transaction():
                context.run_migrations()

    except Exception as e:
        logger.error(f"Failed to run online migrations: {e}")
        raise


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
