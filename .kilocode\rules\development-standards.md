### Development Standards

1. **Robust design principles:** Apply **SOLID** principles for structural design, ensuring maintainability and flexibility through focused responsibilities, extensibility, and proper abstraction. Complement these with practices like **DRY**, **KISS**, and **TDD** to streamline implementation, reduce complexity, and enhance overall code quality.
2. **5-Phase Methodology:** Adopt a systematic 5-phase approach for each feature or task, ensuring a structured, quality-driven development process.
   1. **Discovery & Analysis:** Understand the current state of the system, identify requirements, and define the scope of the main task.
   2. **Task Planning:** Break down tasks into smaller, manageable units to ensure efficient progress.
   3. **Implementation:** Execute changes with engineering-grade quality, focusing on unified patterns and professional electrical design standards.
   4. **Verification:** Ensure all requirements are met through comprehensive testing and compliance verification.
   5. **Documentation & Handover:** Prepare comprehensive documentation and create a handover package for future development and AI agent transfer.
3. **Unified Patterns:** Apply consistent "unified patterns" for calculations, service layers, and repositories, using decorators for error handling, performance monitoring, and memory optimization.
   - [Link to Unified Patterns Documentation](./docs/developer-handbooks/040-unified-patterns.md)
4. **Quality & Standards Focus:** Ensure immaculate attention to detail, adhere to professional electrical design standards (IEEE/IEC/EN), complete type safety with MyPy validation, and comprehensive testing (including real database connections).
5. **Key Success Metrics:** Define success through high unified patterns compliance (≥90%), extensive test coverage (≥85%), 100% test pass rates, and zero remaining placeholder implementations.
6. **Practical Tools & Guidance:** Utilize the provided task planning template, quality assurance checklist, troubleshooting guide, and AI agent handover package to ensure a smooth and successful implementation process.
   1. [Task Planning Template](./docs/004-methodology-template.md)
   2. [Quality Assurance Checklist](./docs/003-implementation-methodology.md#quality-assurance-checklist)
   3. [Troubleshooting Guide](./docs/003-implementation-methodology.md#troubleshooting-guide)