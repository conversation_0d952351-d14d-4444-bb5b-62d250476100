{"meta": {"format": 3, "version": "7.8.0", "timestamp": "2025-07-08T11:32:22.532134", "branch_coverage": true, "show_contexts": false}, "files": {"D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\__init__.py": {"executed_lines": [1, 10, 15, 34, 41, 52, 58, 73, 82, 93, 106], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 10, 15, 34, 41, 52, 58, 73, 82, 93, 106], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 10, 15, 34, 41, 52, 58, 73, 82, 93, 106], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\calculation_enums.py": {"executed_lines": [1, 11, 13, 14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 39, 40, 41, 42, 43, 44, 46, 47, 50, 51, 52, 53, 54, 55, 56], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 11, 13, 14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 39, 40, 41, 42, 43, 44, 46, 47, 50, 51, 52, 53, 54, 55, 56], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"CalculationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CalculationStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "OptimizationObjective": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 11, 13, 14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 39, 40, 41, 42, 43, 44, 46, 47, 50, 51, 52, 53, 54, 55, 56], "summary": {"covered_lines": 33, "num_statements": 33, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\common_enums.py": {"executed_lines": [1, 10, 12, 13, 16, 17, 18, 19, 20, 22, 23, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 68, 69, 70, 71, 74, 75, 76, 77, 78, 81, 82, 83, 86, 87, 90, 91, 92, 95, 96], "summary": {"covered_lines": 59, "num_statements": 59, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 10, 12, 13, 16, 17, 18, 19, 20, 22, 23, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 68, 69, 70, 71, 74, 75, 76, 77, 78, 81, 82, 83, 86, 87, 90, 91, 92, 95, 96], "summary": {"covered_lines": 59, "num_statements": 59, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"InstallationEnvironment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnitOfMeasure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 10, 12, 13, 16, 17, 18, 19, 20, 22, 23, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 68, 69, 70, 71, 74, 75, 76, 77, 78, 81, 82, 83, 86, 87, 90, 91, 92, 95, 96], "summary": {"covered_lines": 59, "num_statements": 59, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\data_io_enums.py": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 92, 93, 96, 97, 98, 99, 100, 101, 103, 104, 107, 108, 109, 110, 111, 112, 113, 115, 116, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 141, 142, 145, 146, 147, 148, 149, 150, 152, 153, 156, 157, 158, 159, 160, 161, 162, 164, 165, 168, 169, 170, 171, 172, 173, 175, 176, 179, 180, 181, 182, 184, 185, 188, 189, 190, 191, 192, 193, 194], "summary": {"covered_lines": 135, "num_statements": 135, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 92, 93, 96, 97, 98, 99, 100, 101, 103, 104, 107, 108, 109, 110, 111, 112, 113, 115, 116, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 141, 142, 145, 146, 147, 148, 149, 150, 152, 153, 156, 157, 158, 159, 160, 161, 162, 164, 165, 168, 169, 170, 171, 172, 173, 175, 176, 179, 180, 181, 182, 184, 185, 188, 189, 190, 191, 192, 193, 194], "summary": {"covered_lines": 135, "num_statements": 135, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"FileFormat": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MappingDataType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DomainDataType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ImportStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ParseStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MappingType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ReportDocumentType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TemplateCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "RenderingEngine": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ReportStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ReportPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TrendPeriod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 92, 93, 96, 97, 98, 99, 100, 101, 103, 104, 107, 108, 109, 110, 111, 112, 113, 115, 116, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 141, 142, 145, 146, 147, 148, 149, 150, 152, 153, 156, 157, 158, 159, 160, 161, 162, 164, 165, 168, 169, 170, 171, 172, 173, 175, 176, 179, 180, 181, 182, 184, 185, 188, 189, 190, 191, 192, 193, 194], "summary": {"covered_lines": 135, "num_statements": 135, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\electrical_enums.py": {"executed_lines": [1, 11, 13, 14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 81, 82, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 120, 121, 122, 123, 124, 125, 126, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 143, 144, 145, 146, 149, 150, 151, 152, 153, 154, 157, 158, 159, 160, 163, 164, 167, 301, 302, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 324, 325, 328, 329, 330, 331, 332, 333, 334, 336, 337, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 352, 353, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 374, 375, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 395, 396, 401, 402, 403, 404, 405, 407, 408, 411, 412, 413, 414, 415, 416, 417, 418, 420, 421, 424, 425, 426, 427, 428, 429, 431, 432, 435, 436, 437, 439, 440, 443, 444, 445, 446, 447, 449, 450, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 467, 468, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 489, 490, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 505, 506, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521], "summary": {"covered_lines": 281, "num_statements": 281, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 11, 13, 14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 81, 82, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 120, 121, 122, 123, 124, 125, 126, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 143, 144, 145, 146, 149, 150, 151, 152, 153, 154, 157, 158, 159, 160, 163, 164, 167, 301, 302, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 324, 325, 328, 329, 330, 331, 332, 333, 334, 336, 337, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 352, 353, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 374, 375, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 395, 396, 401, 402, 403, 404, 405, 407, 408, 411, 412, 413, 414, 415, 416, 417, 418, 420, 421, 424, 425, 426, 427, 428, 429, 431, 432, 435, 436, 437, 439, 440, 443, 444, 445, 446, 447, 449, 450, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 467, 468, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 489, 490, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 505, 506, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521], "summary": {"covered_lines": 281, "num_statements": 281, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ComponentCategoryType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComponentType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComponentFunctionalCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ElectricalNodeType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CableInstallationMethod": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CircuitType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LoadType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "LoadCriticality": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ElectricalCableType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ElectricalInsulationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ConductorMaterial": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "VoltageLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CableSelectionCriteria": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProtectionDeviceType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SwitchboardFunction": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "FeederType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 11, 13, 14, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 79, 80, 81, 82, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 120, 121, 122, 123, 124, 125, 126, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 143, 144, 145, 146, 149, 150, 151, 152, 153, 154, 157, 158, 159, 160, 163, 164, 167, 301, 302, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 324, 325, 328, 329, 330, 331, 332, 333, 334, 336, 337, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 352, 353, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 374, 375, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 395, 396, 401, 402, 403, 404, 405, 407, 408, 411, 412, 413, 414, 415, 416, 417, 418, 420, 421, 424, 425, 426, 427, 428, 429, 431, 432, 435, 436, 437, 439, 440, 443, 444, 445, 446, 447, 449, 450, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 467, 468, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 489, 490, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 505, 506, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521], "summary": {"covered_lines": 281, "num_statements": 281, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\heat_tracing_enums.py": {"executed_lines": [1, 10, 13, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 32, 33, 34, 35, 36, 38, 39, 42, 43, 44, 45, 46, 47, 49, 50, 55, 56, 57, 58, 59], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 10, 13, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 32, 33, 34, 35, 36, 38, 39, 42, 43, 44, 45, 46, 47, 49, 50, 55, 56, 57, 58, 59], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"HeatingMethodType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "HTCircuitApplicationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "HTSensorType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "HeatTracingCableCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 10, 13, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 32, 33, 34, 35, 36, 38, 39, 42, 43, 44, 45, 46, 47, 49, 50, 55, 56, 57, 58, 59], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\mechanical_enums.py": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 66, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 98, 99, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 114, 115, 118, 119, 120, 121, 122, 123, 124], "summary": {"covered_lines": 82, "num_statements": 82, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 66, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 98, 99, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 114, 115, 118, 119, 120, 121, 122, 123, 124], "summary": {"covered_lines": 82, "num_statements": 82, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"PipeMaterialType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ThermalInsulationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TankType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SupportType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TankAccessoryType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PipeSchedule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ValveType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SoilType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 66, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 98, 99, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 114, 115, 118, 119, 120, 121, 122, 123, 124], "summary": {"covered_lines": 82, "num_statements": 82, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\project_management_enums.py": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 46, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 64, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 95, 96, 99, 100, 101, 102, 103, 104, 106, 107, 110, 111, 112, 113, 114, 115, 116, 117, 119, 120, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 134, 135, 138, 139, 140, 141, 142, 143, 144, 145, 147, 148, 151, 152, 153, 154, 155, 156], "summary": {"covered_lines": 110, "num_statements": 110, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 46, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 64, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 95, 96, 99, 100, 101, 102, 103, 104, 106, 107, 110, 111, 112, 113, 114, 115, 116, 117, 119, 120, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 134, 135, 138, 139, 140, 141, 142, 143, 144, 145, 147, 148, 151, 152, 153, 154, 155, 156], "summary": {"covered_lines": 110, "num_statements": 110, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"EventType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EntityType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProjectStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UserRole": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BOMStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BOMItemStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProcurementStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TaskStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MilestoneStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 45, 46, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 64, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 95, 96, 99, 100, 101, 102, 103, 104, 106, 107, 110, 111, 112, 113, 114, 115, 116, 117, 119, 120, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 134, 135, 138, 139, 140, 141, 142, 143, 144, 145, 147, 148, 151, 152, 153, 154, 155, 156], "summary": {"covered_lines": 110, "num_statements": 110, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\standards_enums.py": {"executed_lines": [1, 12, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 52, 53, 54, 55, 56, 57, 59, 60, 63, 64, 65, 66, 67, 68, 69, 71, 72, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93], "summary": {"covered_lines": 58, "num_statements": 58, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 12, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 52, 53, 54, 55, 56, 57, 59, 60, 63, 64, 65, 66, 67, 68, 69, 71, 72, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93], "summary": {"covered_lines": 58, "num_statements": 58, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"TemperatureClass": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "EngineeringStandard": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComplianceLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ATEXZone": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ATEXGasGroup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ATEXProtectionConcept": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 12, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 52, 53, 54, 55, 56, 57, 59, 60, 63, 64, 65, 66, 67, 68, 69, 71, 72, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93], "summary": {"covered_lines": 58, "num_statements": 58, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\enums\\system_enums.py": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 23, 24, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 72, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 103, 104, 105, 106, 107, 109, 110, 113, 114, 115, 116, 117], "summary": {"covered_lines": 75, "num_statements": 75, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 23, 24, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 72, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 103, 104, 105, 106, 107, 109, 110, 113, 114, 115, 116, 117], "summary": {"covered_lines": 75, "num_statements": 75, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ErrorSeverity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ErrorContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MonitoringContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MetricType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ValidationSeverity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ValidationType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ValidationResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SecurityLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 11, 13, 14, 17, 18, 19, 20, 21, 23, 24, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 72, 75, 76, 77, 78, 80, 81, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 103, 104, 105, 106, 107, 109, 110, 113, 114, 115, 116, 117], "summary": {"covered_lines": 75, "num_statements": 75, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\errors\\exceptions.py": {"executed_lines": [2, 14, 17, 18, 20, 28, 29, 30, 31, 32, 33, 37, 38, 43, 46, 47, 49, 59, 60, 66, 76, 77, 79, 89, 90, 92, 102, 103, 105, 120, 121, 123, 139, 140, 142, 152, 153, 155, 156, 165, 166, 169, 173, 174, 176, 197, 198, 200, 215, 216, 218, 233, 234, 236, 252, 253, 255, 270, 271, 273], "summary": {"covered_lines": 42, "num_statements": 62, "percent_covered": 63.63636363636363, "percent_covered_display": "63.64", "missing_lines": 20, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [50, 67, 80, 93, 106, 117, 124, 136, 143, 170, 183, 184, 185, 186, 188, 206, 224, 243, 261, 279], "excluded_lines": [], "executed_branches": [], "missing_branches": [[183, 184], [183, 185], [185, 186], [185, 188]], "functions": {"BaseApplicationException.__init__": {"executed_lines": [28, 29, 30, 31, 32, 33], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProjectNotFoundError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [50], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DataValidationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [67], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InvalidInputError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [80], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ServiceError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [93], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DuplicateEntryError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [106, 117], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DatabaseError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [124, 136], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComponentNotFoundError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [143], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CalculationError.__init__": {"executed_lines": [156], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "StandardComplianceError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [170], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BusinessLogicError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [183, 184, 185, 186, 188], "excluded_lines": [], "executed_branches": [], "missing_branches": [[183, 184], [183, 185], [185, 186], [185, 188]]}, "UtilityError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [206], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MiddlewareError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [224], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "APIError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [243], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SecurityError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [261], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ValidationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [279], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [2, 14, 17, 18, 20, 37, 38, 43, 46, 47, 49, 59, 60, 66, 76, 77, 79, 89, 90, 92, 102, 103, 105, 120, 121, 123, 139, 140, 142, 152, 153, 155, 165, 166, 169, 173, 174, 176, 197, 198, 200, 215, 216, 218, 233, 234, 236, 252, 253, 255, 270, 271, 273], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"BaseApplicationException": {"executed_lines": [28, 29, 30, 31, 32, 33], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "NotFoundError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ProjectNotFoundError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [50], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DataValidationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [67], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "InvalidInputError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [80], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ServiceError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [93], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DuplicateEntryError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [106, 117], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "DatabaseError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [124, 136], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ComponentNotFoundError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [143], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "CalculationError": {"executed_lines": [156], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "StandardComplianceError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [170], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BusinessLogicError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [183, 184, 185, 186, 188], "excluded_lines": [], "executed_branches": [], "missing_branches": [[183, 184], [183, 185], [185, 186], [185, 188]]}, "UtilityError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [206], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "MiddlewareError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [224], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "APIError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [243], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "SecurityError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [261], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ValidationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [279], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [2, 14, 17, 18, 20, 37, 38, 43, 46, 47, 49, 59, 60, 66, 76, 77, 79, 89, 90, 92, 102, 103, 105, 120, 121, 123, 139, 140, 142, 152, 153, 155, 165, 166, 169, 173, 174, 176, 197, 198, 200, 215, 216, 218, 233, 234, 236, 252, 253, 255, 270, 271, 273], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\errors\\unified_error_handler.py": {"executed_lines": [2, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 33, 47], "summary": {"covered_lines": 13, "num_statements": 262, "percent_covered": 4.012345679012346, "percent_covered_display": "4.01", "missing_lines": 249, "excluded_lines": 0, "num_branches": 62, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 62}, "missing_lines": [48, 49, 51, 55, 56, 59, 60, 61, 62, 63, 65, 67, 68, 70, 72, 78, 83, 90, 91, 92, 93, 96, 97, 98, 100, 102, 121, 122, 124, 126, 127, 130, 131, 134, 135, 138, 139, 143, 148, 149, 152, 153, 156, 158, 160, 162, 165, 167, 176, 177, 181, 183, 197, 198, 201, 202, 203, 205, 218, 226, 238, 253, 262, 263, 267, 268, 273, 279, 283, 291, 295, 299, 307, 308, 309, 313, 329, 342, 348, 360, 372, 380, 382, 395, 403, 404, 407, 408, 409, 412, 425, 426, 428, 429, 442, 443, 445, 446, 447, 448, 450, 455, 457, 458, 465, 466, 467, 475, 483, 485, 486, 487, 488, 491, 493, 495, 498, 506, 508, 509, 510, 511, 512, 513, 514, 523, 524, 526, 530, 532, 534, 542, 544, 545, 546, 557, 558, 559, 560, 569, 570, 571, 573, 574, 578, 579, 584, 589, 593, 595, 597, 606, 607, 608, 609, 610, 612, 614, 615, 617, 626, 628, 632, 634, 636, 648, 649, 650, 651, 652, 653, 662, 663, 664, 666, 672, 677, 679, 681, 690, 691, 692, 693, 694, 695, 697, 709, 710, 711, 712, 713, 714, 716, 726, 728, 730, 739, 740, 741, 742, 743, 744, 753, 754, 756, 760, 762, 764, 773, 774, 775, 776, 777, 778, 787, 792, 794, 796, 805, 806, 807, 808, 809, 810, 812, 824, 825, 834, 835, 845, 855, 857, 859, 868, 869, 870, 871, 872, 873, 875, 887, 888, 898, 908, 910, 912, 932, 940, 953], "excluded_lines": [], "executed_branches": [], "missing_branches": [[67, -65], [67, 68], [126, 127], [126, 130], [130, 131], [130, 134], [134, 135], [134, 138], [138, 139], [138, 143], [148, 149], [148, 152], [152, 153], [152, 158], [177, 181], [177, 183], [198, 201], [198, 202], [202, 203], [202, 205], [262, 263], [262, 267], [267, 268], [267, 273], [308, 309], [308, 313], [408, 409], [408, 412], [425, -395], [425, 426], [466, 467], [466, 475], [523, 524], [523, 526], [569, 570], [569, 571], [571, 573], [571, 589], [573, 574], [573, 578], [578, 579], [578, 584], [614, 615], [614, 617], [662, 663], [662, 664], [664, 666], [664, 672], [709, 710], [709, 711], [711, 712], [711, 713], [713, 714], [713, 716], [753, 754], [753, 756], [824, 825], [824, 834], [834, 835], [834, 845], [887, 888], [887, 898]], "functions": {"ErrorHandlingResult.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [67, 68], "excluded_lines": [], "executed_branches": [], "missing_branches": [[67, -65], [67, 68]]}, "ErrorHandlingResult.to_json_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [72], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnifiedErrorHandler.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [90, 91, 92, 93, 96, 97, 98, 100], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnifiedErrorHandler.handle_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 21, "excluded_lines": 0, "num_branches": 12, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 12}, "missing_lines": [121, 122, 124, 126, 127, 130, 131, 134, 135, 138, 139, 143, 148, 149, 152, 153, 156, 158, 160, 162, 165], "excluded_lines": [], "executed_branches": [], "missing_branches": [[126, 127], [126, 130], [130, 131], [130, 134], [134, 135], [134, 138], [138, 139], [138, 143], [148, 149], [148, 152], [152, 153], [152, 158]]}, "UnifiedErrorHandler._handle_application_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 10, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [176, 177, 181, 183, 197, 198, 201, 202, 203, 205], "excluded_lines": [], "executed_branches": [], "missing_branches": [[177, 181], [177, 183], [198, 201], [198, 202], [202, 203], [202, 205]]}, "UnifiedErrorHandler._handle_http_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [226, 238], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnifiedErrorHandler._handle_database_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [262, 263, 267, 268, 273, 279], "excluded_lines": [], "executed_branches": [], "missing_branches": [[262, 263], [262, 267], [267, 268], [267, 273]]}, "UnifiedErrorHandler._handle_validation_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [291, 295], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnifiedErrorHandler._handle_generic_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [307, 308, 309, 313, 329], "excluded_lines": [], "executed_branches": [], "missing_branches": [[308, 309], [308, 313]]}, "UnifiedErrorHandler._create_fallback_error_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [348, 360], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnifiedErrorHandler._log_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [380, 382], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnifiedErrorHandler._track_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 8, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [403, 404, 407, 408, 409, 412, 425, 426], "excluded_lines": [], "executed_branches": [], "missing_branches": [[408, 409], [408, 412], [425, -395], [425, 426]]}, "UnifiedErrorHandler.error_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [442, 443, 445, 446, 447, 448, 450], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "UnifiedErrorHandler.get_error_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 6, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [457, 458, 465, 466, 467, 475], "excluded_lines": [], "executed_branches": [], "missing_branches": [[466, 467], [466, 475]]}, "UnifiedErrorHandler.clear_error_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [485, 486, 487, 488], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "get_unified_error_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [495], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_service_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [506, 508, 509, 532], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_service_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [510, 530], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_service_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [511, 512, 513, 514, 523, 524, 526], "excluded_lines": [], "executed_branches": [], "missing_branches": [[523, 524], [523, 526]]}, "handle_repository_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 4, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [542, 544, 545, 595], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_repository_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [546, 593], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_repository_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 13, "excluded_lines": 0, "num_branches": 8, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 8}, "missing_lines": [557, 558, 559, 560, 569, 570, 571, 573, 574, 578, 579, 584, 589], "excluded_lines": [], "executed_branches": [], "missing_branches": [[569, 570], [569, 571], [571, 573], [571, 589], [573, 574], [573, 578], [578, 579], [578, 584]]}, "handle_calculation_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [606, 634], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_calculation_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [607, 632], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_calculation_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [608, 609, 610, 612, 614, 615, 617, 626, 628], "excluded_lines": [], "executed_branches": [], "missing_branches": [[614, 615], [614, 617]]}, "handle_database_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [648, 679], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_database_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [649, 677], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_database_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [650, 651, 652, 653, 662, 663, 664, 666, 672], "excluded_lines": [], "executed_branches": [], "missing_branches": [[662, 663], [662, 664], [664, 666], [664, 672]]}, "handle_api_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [690, 691, 728], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_api_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [692, 726], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_api_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 11, "excluded_lines": 0, "num_branches": 6, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 6}, "missing_lines": [693, 694, 695, 697, 709, 710, 711, 712, 713, 714, 716], "excluded_lines": [], "executed_branches": [], "missing_branches": [[709, 710], [709, 711], [711, 712], [711, 713], [713, 714], [713, 716]]}, "handle_validation_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [739, 762], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_validation_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [740, 760], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_validation_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [741, 742, 743, 744, 753, 754, 756], "excluded_lines": [], "executed_branches": [], "missing_branches": [[753, 754], [753, 756]]}, "handle_security_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [773, 794], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_security_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [774, 792], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_security_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 5, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [775, 776, 777, 778, 787], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_utility_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [805, 806, 857], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_utility_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [807, 855], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_utility_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 9, "excluded_lines": 0, "num_branches": 4, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 4}, "missing_lines": [808, 809, 810, 812, 824, 825, 834, 835, 845], "excluded_lines": [], "executed_branches": [], "missing_branches": [[824, 825], [824, 834], [834, 835], [834, 845]]}, "handle_middleware_errors": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [868, 869, 910], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_middleware_errors.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 2, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [870, 908], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "handle_middleware_errors.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 7, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [871, 872, 873, 875, 887, 888, 898], "excluded_lines": [], "executed_branches": [], "missing_branches": [[887, 888], [887, 898]]}, "create_error_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [932], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "log_error_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 1, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [953], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [2, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 33, 47], "summary": {"covered_lines": 13, "num_statements": 53, "percent_covered": 24.528301886792452, "percent_covered_display": "24.53", "missing_lines": 40, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [48, 49, 51, 55, 56, 59, 60, 61, 62, 63, 65, 70, 78, 83, 102, 167, 218, 253, 283, 299, 342, 372, 395, 428, 429, 455, 483, 491, 493, 498, 534, 597, 636, 681, 730, 764, 796, 859, 912, 940], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ErrorHandlingResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 3, "excluded_lines": 0, "num_branches": 2, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 2}, "missing_lines": [67, 68, 72], "excluded_lines": [], "executed_branches": [], "missing_branches": [[67, -65], [67, 68]]}, "UnifiedErrorHandler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 83, "percent_covered": 0.0, "percent_covered_display": "0.00", "missing_lines": 83, "excluded_lines": 0, "num_branches": 30, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 30}, "missing_lines": [90, 91, 92, 93, 96, 97, 98, 100, 121, 122, 124, 126, 127, 130, 131, 134, 135, 138, 139, 143, 148, 149, 152, 153, 156, 158, 160, 162, 165, 176, 177, 181, 183, 197, 198, 201, 202, 203, 205, 226, 238, 262, 263, 267, 268, 273, 279, 291, 295, 307, 308, 309, 313, 329, 348, 360, 380, 382, 403, 404, 407, 408, 409, 412, 425, 426, 442, 443, 445, 446, 447, 448, 450, 457, 458, 465, 466, 467, 475, 485, 486, 487, 488], "excluded_lines": [], "executed_branches": [], "missing_branches": [[126, 127], [126, 130], [130, 131], [130, 134], [134, 135], [134, 138], [138, 139], [138, 143], [148, 149], [148, 152], [152, 153], [152, 158], [177, 181], [177, 183], [198, 201], [198, 202], [202, 203], [202, 205], [262, 263], [262, 267], [267, 268], [267, 273], [308, 309], [308, 313], [408, 409], [408, 412], [425, -395], [425, 426], [466, 467], [466, 475]]}, "": {"executed_lines": [2, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 33, 47], "summary": {"covered_lines": 13, "num_statements": 176, "percent_covered": 6.310679611650485, "percent_covered_display": "6.31", "missing_lines": 163, "excluded_lines": 0, "num_branches": 30, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 30}, "missing_lines": [48, 49, 51, 55, 56, 59, 60, 61, 62, 63, 65, 70, 78, 83, 102, 167, 218, 253, 283, 299, 342, 372, 395, 428, 429, 455, 483, 491, 493, 495, 498, 506, 508, 509, 510, 511, 512, 513, 514, 523, 524, 526, 530, 532, 534, 542, 544, 545, 546, 557, 558, 559, 560, 569, 570, 571, 573, 574, 578, 579, 584, 589, 593, 595, 597, 606, 607, 608, 609, 610, 612, 614, 615, 617, 626, 628, 632, 634, 636, 648, 649, 650, 651, 652, 653, 662, 663, 664, 666, 672, 677, 679, 681, 690, 691, 692, 693, 694, 695, 697, 709, 710, 711, 712, 713, 714, 716, 726, 728, 730, 739, 740, 741, 742, 743, 744, 753, 754, 756, 760, 762, 764, 773, 774, 775, 776, 777, 778, 787, 792, 794, 796, 805, 806, 807, 808, 809, 810, 812, 824, 825, 834, 835, 845, 855, 857, 859, 868, 869, 870, 871, 872, 873, 875, 887, 888, 898, 908, 910, 912, 932, 940, 953], "excluded_lines": [], "executed_branches": [], "missing_branches": [[523, 524], [523, 526], [569, 570], [569, 571], [571, 573], [571, 589], [573, 574], [573, 578], [578, 579], [578, 584], [614, 615], [614, 617], [662, 663], [662, 664], [664, 666], [664, 672], [709, 710], [709, 711], [711, 712], [711, 713], [713, 714], [713, 716], [753, 754], [753, 756], [824, 825], [824, 834], [834, 835], [834, 845], [887, 888], [887, 898]]}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\schemas\\__init__.py": {"executed_lines": [1, 17, 22, 23], "summary": {"covered_lines": 3, "num_statements": 17, "percent_covered": 17.647058823529413, "percent_covered_display": "17.65", "missing_lines": 14, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [29, 39, 46, 51, 58, 67, 72, 81, 82, 91, 98, 103, 109, 119], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 17, 22, 23], "summary": {"covered_lines": 3, "num_statements": 17, "percent_covered": 17.647058823529413, "percent_covered_display": "17.65", "missing_lines": 14, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [29, 39, 46, 51, 58, 67, 72, 81, 82, 91, 98, 103, 109, 119], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"": {"executed_lines": [1, 17, 22, 23], "summary": {"covered_lines": 3, "num_statements": 17, "percent_covered": 17.647058823529413, "percent_covered_display": "17.65", "missing_lines": 14, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [29, 39, 46, 51, 58, 67, 72, 81, 82, 91, 98, 103, 109, 119], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\schemas\\base.py": {"executed_lines": [1, 8, 9, 10, 13, 14, 16, 24, 25, 27, 28, 31, 32, 34, 35, 38, 39, 41, 42, 43, 44, 47, 48, 50, 51, 54], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 13, 14, 16, 24, 25, 27, 28, 31, 32, 34, 35, 38, 39, 41, 42, 43, 44, 47, 48, 50, 51, 54], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"BaseSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "TimestampMixin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "BaseSoftDeleteSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PaginationSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "PaginatedResponseSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 8, 9, 10, 13, 14, 16, 24, 25, 27, 28, 31, 32, 34, 35, 38, 39, 41, 42, 43, 44, 47, 48, 50, 51, 54], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}, "D:\\Projects\\zzz_ultimate-electrical-designer\\backend\\core\\schemas\\general\\activity_log_schemas.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 17, 18, 20, 21, 22, 23, 24, 27, 28, 31, 32, 34, 37, 38, 40, 42, 43, 45, 48, 49, 56, 57, 58, 61, 62, 65, 68, 69, 70, 71, 74, 77, 78, 79, 81, 82, 83, 86, 87, 89, 92, 93, 95, 96, 97, 98, 100, 101, 103, 106, 107, 109, 112, 113, 114, 118, 119, 121, 124], "summary": {"covered_lines": 53, "num_statements": 53, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 12, 13, 17, 18, 20, 21, 22, 23, 24, 27, 28, 31, 32, 34, 37, 38, 40, 42, 43, 45, 48, 49, 56, 57, 58, 61, 62, 65, 68, 69, 70, 71, 74, 77, 78, 79, 81, 82, 83, 86, 87, 89, 92, 93, 95, 96, 97, 98, 100, 101, 103, 106, 107, 109, 112, 113, 114, 118, 119, 121, 124], "summary": {"covered_lines": 53, "num_statements": 53, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}, "classes": {"ActivityLogBaseSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogCreateSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogReadSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogReadSchema.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogFilterSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogFilterSchema.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogPaginatedResponseSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogSummarySchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogSummarySchema.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogUpdateSchema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "ActivityLogUpdateSchema.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 17, 18, 20, 21, 22, 23, 24, 27, 28, 31, 32, 34, 37, 38, 40, 42, 43, 45, 48, 49, 56, 57, 58, 61, 62, 65, 68, 69, 70, 71, 74, 77, 78, 79, 81, 82, 83, 86, 87, 89, 92, 93, 95, 96, 97, 98, 100, 101, 103, 106, 107, 109, 112, 113, 114, 118, 119, 121, 124], "summary": {"covered_lines": 53, "num_statements": 53, "percent_covered": 100.0, "percent_covered_display": "100.00", "missing_lines": 0, "excluded_lines": 0, "num_branches": 0, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 0}, "missing_lines": [], "excluded_lines": [], "executed_branches": [], "missing_branches": []}}}}, "totals": {"covered_lines": 1004, "num_statements": 1287, "percent_covered": 74.20546932742054, "percent_covered_display": "74.21", "missing_lines": 283, "excluded_lines": 0, "num_branches": 66, "num_partial_branches": 0, "covered_branches": 0, "missing_branches": 66}}