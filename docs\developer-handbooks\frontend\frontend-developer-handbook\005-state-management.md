## **5. State Management**

Effective state management is critical for building scalable and maintainable frontend applications, especially with React's component-based architecture.

### **5.1. Choosing a State Management Solution**

- **What:** We'll leverage a combination of React's built-in capabilities and dedicated libraries.

  - **React Context API:** For truly global, application-wide state that changes infrequently (e.g., user authentication status, theme settings). It's simple but can lead to performance issues if used for frequently updating state.

  - **Zustand:** For local and global component state that needs to be accessed by various parts of the application without prop drilling. It's a lightweight, performant, and flexible solution, great for complex UI state.

  - **React Query (or similar, like SWR):** **Highly recommended for server-state management.** This is crucial for handling data fetching, caching, synchronization with our FastAPI backend, and managing loading/error states. It effectively separates server state from UI state.

- **Why:** This hybrid approach provides flexibility: React Context/Zustand for UI-specific state, and React Query for efficient, robust handling of asynchronous data from our backend. This minimizes unnecessary re-renders and simplifies data flow.

### **5.2. Defining Global vs. Local State**

- **What:**

  - **Global State (Zustand/Context/React Query):** Data that affects multiple, often distant, components (e.g., current project data, user session, application settings, cached API responses).

  - **Local State (useState/useReducer):** Data specific to a single component or a small, self-contained part of the UI (e.g., form input values, modal open/close status, temporary UI flags).

- **Why:** Proper distinction reduces complexity. Global state should be reserved for genuinely shared data to avoid unnecessary re-renders and make debugging easier. Local state keeps components encapsulated.

### **5.3. Implementing Stores/Contexts (with Zustand/React Query)**

- **What (Zustand Example):** Create small, focused "stores" using Zustand's create function.  
  // src/store/projectStore.ts  
  import { create } from 'zustand';  
  import { ProjectReadSchema } from '@/api/schemas/project'; // Assuming this schema is available  
    
  interface ProjectState {  
  currentProject: ProjectReadSchema \| null;  
  setCurrentProject: (project: ProjectReadSchema \| null) =\> void;  
  // Add other project-related UI state, e.g., editing status, modal visibility  
  isEditingProject: boolean;  
  setIsEditingProject: (status: boolean) =\> void;  
  }  
    
  export const useProjectStore = create\<ProjectState\>((set) =\> ({  
  currentProject: null,  
  setCurrentProject: (project) =\> set({ currentProject: project }),  
  isEditingProject: false,  
  setIsEditingProject: (status) =\> set({ isEditingProject: status }),  
  }));

- **What (React Query Example for fetching project):**  
  // src/hooks/useProjectData.ts  
  import { useQuery } from '@tanstack/react-query';  
  import { projectService } from '@/api/services/projectService'; // Our API service wrapper  
    
  // This hook fetches a project by ID and manages its loading/error state  
  export const useProjectData = (projectId: string) =\> {  
  return useQuery({  
  queryKey: \['project', projectId\], // Unique key for caching  
  queryFn: () =\> projectService.getProjectById(projectId), // Function to fetch data  
  enabled: !!projectId, // Only run query if projectId exists  
  staleTime: 5 \* 60 \* 1000, // Data is considered fresh for 5 minutes  
  // onError: (error) =\> console.error("Failed to fetch project:", error),  
  });  
  };

- **Why:**

  - **Zustand:** Offers a concise API, avoids boilerplate, and ensures components only re-render when the specific slice of state they consume changes.

  - **React Query:** Handles complex async logic (loading, error, caching, background refetching, retries, etc.) automatically, leading to cleaner components that just display data.

### **5.4. Data Fetching and Caching Strategies**

- **What:** We'll primarily use **React Query** for all data fetching from our FastAPI backend.

  - **useQuery:** For GET requests (read operations) that fetch data from the server. Automatically caches, refetches, and provides loading/error states.

  - **useMutation:** For POST, PUT, DELETE requests (write operations). Handles optimistic updates, invalidation, and error handling for mutations.

- **Why:** React Query is purpose-built for managing server state. It drastically reduces boilerplate code for data fetching, improves application performance by intelligently caching data, provides a consistent way to handle loading/error states, and simplifies complex scenarios like optimistic updates. This is crucial for an application like ours that heavily interacts with a backend API.
