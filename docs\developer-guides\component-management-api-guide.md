# Component Management API - Developer Guide

## Quick Start

### 1. Run Database Migration
```bash
cd server
poetry run alembic upgrade head
```

### 2. Import New Dependencies
```python
from src.core.services.dependencies import (
    get_component_category_service,
    get_component_type_service,
)
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryReadSchema,
)
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeReadSchema,
)
```

### 3. Basic Usage Examples

#### Create a Component Category
```python
# API Endpoint
POST /api/v1/component-categories/
{
    "name": "Power Distribution",
    "description": "Power distribution equipment and systems",
    "is_active": true
}

# Service Layer
category_data = ComponentCategoryCreateSchema(
    name="Power Distribution",
    description="Power distribution equipment and systems",
    is_active=True
)
category = category_service.create_category(category_data)
```

#### Create a Component Type
```python
# API Endpoint
POST /api/v1/component-types/
{
    "name": "Switchboard",
    "description": "Main electrical switchboard",
    "category_id": 1,
    "is_active": true,
    "specifications_template": {
        "electrical": {
            "voltage": {"type": "number", "required": true},
            "current": {"type": "number", "required": true}
        }
    }
}

# Service Layer
type_data = ComponentTypeCreateSchema(
    name="Switchboard",
    description="Main electrical switchboard",
    category_id=1,
    is_active=True,
    specifications_template={
        "electrical": {
            "voltage": {"type": "number", "required": True},
            "current": {"type": "number", "required": True}
        }
    }
)
component_type = type_service.create_type(type_data)
```

## API Endpoints Reference

### Component Categories

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/component-categories/` | Create new category |
| GET | `/component-categories/{id}` | Get category by ID |
| PUT | `/component-categories/{id}` | Update category |
| DELETE | `/component-categories/{id}` | Soft delete category |
| GET | `/component-categories/` | List categories with pagination |
| GET | `/component-categories/tree` | Get hierarchical tree |

### Component Types

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/component-types/` | Create new type |
| GET | `/component-types/{id}` | Get type by ID |
| PUT | `/component-types/{id}` | Update type |
| DELETE | `/component-types/{id}` | Soft delete type |
| GET | `/component-types/` | List types with pagination |
| GET | `/component-types/by-category/{id}` | Get types by category |
| PUT | `/component-types/{id}/specifications-template` | Update template |

## Database Schema

### ComponentCategory Table
```sql
CREATE TABLE ComponentCategory (
    id INTEGER PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    parent_category_id INTEGER REFERENCES ComponentCategory(id),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    deleted_at DATETIME,
    deleted_by_user_id INTEGER,
    notes VARCHAR
);
```

### ComponentType Table
```sql
CREATE TABLE ComponentType (
    id INTEGER PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    category_id INTEGER NOT NULL REFERENCES ComponentCategory(id),
    specifications_template TEXT, -- JSON
    metadata TEXT, -- JSON
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    deleted_at DATETIME,
    deleted_by_user_id INTEGER,
    notes VARCHAR
);
```

## Service Layer Usage

### ComponentCategoryService

```python
from src.core.services.dependencies import get_component_category_service

# Dependency injection in FastAPI
async def my_endpoint(
    category_service: ComponentCategoryService = Depends(get_component_category_service)
):
    # Create category
    category = category_service.create_category(category_data)
    
    # Get category
    category = category_service.get_category(category_id)
    
    # Update category
    category = category_service.update_category(category_id, update_data)
    
    # Delete category
    result = category_service.delete_category(category_id, user_id)
    
    # List categories
    categories = category_service.list_categories(search_schema, pagination)
    
    # Get category tree
    tree = category_service.get_category_tree(root_id)
```

### ComponentTypeService

```python
from src.core.services.dependencies import get_component_type_service

# Dependency injection in FastAPI
async def my_endpoint(
    type_service: ComponentTypeService = Depends(get_component_type_service)
):
    # Create type
    component_type = type_service.create_type(type_data)
    
    # Get type
    component_type = type_service.get_type(type_id)
    
    # Update type
    component_type = type_service.update_type(type_id, update_data)
    
    # Delete type
    result = type_service.delete_type(type_id, user_id)
    
    # List types
    types = type_service.list_types(search_schema, pagination)
    
    # Get types by category
    types = type_service.get_types_by_category(category_id)
    
    # Update specifications template
    component_type = type_service.update_specifications_template(type_id, template)
```

## Repository Layer Usage

### Direct Repository Access

```python
from src.core.repositories.repository_dependencies import (
    get_component_category_repository,
    get_component_type_repository,
)

# Category repository
category_repo = get_component_category_repository(db_session)
categories = category_repo.get_root_categories()
category = category_repo.get_by_name("Power Distribution")

# Type repository
type_repo = get_component_type_repository(db_session)
types = type_repo.get_by_category(category_id)
component_type = type_repo.get_by_name("Switchboard", category_id)
```

## Specifications Template Format

### Template Structure
```json
{
    "electrical": {
        "voltage": {
            "type": "number",
            "required": true,
            "unit": "V",
            "min": 0,
            "max": 50000
        },
        "current": {
            "type": "number",
            "required": true,
            "unit": "A",
            "min": 0
        },
        "frequency": {
            "type": "number",
            "unit": "Hz",
            "default": 50
        }
    },
    "thermal": {
        "operating_temp": {
            "type": "range",
            "unit": "°C",
            "min": -40,
            "max": 85
        }
    },
    "mechanical": {
        "dimensions": {
            "type": "object",
            "properties": {
                "width": {"type": "number", "unit": "mm"},
                "height": {"type": "number", "unit": "mm"},
                "depth": {"type": "number", "unit": "mm"}
            }
        }
    },
    "standards_compliance": [
        "IEC 61439-1",
        "IEC 61439-2",
        "AS/NZS 3439.1"
    ]
}
```

### Supported Template Sections
- `electrical`: Electrical specifications
- `thermal`: Thermal specifications  
- `mechanical`: Mechanical specifications
- `environmental`: Environmental specifications
- `standards_compliance`: Applicable standards
- `physical`: Physical specifications
- `performance`: Performance specifications

## Error Handling

### Common Error Responses

```python
# 400 Bad Request - Validation Error
{
    "error": "VALIDATION_ERROR",
    "detail": "Category validation failed: Category name is required",
    "status_code": 400
}

# 404 Not Found - Resource Not Found
{
    "error": "COMPONENT_CATEGORY_NOT_FOUND", 
    "detail": "Component category 999 not found",
    "status_code": 404
}

# 409 Conflict - Business Logic Error
{
    "error": "BUSINESS_LOGIC_ERROR",
    "detail": "Category 'Power Distribution' already exists in this scope",
    "status_code": 409
}
```

### Exception Types
- `ValidationError`: Invalid input data
- `NotFoundError`: Resource not found
- `BusinessLogicError`: Business rule violation
- `DatabaseError`: Database operation failure

## Testing

### Running Tests
```bash
# Run all component management tests
poetry run pytest server/tests/test_component_category.py -v
poetry run pytest server/tests/test_component_type.py -v

# Run with coverage
poetry run pytest server/tests/test_component_category.py --cov=src.core.models.general.component_category
poetry run pytest server/tests/test_component_type.py --cov=src.core.models.general.component_type
```

### Test Examples
```python
def test_create_category(client, auth_headers):
    """Test creating a category via API."""
    category_data = {
        "name": "Test Category",
        "description": "Test description",
        "is_active": True
    }
    
    response = client.post(
        "/api/v1/component-categories/",
        json=category_data,
        headers=auth_headers
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "Test Category"
```

## Migration Guide

### From Enum to Relational Data

1. **Current Enum Usage:**
```python
# Old way (still supported)
component = Component(
    name="Main Switchboard",
    component_type=ComponentType.SWITCHBOARD,
    category=ComponentCategoryType.POWER_DISTRIBUTION
)
```

2. **New Relational Usage:**
```python
# New way (recommended)
component = Component(
    name="Main Switchboard",
    component_type_id=1,  # References ComponentType table
    component_category_id=1  # References ComponentCategory table
)
```

3. **Migration Steps:**
   - Run Alembic migration to create new tables
   - Update application code to use new foreign key relationships
   - Gradually phase out enum usage
   - Remove enum fields after full migration

## Performance Considerations

### Indexing
- All foreign key columns are indexed
- Name and active status combinations are indexed
- Unique constraints prevent duplicates

### Caching
- Consider implementing Redis caching for frequently accessed categories
- Cache category trees for improved performance
- Implement cache invalidation on updates

### Pagination
- All list endpoints support pagination
- Default page size is 20, maximum is 100
- Use pagination for large datasets

## Best Practices

1. **Always validate category existence before creating types**
2. **Use soft delete instead of hard delete**
3. **Implement proper error handling in API consumers**
4. **Cache frequently accessed category trees**
5. **Use specifications templates for consistent component data**
6. **Follow hierarchical organization for categories**
7. **Implement proper authentication and authorization**
8. **Monitor API performance and database queries**
