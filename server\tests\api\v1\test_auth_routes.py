# tests/api/test_auth_routes.py
"""Tests for authentication API endpoints."""

from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch

from fixtures.user_fixtures import test_user, test_inactive_user


class TestAuthRoutes:
    """Test suite for authentication endpoints."""

    def test_login_success(self, client: TestC<PERSON>, test_user):
        """Test successful user login."""
        login_data = {"username": test_user.email, "password": "SecurePass123"}

        response = client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 200
        data = response.json()

        assert "access_token" in data
        assert "token_type" in data
        assert "expires_in" in data
        assert "user" in data

        assert data["token_type"] == "bearer"
        assert isinstance(data["expires_in"], int)
        assert data["user"]["email"] == test_user.email

    def test_login_invalid_credentials(self, client: TestClient, test_user):
        """Test login with invalid credentials."""
        login_data = {"username": test_user.email, "password": "WrongPassword"}

        response = client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 401
        data = response.json()
        assert "detail" in data

    def test_login_nonexistent_user(self, client: TestClient):
        """Test login with nonexistent user."""
        login_data = {"username": "<EMAIL>", "password": "SomePassword"}

        response = client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 401
        data = response.json()
        assert "detail" in data

    def test_login_inactive_user(self, client: TestClient, test_inactive_user):
        """Test login with inactive user."""

        login_data = {"username": test_inactive_user.email, "password": "SecurePass123"}

        response = client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 401

    def test_oauth2_token_endpoint(self, client: TestClient, test_user):
        """Test OAuth2-compatible token endpoint."""
        form_data = {
            "username": test_user.email,
            "password": "SecurePass123",
            "grant_type": "password",
        }

        response = client.post("/api/v1/auth/token", data=form_data)

        assert response.status_code == 200
        data = response.json()

        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"

    def test_logout_authenticated_user(self, client: TestClient, test_user):
        """Test logout for authenticated user."""
        # First login to get token
        login_data = {"username": test_user.email, "password": "SecurePass123"}
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]

        # Then logout
        headers = {"Authorization": f"Bearer {token}"}
        response = client.post("/api/v1/auth/logout", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert "message" in data
        assert "logged_out_at" in data

    def test_logout_unauthenticated_user(self, client: TestClient):
        """Test logout without authentication."""
        response = client.post("/api/v1/auth/logout")

        assert response.status_code == 401

    def test_refresh_token_authenticated_user(self, client: TestClient, test_user):
        """Test token refresh for authenticated user."""
        # First login to get token
        login_data = {"username": test_user.email, "password": "SecurePass123"}
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]

        # Then refresh token
        headers = {"Authorization": f"Bearer {token}"}
        response = client.post("/api/v1/auth/refresh", headers=headers)

        assert response.status_code == 200
        data = response.json()

        assert "access_token" in data
        assert "token_type" in data
        assert "expires_in" in data
        assert data["token_type"] == "bearer"

        # New token should be valid (may be same as original if generated quickly)
        assert data["access_token"] is not None
        assert len(data["access_token"]) > 0

    def test_refresh_token_unauthenticated_user(self, client: TestClient):
        """Test token refresh without authentication."""
        response = client.post("/api/v1/auth/refresh")

        assert response.status_code == 401

    def test_change_password_authenticated_user(self, client: TestClient, test_user):
        """Test password change for authenticated user."""
        # First login to get token
        login_data = {"username": test_user.email, "password": "SecurePass123"}
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]

        # Change password
        password_data = {
            "current_password": "SecurePass123",
            "new_password": "NewSecurePass456",
            "confirm_password": "NewSecurePass456",
        }

        headers = {"Authorization": f"Bearer {token}"}
        response = client.post(
            "/api/v1/auth/change-password", json=password_data, headers=headers
        )

        assert response.status_code == 200
        data = response.json()

        assert "message" in data
        assert "changed_at" in data

    def test_change_password_mismatched_passwords(self, client: TestClient, test_user):
        """Test password change with mismatched passwords."""
        # First login to get token
        login_data = {"username": test_user.email, "password": "SecurePass123"}
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]

        # Try to change password with mismatched confirmation
        password_data = {
            "current_password": "SecurePass123",
            "new_password": "NewSecurePass456",
            "confirm_password": "DifferentPassword",
        }

        headers = {"Authorization": f"Bearer {token}"}
        response = client.post(
            "/api/v1/auth/change-password", json=password_data, headers=headers
        )

        assert response.status_code == 400

    def test_change_password_unauthenticated_user(self, client: TestClient):
        """Test password change without authentication."""
        password_data = {
            "current_password": "OldPassword",
            "new_password": "NewPassword",
            "confirm_password": "NewPassword",
        }

        response = client.post("/api/v1/auth/change-password", json=password_data)

        assert response.status_code == 401

    def test_login_validation_errors(self, client: TestClient):
        """Test login with validation errors."""
        # Missing password
        login_data = {"username": "<EMAIL>"}

        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 422

        # Missing username
        login_data = {"password": "password"}

        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 422

    def test_auth_endpoints_performance_monitoring(self, client: TestClient, test_user):
        """Test that auth endpoints include performance monitoring."""
        login_data = {"username": test_user.email, "password": "SecurePass123"}

        # Performance monitoring should be transparent to the client
        response = client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 200
        # Performance metrics are logged, not returned in response

    def test_auth_error_handling(self, client: TestClient):
        """Test authentication error handling."""
        with patch(
            "src.core.services.general.user_service.UserService.login"
        ) as mock_login:
            mock_login.side_effect = Exception("Database error")

            login_data = {"username": "<EMAIL>", "password": "password"}

            response = client.post("/api/v1/auth/login", json=login_data)

            # Should return 401 for security (don't expose internal errors)
            assert response.status_code == 401
            assert response.json()["detail"] == "Invalid email or password"

    def test_token_expiration_format(self, client: TestClient, test_user):
        """Test token expiration format."""
        login_data = {"username": test_user.email, "password": "SecurePass123"}

        response = client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 200
        data = response.json()

        assert isinstance(data["expires_in"], int)
        assert data["expires_in"] > 0

    def test_user_data_in_login_response(self, client: TestClient, test_user):
        """Test user data included in login response."""
        login_data = {"username": test_user.email, "password": "SecurePass123"}

        response = client.post("/api/v1/auth/login", json=login_data)

        assert response.status_code == 200
        data = response.json()

        user_data = data["user"]
        assert "id" in user_data
        assert "name" in user_data
        assert "email" in user_data
        assert "role" in user_data
        assert "is_active" in user_data

        # Should not include sensitive data
        assert "password_hash" not in user_data
