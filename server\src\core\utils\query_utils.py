# src/core/utils/query_utils.py
"""Query Utilities.

This module provides common database query utilities for filtering,
searching, and building complex queries with SQLAlchemy.

Key Features:
- Dynamic query building
- Common filter patterns
- Search functionality
- Query optimization helpers
"""

from datetime import date, datetime
from typing import TYPE_CHECKING, Any, List, TypeVar, Union

from sqlalchemy import and_, func, or_
from sqlalchemy.orm import Query, Session
from sqlalchemy.sql import Select
from sqlalchemy.sql.elements import BinaryExpression

if TYPE_CHECKING:
    from sqlalchemy.orm import DeclarativeBase

from src.config.logging_config import logger

# Type variable for SQLAlchemy models
ModelType = TypeVar("ModelType")

# Type aliases for better readability
QueryType = Query[Any]
SelectType = Select[Any]
QueryOrSelect = Union[QueryType, SelectType]


class QueryBuilder:
    """Helper class for building dynamic SQLAlchemy queries."""

    def __init__(self, session: Session, model: type[ModelType]):
        self.session = session
        self.model = model
        self.query = session.query(model)
        self.filters: List[Any] = []

    def filter_by_field(
        self, field_name: str, value: Any, operator: str = "eq"
    ) -> "QueryBuilder":
        """Add a filter condition for a specific field.

        Args:
            field_name: Name of the field to filter
            value: Value to filter by
            operator: Comparison operator ('eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'like', 'ilike')

        Returns:
            QueryBuilder: Self for method chaining

        """
        if not hasattr(self.model, field_name):
            logger.warning(
                f"Field '{field_name}' not found on model {self.model.__name__}"
            )
            return self

        field = getattr(self.model, field_name)

        if operator == "eq":
            condition = field == value
        elif operator == "ne":
            condition = field != value
        elif operator == "gt":
            condition = field > value
        elif operator == "gte":
            condition = field >= value
        elif operator == "lt":
            condition = field < value
        elif operator == "lte":
            condition = field <= value
        elif operator == "in":
            condition = field.in_(
                value if isinstance(value, (list, tuple)) else [value]
            )
        elif operator == "like":
            condition = field.like(f"%{value}%")
        elif operator == "ilike":
            condition = field.ilike(f"%{value}%")
        else:
            logger.warning(f"Unknown operator: {operator}")
            return self

        self.filters.append(condition)
        return self

    def filter_by_text_search(
        self, search_term: str, fields: list[str], case_sensitive: bool = False
    ) -> "QueryBuilder":
        """Add text search across multiple fields.

        Args:
            search_term: Text to search for
            fields: List of field names to search in
            case_sensitive: Whether search is case sensitive

        Returns:
            QueryBuilder: Self for method chaining

        """
        if not search_term.strip():
            return self

        search_conditions = []

        for field_name in fields:
            if hasattr(self.model, field_name):
                field = getattr(self.model, field_name)
                if case_sensitive:
                    condition = field.contains(search_term)
                else:
                    condition = field.ilike(f"%{search_term}%")
                search_conditions.append(condition)

        if search_conditions:
            self.filters.append(or_(*search_conditions))

        return self

    def filter_by_date_range(
        self,
        field_name: str,
        start_date: date | datetime | None = None,
        end_date: date | datetime | None = None,
    ) -> "QueryBuilder":
        """Add date range filter.

        Args:
            field_name: Name of the date field
            start_date: Start of date range
            end_date: End of date range

        Returns:
            QueryBuilder: Self for method chaining

        """
        if not hasattr(self.model, field_name):
            logger.warning(
                f"Field '{field_name}' not found on model {self.model.__name__}"
            )
            return self

        field = getattr(self.model, field_name)

        if start_date:
            self.filters.append(field >= start_date)
        if end_date:
            self.filters.append(field <= end_date)

        return self

    def filter_by_soft_delete(
        self, include_deleted: bool = False, deleted_field: str = "is_deleted"
    ) -> "QueryBuilder":
        """Add soft delete filter.

        Args:
            include_deleted: Whether to include soft-deleted records
            deleted_field: Name of the soft delete field

        Returns:
            QueryBuilder: Self for method chaining

        """
        if not include_deleted and hasattr(self.model, deleted_field):
            field = getattr(self.model, deleted_field)
            self.filters.append(field == False)

        return self

    def filter_by_range(
        self, field_name: str, min_value: Any, max_value: Any
    ) -> "QueryBuilder":
        """Add a range filter condition.

        Args:
            field_name: Name of the field to filter
            min_value: Minimum value
            max_value: Maximum value

        Returns:
            QueryBuilder: Self for method chaining

        """
        if not hasattr(self.model, field_name):
            logger.warning(
                f"Field '{field_name}' not found on model {self.model.__name__}"
            )
            return self

        field = getattr(self.model, field_name)

        if min_value is not None:
            self.filters.append(field >= min_value)
        if max_value is not None:
            self.filters.append(field <= max_value)

        return self

    def sort_by(self, field_name: str, ascending: bool = True) -> "QueryBuilder":
        """Add sorting to the query.

        Args:
            field_name: Name of the field to sort by
            ascending: Whether to sort in ascending order

        Returns:
            QueryBuilder: Self for method chaining

        """
        if not hasattr(self.model, field_name):
            logger.warning(
                f"Field '{field_name}' not found on model {self.model.__name__}"
            )
            return self

        field = getattr(self.model, field_name)

        if ascending:
            self.query = self.query.order_by(field.asc())
        else:
            self.query = self.query.order_by(field.desc())

        return self

    def paginate(self, page: int, per_page: int) -> "QueryBuilder":
        """Add pagination to the query.

        Args:
            page: Page number (1-based)
            per_page: Number of items per page

        Returns:
            QueryBuilder: Self for method chaining

        """
        offset = (page - 1) * per_page
        self.query = self.query.offset(offset).limit(per_page)
        return self

    def count(self) -> int:
        """Get the count of records matching the current filters.

        Returns:
            int: Number of matching records

        """
        query = self.session.query(self.model)
        if self.filters:
            query = query.filter(and_(*self.filters))
        return query.count()

    def all(self) -> list[Any]:
        """Execute the query and return all results.

        Returns:
            List: All matching records

        """
        return self.build().all()

    def first(self) -> Any:
        """Execute the query and return the first result.

        Returns:
            First matching record or None

        """
        return self.build().first()

    def build(self) -> QueryType:
        """Build the final query with all filters applied.

        Returns:
            Query: SQLAlchemy query with filters applied

        """
        if self.filters:
            self.query = self.query.filter(and_(*self.filters))

        return self.query


def build_search_query(
    session: Session,
    model: type[ModelType],
    search_params: dict[str, Any],
    searchable_fields: list[str] | None = None,
) -> QueryType:
    """Build a search query from parameters.

    Args:
        session: SQLAlchemy session
        model: Model class to query
        search_params: Dictionary of search parameters
        searchable_fields: List of fields that can be searched (None for all text fields)

    Returns:
        Query: Built search query

    """
    builder = QueryBuilder(session, model)

    # Handle general text search
    if "search" in search_params and search_params["search"]:
        if searchable_fields:
            builder.filter_by_text_search(
                search_params["search"],
                searchable_fields,
                search_params.get("case_sensitive", False),
            )

    # Handle specific field filters
    for key, value in search_params.items():
        if key in (
            "search",
            "case_sensitive",
            "include_deleted",
            "page",
            "per_page",
            "sort_by",
            "sort_order",
        ):
            continue

        if value is not None and value != "":
            # Handle range filters (e.g., 'created_at_start', 'created_at_end')
            if key.endswith("_start"):
                field_name = key[:-6]  # Remove '_start'
                builder.filter_by_date_range(field_name, start_date=value)
            elif key.endswith("_end"):
                field_name = key[:-4]  # Remove '_end'
                builder.filter_by_date_range(field_name, end_date=value)
            else:
                # Regular field filter
                builder.filter_by_field(key, value)

    # Handle soft delete
    builder.filter_by_soft_delete(search_params.get("include_deleted", False))

    return builder.build()


def apply_filters_from_dict(
    query: QueryOrSelect,
    model: type[ModelType],
    filters: dict[str, Any],
    allowed_fields: list[str] | None = None,
) -> QueryOrSelect:
    """Apply filters from dictionary to query.

    Args:
        query: Base query
        model: Model class
        filters: Dictionary of filters to apply
        allowed_fields: List of allowed filter fields

    Returns:
        Query with filters applied

    """
    conditions = []

    for field_name, value in filters.items():
        if value is None or value == "":
            continue

        if allowed_fields and field_name not in allowed_fields:
            logger.warning(f"Filter field '{field_name}' not allowed")
            continue

        if not hasattr(model, field_name):
            logger.warning(f"Filter field '{field_name}' not found on model")
            continue

        field = getattr(model, field_name)

        if isinstance(value, (list, tuple)):
            conditions.append(field.in_(value))
        else:
            conditions.append(field == value)

    if conditions:
        if isinstance(query, Select):
            return query.where(and_(*conditions))
        return query.filter(and_(*conditions))

    return query


def get_model_searchable_fields(model: Any) -> list[str]:
    """Get list of searchable text fields from a model.

    Args:
        model: SQLAlchemy model class

    Returns:
        List of field names that are text-searchable

    """
    searchable_fields = [
        column.name
        for column in model.__table__.columns
        if str(column.type).lower() in ("string", "text", "varchar", "char")
    ]

    return searchable_fields


def optimize_query_for_count(query: QueryType) -> Any:
    """Optimize query for counting records.

    Args:
        query: Query to optimize

    Returns:
        Optimized query for counting

    """
    # Remove unnecessary joins and ordering for count queries
    return query.statement.with_only_columns(func.count()).order_by(None)  # type: ignore


def add_full_text_search(
    query: QueryOrSelect,
    model: type[ModelType],
    search_term: str,
    fields: list[str],
    language: str = "english",
) -> QueryOrSelect:
    """Add full-text search capability (PostgreSQL specific).

    Args:
        query: Base query
        model: Model class
        search_term: Text to search for
        fields: List of fields to search in
        language: Language for full-text search

    Returns:
        Query with full-text search applied

    Note:
        This requires PostgreSQL with full-text search setup

    """
    if not search_term.strip():
        return query

    # Build tsvector from specified fields
    tsvector_parts = []
    for field_name in fields:
        if hasattr(model, field_name):
            field = getattr(model, field_name)
            tsvector_parts.append(func.to_tsvector(language, field))

    if not tsvector_parts:
        return query

    # Combine tsvectors
    tsvector: Any
    if len(tsvector_parts) == 1:
        tsvector = tsvector_parts[0]
    else:
        tsvector = tsvector_parts[0]
        for part in tsvector_parts[1:]:
            tsvector = tsvector.op("||")(part)

    # Create search query
    tsquery = func.plainto_tsquery(language, search_term)

    # Apply search condition
    search_condition = tsvector.op("@@")(tsquery)

    if isinstance(query, Select):
        return query.where(search_condition)
    return query.filter(search_condition)


def create_case_insensitive_filter(
    field: Any, value: str, exact_match: bool = False
) -> Any:
    """Create case-insensitive filter condition.

    Args:
        field: SQLAlchemy column
        value: Value to filter by
        exact_match: Whether to match exactly or use LIKE

    Returns:
        BinaryExpression: Filter condition

    """
    if exact_match:
        return func.lower(field) == func.lower(value)
    return func.lower(field).like(func.lower(f"%{value}%"))


def build_dynamic_order_by(
    query: QueryOrSelect,
    model: type[ModelType],
    sort_fields: list[dict[str, str]],
    allowed_fields: list[str] | None = None,
) -> QueryOrSelect:
    """Build dynamic ORDER BY clause from list of sort specifications.

    Args:
        query: Base query
        model: Model class
        sort_fields: List of dicts with 'field' and 'direction' keys
        allowed_fields: List of allowed sort fields

    Returns:
        Query with ordering applied

    Example:
        sort_fields = [
            {'field': 'name', 'direction': 'asc'},
            {'field': 'created_at', 'direction': 'desc'}
        ]

    """
    order_clauses = []

    for sort_spec in sort_fields:
        field_name = sort_spec.get("field")
        direction = sort_spec.get("direction", "asc").lower()

        if not field_name:
            continue

        if allowed_fields and field_name not in allowed_fields:
            logger.warning(f"Sort field '{field_name}' not allowed")
            continue

        if not hasattr(model, field_name):
            logger.warning(f"Sort field '{field_name}' not found on model")
            continue

        field = getattr(model, field_name)

        if direction == "desc":
            order_clauses.append(field.desc())
        else:
            order_clauses.append(field.asc())

    if order_clauses:
        if isinstance(query, Select):
            return query.order_by(*order_clauses)
        return query.order_by(*order_clauses)

    return query
