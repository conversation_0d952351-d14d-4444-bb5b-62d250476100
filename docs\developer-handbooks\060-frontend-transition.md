# 06 - Frontend Transition

**Section:** 06-frontend-transition  
**Version:** 1.0  
**Last Updated:** July 2025  
**Prerequisites:** [Backend Development](050-backend-development.md) completed  
**Estimated Reading Time:** 30 minutes  

## Overview

The Frontend Transition section provides comprehensive guidance for transitioning from backend development to frontend implementation. This section bridges the gap between the robust backend architecture and the upcoming frontend development, ensuring seamless integration and maintaining the project's engineering-grade standards throughout the full-stack implementation.

## Table of Contents

- [06 - Frontend Transition](#06---frontend-transition)
  - [Overview](#overview)
  - [Table of Contents](#table-of-contents)
  - [Frontend Architecture Preparation](#frontend-architecture-preparation)
    - [Technology Stack Overview](#technology-stack-overview)
    - [Frontend Project Structure](#frontend-project-structure)
    - [Backend Analysis Integration](#backend-analysis-integration)
      - [Backend Analysis Guide](#backend-analysis-guide)
      - [Frontend Handbooks](#frontend-handbooks)
  - [Backend-Frontend Integration](#backend-frontend-integration)
    - [API Client Architecture](#api-client-architecture)
      - [Type-Safe API Client](#type-safe-api-client)
      - [React Query Integration](#react-query-integration)
    - [Type System Integration](#type-system-integration)
      - [Backend Type Synchronization](#backend-type-synchronization)
      - [Form Validation Integration](#form-validation-integration)
  - [API Integration Patterns](#api-integration-patterns)
    - [Error Handling Integration](#error-handling-integration)
      - [Frontend Error Handling](#frontend-error-handling)
    - [Authentication Integration](#authentication-integration)
      - [JWT Token Management](#jwt-token-management)

## Frontend Architecture Preparation

### Technology Stack Overview

The frontend implementation will use modern technologies that complement the backend's engineering-grade standards:

```
Frontend Technology Stack:
┌─────────────────────────────────────────────────────────────┐
│                    React 18+ with TypeScript                │
│  • Component-based architecture                             │
│  • Strong type safety with TypeScript                       │
│  • Modern React patterns (hooks, context, suspense)        │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Next.js 15+ Framework                  │
│  • App Router for modern routing                           │
│  • Server-side rendering capabilities                      │
│  • Built-in optimization and performance                   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    State Management                         │
│  • Zustand for global state                               │
│  • React Query for server state                           │
│  • React Hook Form for form state                         │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      UI Framework                           │
│  • Tailwind CSS for styling                               │
│  • Headless UI for accessible components                  │
│  • Framer Motion for animations                           │
└─────────────────────────────────────────────────────────────┘
```

### Frontend Project Structure

The frontend will follow a structured architecture that mirrors the backend's organization:

```
frontend/
├── src/
│   ├── app/                     # Next.js App Router
│   │   ├── (auth)/             # Authentication routes
│   │   ├── (dashboard)/        # Main application routes
│   │   ├── api/                # API route handlers (if needed)
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout
│   │   └── page.tsx            # Home page
│   ├── components/             # Reusable UI components
│   │   ├── ui/                 # Base UI components
│   │   ├── forms/              # Form components
│   │   ├── charts/             # Data visualization
│   │   └── electrical/         # Domain-specific components
│   ├── lib/                    # Utility libraries
│   │   ├── api/                # API client and types
│   │   ├── auth/               # Authentication utilities
│   │   ├── utils/              # General utilities
│   │   └── validations/        # Form validation schemas
│   ├── hooks/                  # Custom React hooks
│   ├── stores/                 # State management
│   ├── types/                  # TypeScript type definitions
│   └── constants/              # Application constants
├── docs/                       # Frontend documentation
├── public/                     # Static assets
├── tests/                      # Test files
└── package.json               # Dependencies and scripts
```

### Backend Analysis Integration

The frontend development leverages comprehensive backend analysis documentation:

#### Backend Analysis Guide
**Location:** `docs/developer-handbooks/frontend-transition/001-backend-analysis-guide.md`  
**Purpose:** Deep understanding of backend architecture for frontend integration

**Key Analysis Areas:**
- API endpoint mapping and usage patterns
- Data flow and state management requirements
- Authentication and authorization integration
- Error handling and user feedback patterns
- Performance optimization opportunities

#### Frontend Handbooks
**Location:** `docs/developer-handbooks/frontend/`  
**Purpose:** Practical guides for frontend development

**Available Guides:**
- [Frontend Typing Handbook](./frontend/frontend-typing-handbook/001-typescript-handbook.md)
  - 001: Handbook Overview
  - 002: Core Typescript Concepts
  - 003: React Components and Props
  - 004: React Hooks
  - 005: State Management
  - 006: Data Fetching & API Interaction
  - 007: Routing
  - 008: Forms & Validation
  - 009: Utility Functions & Advanced Patterns
  - 010: Testing
- [Frontend Developer Handbook](./frontend/frontend-developer-handbook/001-frontend-development-handbook.md)
  - 001: Handbook Overview
  - 002: UI Implementation Guide
  - 003: Linting Guide
  - 004: Documentation Guide
  - 005: State Management
  - 006: Data Fetching & API Interaction
  - 007: Testing
  - 008: Routing & Navigation
  - 009: Performance Optimization
  - 010: Deployment & Environment Setup

## Backend-Frontend Integration

### API Client Architecture

#### Type-Safe API Client
```typescript
// lib/api/client.ts
import { z } from 'zod';

// Import backend schema types
import type {
  ProjectResponse,
  ProjectCreateSchema,
  ProjectUpdateSchema,
  ProjectListResponse
} from '@/types/api';

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  setAuthToken(token: string) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(response.status, errorData.detail || 'Request failed');
    }

    return response.json();
  }

  // Project API methods
  async getProjects(params?: {
    skip?: number;
    limit?: number;
    search?: string;
    status?: string;
  }): Promise<ProjectListResponse> {
    const searchParams = new URLSearchParams();
    if (params?.skip) searchParams.set('skip', params.skip.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);
    if (params?.status) searchParams.set('status', params.status);

    const query = searchParams.toString();
    const endpoint = `/api/v1/projects${query ? `?${query}` : ''}`;
    
    return this.request<ProjectListResponse>(endpoint);
  }

  async getProject(id: number): Promise<ProjectResponse> {
    return this.request<ProjectResponse>(`/api/v1/projects/${id}`);
  }

  async createProject(data: ProjectCreateSchema): Promise<ProjectResponse> {
    return this.request<ProjectResponse>('/api/v1/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProject(
    id: number,
    data: ProjectUpdateSchema
  ): Promise<ProjectResponse> {
    return this.request<ProjectResponse>(`/api/v1/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: number): Promise<void> {
    await this.request<void>(`/api/v1/projects/${id}`, {
      method: 'DELETE',
    });
  }
}

class ApiError extends Error {
  constructor(
    public status: number,
    message: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Export singleton instance
export const apiClient = new ApiClient(
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
);
```

#### React Query Integration
```typescript
// hooks/useProjects.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api/client';
import type { ProjectCreateSchema, ProjectUpdateSchema } from '@/types/api';

export function useProjects(params?: {
  skip?: number;
  limit?: number;
  search?: string;
  status?: string;
}) {
  return useQuery({
    queryKey: ['projects', params],
    queryFn: () => apiClient.getProjects(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useProject(id: number) {
  return useQuery({
    queryKey: ['projects', id],
    queryFn: () => apiClient.getProject(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProjectCreateSchema) => apiClient.createProject(data),
    onSuccess: () => {
      // Invalidate and refetch projects list
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

export function useUpdateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: ProjectUpdateSchema }) =>
      apiClient.updateProject(id, data),
    onSuccess: (updatedProject) => {
      // Update specific project in cache
      queryClient.setQueryData(['projects', updatedProject.id], updatedProject);
      // Invalidate projects list to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

export function useDeleteProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiClient.deleteProject(id),
    onSuccess: () => {
      // Invalidate projects list
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}
```

### Type System Integration

#### Backend Type Synchronization
```typescript
// types/api.ts - Generated from backend schemas
export interface ProjectResponse {
  id: number;
  name: string;
  description?: string;
  voltage_level: number;
  environment_type: 'indoor' | 'outdoor' | 'hazardous';
  ambient_temperature_min: number;
  ambient_temperature_max: number;
  status: 'draft' | 'active' | 'on_hold' | 'completed' | 'cancelled';
  client_name?: string;
  project_location?: string;
  design_standards: string[];
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by?: number;
  temperature_range: number;
  voltage_category: string;
  safety_classification: string;
}

export interface ProjectCreateSchema {
  name: string;
  description?: string;
  voltage_level: number;
  environment_type: 'indoor' | 'outdoor' | 'hazardous';
  ambient_temperature_min: number;
  ambient_temperature_max: number;
  client_name?: string;
  project_location?: string;
  design_standards?: string[];
}

export interface ProjectUpdateSchema {
  name?: string;
  description?: string;
  voltage_level?: number;
  environment_type?: 'indoor' | 'outdoor' | 'hazardous';
  ambient_temperature_min?: number;
  ambient_temperature_max?: number;
  status?: 'draft' | 'active' | 'on_hold' | 'completed' | 'cancelled';
  client_name?: string;
  project_location?: string;
  design_standards?: string[];
}

export interface ProjectListResponse {
  projects: ProjectResponse[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}
```

#### Form Validation Integration
```typescript
// lib/validations/project.ts
import { z } from 'zod';

// Mirror backend validation rules
export const projectCreateSchema = z.object({
  name: z
    .string()
    .min(1, 'Project name is required')
    .max(255, 'Project name must be less than 255 characters'),
  
  description: z
    .string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  
  voltage_level: z
    .number()
    .positive('Voltage level must be positive')
    .max(1000000, 'Voltage level cannot exceed 1 MV'),
  
  environment_type: z.enum(['indoor', 'outdoor', 'hazardous']),
  
  ambient_temperature_min: z
    .number()
    .min(-50, 'Minimum temperature cannot be below -50°C')
    .max(100, 'Minimum temperature cannot exceed 100°C'),
  
  ambient_temperature_max: z
    .number()
    .min(-50, 'Maximum temperature cannot be below -50°C')
    .max(100, 'Maximum temperature cannot exceed 100°C'),
  
  client_name: z
    .string()
    .max(255, 'Client name must be less than 255 characters')
    .optional(),
  
  project_location: z
    .string()
    .max(500, 'Location must be less than 500 characters')
    .optional(),
  
  design_standards: z
    .array(z.string())
    .optional()
    .default([]),
}).refine(
  (data) => data.ambient_temperature_max > data.ambient_temperature_min,
  {
    message: 'Maximum temperature must be greater than minimum temperature',
    path: ['ambient_temperature_max'],
  }
).refine(
  (data) => {
    if (data.environment_type === 'hazardous') {
      return data.design_standards?.includes('IEC-60079') ?? false;
    }
    return true;
  },
  {
    message: 'Hazardous environments must include IEC-60079 standard',
    path: ['design_standards'],
  }
);

export const projectUpdateSchema = projectCreateSchema.partial().extend({
  status: z.enum(['draft', 'active', 'on_hold', 'completed', 'cancelled']).optional(),
});

export type ProjectCreateFormData = z.infer<typeof projectCreateSchema>;
export type ProjectUpdateFormData = z.infer<typeof projectUpdateSchema>;
```

## API Integration Patterns

### Error Handling Integration

#### Frontend Error Handling
```typescript
// lib/errors/api-errors.ts
export class ApiError extends Error {
  constructor(
    public status: number,
    public detail: string,
    public code?: string
  ) {
    super(detail);
    this.name = 'ApiError';
  }

  static fromResponse(status: number, errorData: any): ApiError {
    return new ApiError(
      status,
      errorData.detail || 'An error occurred',
      errorData.code
    );
  }

  get isValidationError(): boolean {
    return this.status === 400;
  }

  get isAuthError(): boolean {
    return this.status === 401 || this.status === 403;
  }

  get isNotFoundError(): boolean {
    return this.status === 404;
  }

  get isServerError(): boolean {
    return this.status >= 500;
  }
}

// hooks/useErrorHandler.ts
import { useCallback } from 'react';
import { toast } from 'sonner';
import { ApiError } from '@/lib/errors/api-errors';

export function useErrorHandler() {
  const handleError = useCallback((error: unknown) => {
    if (error instanceof ApiError) {
      switch (true) {
        case error.isValidationError:
          toast.error('Validation Error', {
            description: error.detail,
          });
          break;
        
        case error.isAuthError:
          toast.error('Authentication Error', {
            description: 'Please log in to continue',
          });
          // Redirect to login
          window.location.href = '/login';
          break;
        
        case error.isNotFoundError:
          toast.error('Not Found', {
            description: 'The requested resource was not found',
          });
          break;
        
        case error.isServerError:
          toast.error('Server Error', {
            description: 'An internal server error occurred. Please try again later.',
          });
          break;
        
        default:
          toast.error('Error', {
            description: error.detail,
          });
      }
    } else {
      toast.error('Unexpected Error', {
        description: 'An unexpected error occurred',
      });
    }
  }, []);

  return { handleError };
}
```

### Authentication Integration

#### JWT Token Management
```typescript
// lib/auth/token-manager.ts
class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static getAccessToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static setAccessToken(token: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.ACCESS_TOKEN_KEY, token);
  }

  static getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setRefreshToken(token: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
  }

  static clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return Date.now() >= payload.exp * 1000;
    } catch {
      return true;
    }
  }
}

// hooks/useAuth.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: number;
  email: string;
  name: string;
  role: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,

      login: async (email: string, password: string) => {
        try {
          const response = await apiClient.login({ email, password });
          
          TokenManager.setAccessToken(response.access_token);
          TokenManager.setRefreshToken(response.refresh_token);
          apiClient.setAuthToken(response.access_token);
          
          set({
            user: response.user,
            isAuthenticated: true,
          });
        } catch (error) {
          throw error;
        }
      },

      logout: () => {
        TokenManager.clearTokens();
        apiClient.setAuthToken('');
        set({
          user: null,
          isAuthenticated: false,
        });
      },

      refreshToken: async () => {
        try {
          const refreshToken = TokenManager.getRefreshToken();
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }

          const response = await apiClient.refreshToken(refreshToken);
          
          TokenManager.setAccessToken(response.access_token);
          apiClient.setAuthToken(response.access_token);
        } catch (error) {
          // Refresh failed, logout user
          get().logout();
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
```

---

**Navigation:**  
← [Previous: Backend Development](050-backend-development.md) | [Handbook Home](001-cover.md) | [Next: Testing Framework](070-testing-framework.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Development Roadmap](../001-development-roadmap.md)
- [Backend Analysis Guide](../../frontend/docs/backend-analysis-guide.md)
- [Frontend How-To Guides](../../frontend/docs/how-to/)
- [TypeScript Handbook](../../frontend/docs/how-to/00_typescript-handbook.md)
