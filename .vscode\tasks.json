{"version": "2.0.0", "tasks": [{"label": "Type Check - Critical Modules", "type": "shell", "command": "make", "args": ["type-check-critical"], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "mypy", "fileLocation": ["relative", "${workspaceFolder}/server"], "pattern": {"regexp": "^(.+?):(\\d+):(\\d+):\\s+(error|warning|note):\\s+(.+)\\s+\\[(.+)\\]$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5, "code": 6}}}, {"label": "Type Check - Comprehensive", "type": "shell", "command": "make", "args": ["type-check"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "mypy", "fileLocation": ["relative", "${workspaceFolder}/server"], "pattern": {"regexp": "^(.+?):(\\d+):(\\d+):\\s+(error|warning|note):\\s+(.+)\\s+\\[(.+)\\]$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5, "code": 6}}}, {"label": "Format Code", "type": "shell", "command": "make", "args": ["format"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "Lint Code", "type": "shell", "command": "make", "args": ["lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "flake8", "fileLocation": ["relative", "${workspaceFolder}/server"], "pattern": {"regexp": "^(.+?):(\\d+):(\\d+):\\s+(.+)\\s+(.+)$", "file": 1, "line": 2, "column": 3, "code": 4, "message": 5}}}, {"label": "Security Check", "type": "shell", "command": "make", "args": ["security-check"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Run Tests", "type": "shell", "command": "make", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "pytest", "fileLocation": ["relative", "${workspaceFolder}/server"], "pattern": {"regexp": "^(.+?):(\\d+):\\s+(.+)$", "file": 1, "line": 2, "message": 3}}}, {"label": "CI Check", "type": "shell", "command": "make", "args": ["ci-check"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Start Development Server", "type": "shell", "command": "make", "args": ["dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated"}, "isBackground": true, "problemMatcher": {"owner": "u<PERSON><PERSON>", "pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": "^.*Starting.*$", "endsPattern": "^.*<PERSON><PERSON><PERSON> running.*$"}}}, {"label": "Install Dependencies", "type": "shell", "command": "make", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Setup Development Environment", "type": "shell", "command": "make", "args": ["dev-setup"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Clean Cache", "type": "shell", "command": "make", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}]}