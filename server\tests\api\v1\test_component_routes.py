#!/usr/bin/env python3
"""Component API Routes Tests for Ultimate Electrical Designer.

This module provides comprehensive tests for component management API endpoints,
including CRUD operations, search functionality, filtering, and error handling.

Key Test Areas:
- Component CRUD operations with validation
- Advanced search and filtering with pagination
- Component categorization and type filtering
- Authentication and authorization testing
- Error handling and edge cases
- Performance and load testing scenarios
- Advanced search endpoints with complex filtering
- Enhanced bulk operations endpoints
- Performance optimization endpoints
- Cache management endpoints
- Error handling and validation
"""

import json
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, Mock, patch

from fastapi import status
from fastapi.testclient import TestClient

from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchResponseSchema,
    ComponentCreateSchema,
    ComponentReadSchema,
    ComponentSearchSchema,
    ComponentUpdateSchema,
)
from src.core.services.general.component_service import ComponentService
from fixtures.api_mocks import (
    mock_advanced_search_request,
    mock_bulk_create_request,
    mock_bulk_update_request,
    mock_search_response,
    mock_search_result,
    mock_component_service,
    mock_auth_user
)
from fixtures.data_fixtures import sample_component_data

class TestComponentCRUDEndpoints:
    """Test suite for component CRUD operations."""

    def test_create_component_success(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any]
    ):
        """Test successful component creation."""
        # Make real API call without mocking to test actual functionality
        response = authenticated_client.post("/api/v1/components/", json=sample_component_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["name"] == sample_component_data["name"]
        assert response_data["manufacturer"] == sample_component_data["manufacturer"]
        assert response_data["model_number"] == sample_component_data["model_number"]
        assert "id" in response_data

    def test_create_component_validation_error(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component creation with validation error."""
        invalid_data = {
            "name": "",  # Invalid: empty name
            "manufacturer": "ABB",
            "model_number": "S203-C16",
            "component_type": "INVALID_TYPE"  # Invalid enum value
        }

        # FastAPI will return 422 for validation errors at the request level
        response = authenticated_client.post("/api/v1/components/", json=invalid_data)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    def test_create_component_duplicate_error(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component creation with duplicate error."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.create_component.side_effect = BusinessLogicError(detail="Component already exists")

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.post("/api/v1/components/", json=sample_component_data)

            assert response.status_code == status.HTTP_409_CONFLICT
            assert "Component already exists" in response.json()["detail"]
        finally:
            # Clean up the override
            if get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_get_component_success(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful component retrieval."""
        from src.core.services.dependencies import get_component_service

        # Fix the schema data to match ComponentReadSchema expectations
        component_data = sample_component_data.copy()
        # Convert dimensions and metadata to the _json fields expected by ComponentReadSchema
        component_data["dimensions_json"] = component_data.pop("dimensions")
        component_data["metadata_json"] = component_data.pop("metadata")

        component = ComponentReadSchema(
            id=1,
            **component_data,
            created_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00"),
            updated_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00")
        )
        mock_component_service.get_component.return_value = component

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/1")

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["id"] == 1
            assert response_data["name"] == sample_component_data["name"]
            mock_component_service.get_component.assert_called_once_with(1)
        finally:
            # Clean up the override
            if get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_get_component_not_found(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component retrieval with not found error."""
        mock_component_service.get_component.side_effect = NotFoundError(
            code="COMPONENT_NOT_FOUND",
            detail="Component with ID 999 was not found"
        )

        with patch("src.api.v1.component_routes.get_component_service", return_value=mock_component_service):
            
            response = authenticated_client.get("/api/v1/components/999")
            
            assert response.status_code == status.HTTP_404_NOT_FOUND
            assert "not found" in response.json()["detail"].lower()

    def test_update_component_success(
        self,
        authenticated_client: TestClient,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful component update."""
        update_data = {
            "name": "Updated Circuit Breaker",
            "unit_price": "30.00"
        }
        
        # Fix the schema data to match ComponentReadSchema expectations
        component_data = {**sample_component_data, **update_data}
        component_data["dimensions_json"] = component_data.pop("dimensions")
        component_data["metadata_json"] = component_data.pop("metadata")
        
        updated_component = ComponentReadSchema(
            id=1,
            **component_data,
            created_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00"),
            updated_at=datetime.fromisoformat("2024-01-01T01:00:00+00:00")
        )
        mock_component_service.update_component.return_value = updated_component

        from src.core.services.dependencies import get_component_service

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.put("/api/v1/components/1", json=update_data)

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["name"] == update_data["name"]
            assert response_data["unit_price"] == update_data["unit_price"]
            mock_component_service.update_component.assert_called_once()
        finally:
            # Clean up the override
            if get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_delete_component_success(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test successful component deletion."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.delete_component.return_value = True

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.delete("/api/v1/components/1")

            assert response.status_code == status.HTTP_204_NO_CONTENT
            mock_component_service.delete_component.assert_called_once_with(1, deleted_by_user_id=1)
        finally:
            # Clean up the override
            if get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_delete_component_with_dependencies(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test component deletion with dependency error."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.delete_component.side_effect = BusinessLogicError(
            detail="Component has dependencies and cannot be deleted"
        )

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.delete("/api/v1/components/1")

            assert response.status_code == status.HTTP_409_CONFLICT
            assert "dependencies" in response.json()["detail"].lower()
        finally:
            # Clean up the override
            if get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]


class TestComponentSearchEndpoints:
    """Test suite for component search and filtering."""

    def test_list_components_success(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result
    ):
        """Test successful component listing."""
        from src.core.services.dependencies import get_component_service
        from src.core.schemas.general.component_schemas import ComponentPaginatedResponseSchema
        from src.core.schemas.base import PaginationSchema

        # Create a simple object that behaves like ComponentPaginatedResponseSchema
        class MockResponse:
            def __init__(self):
                self.items = mock_search_result["items"]
                self.pagination = type('obj', (object,), {
                    'page': 1, 'size': 20, 'total': 2, 'pages': 1
                })()

        mock_response = MockResponse()
        mock_component_service.search_components.return_value = mock_response

        # Override the dependency using the app from the test client
        # Access the FastAPI app instance through the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/")

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "items" in response_data
            assert "pagination" in response_data
            mock_component_service.search_components.assert_called_once()
        finally:
            # Clean up the override
            if get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_list_components_with_filters(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result
    ):
        """Test component listing with filters."""
        from src.core.services.dependencies import get_component_service

        # Create a simple object that behaves like ComponentPaginatedResponseSchema
        class MockResponse:
            def __init__(self):
                self.items = mock_search_result["items"]
                self.pagination = type('obj', (object,), {
                    'page': 1, 'size': 20, 'total': 2, 'pages': 1
                })()

        mock_response = MockResponse()
        mock_component_service.search_components.return_value = mock_response

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            
            response = authenticated_client.get(
                "/api/v1/components/",
                params={
                    "search_term": "circuit breaker",
                    "category": ComponentCategoryType.PROTECTION_DEVICES.value,
                    "manufacturer": "ABB",
                    "is_preferred": True,
                    "page": 1,
                    "size": 10
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            mock_component_service.search_components.assert_called_once()
            
            # Verify search parameters were passed correctly
            call_args = mock_component_service.search_components.call_args
            search_params = call_args[0][0]  # First argument
            assert search_params.search_term == "circuit breaker"
            assert search_params.category == ComponentCategoryType.PROTECTION_DEVICES
            assert search_params.manufacturer == "ABB"
            assert search_params.is_preferred is True
        finally:
            # Clean up the override
            if get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_advanced_search_components(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result
    ):
        """Test advanced component search."""
        search_data = {
            "search_term": "circuit breaker",
            "category": ComponentCategoryType.PROTECTION_DEVICES.value,
            "component_type": ComponentType.CIRCUIT_BREAKER.value,
            "manufacturer": "ABB",
            "is_preferred": False,
            "is_active": True,
            "min_price": 10.0,
            "max_price": 50.0,
            "currency": "EUR",
            "stock_status": "available",
            "specifications": {
                "electrical": {
                    "current_rating": "16A"
                }
            }
        }
        
        from src.core.services.dependencies import get_component_service

        # Create a simple object that behaves like ComponentAdvancedSearchResponseSchema
        class MockAdvancedResponse:
            def __init__(self):
                # Wrap each component in a ComponentSearchResultSchema structure
                self.items = [
                    {
                        "component": item,
                        "relevance_score": 0.95,
                        "matched_fields": ["name", "manufacturer"]
                    }
                    for item in mock_search_result["items"]
                ]
                self.pagination = type('obj', (object,), {
                    'page': 1, 'size': 20, 'total': 2, 'pages': 1
                })()
                self.search_metadata = {}
                self.suggestions = []

        mock_response = MockAdvancedResponse()
        mock_component_service.search_components_with_builder.return_value = mock_response

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            # Use POST to the correct advanced search endpoint
            advanced_search_data = {
                "search_term": search_data["search_term"],
                "basic_filters": [
                    {
                        "field": "category",
                        "operator": "eq",
                        "value": search_data["category"]
                    },
                    {
                        "field": "manufacturer",
                        "operator": "eq",
                        "value": search_data["manufacturer"]
                    }
                ]
            }

            response = authenticated_client.post(
                "/api/v1/components/search/advanced",
                json=advanced_search_data,
                params={"page": 1, "size": 20}
            )

            assert response.status_code == status.HTTP_200_OK
            # Verify the correct method was called
            mock_component_service.search_components_with_builder.assert_called_once()
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]


class TestComponentCategoryEndpoints:
    """Test suite for component category and type endpoints."""

    def test_list_component_categories(
        self,
        authenticated_client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test listing component categories."""
        response = authenticated_client.get("/api/v1/components/categories")

        assert response.status_code == status.HTTP_200_OK
        categories = response.json()
        assert isinstance(categories, list)
        assert len(categories) > 0

        # Verify category structure
        for category in categories:
            assert "value" in category
            assert "description" in category

    def test_list_component_types(
        self,
        authenticated_client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test listing component types."""
        response = authenticated_client.get("/api/v1/components/types")

        assert response.status_code == status.HTTP_200_OK
        types = response.json()
        assert isinstance(types, list)
        assert len(types) > 0

        # Verify type structure
        for comp_type in types:
            assert "value" in comp_type
            assert "description" in comp_type

    def test_list_component_types_filtered_by_category(
        self,
        authenticated_client: TestClient,
        mock_auth_user: Dict[str, Any]
    ):
        """Test listing component types filtered by category."""
        response = authenticated_client.get(
            "/api/v1/components/types",
            params={"category": ComponentCategoryType.PROTECTION_DEVICES.value}
        )

        assert response.status_code == status.HTTP_200_OK
        types = response.json()
        assert isinstance(types, list)
        # Should have fewer types when filtered by category
        assert len(types) >= 0

    def test_get_components_by_category(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting components by category."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.get_components_by_category.return_value = []

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get(f"/api/v1/components/by-category/{ComponentCategoryType.PROTECTION_DEVICES.value}")

            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_components_by_category.assert_called_once()
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_get_components_by_type(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting components by type."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.get_components_by_type.return_value = []

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get(f"/api/v1/components/by-type/{ComponentType.CIRCUIT_BREAKER.value}")

            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_components_by_type.assert_called_once()
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_get_preferred_components(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting preferred components."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.get_preferred_components.return_value = []

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/preferred", params={"skip": 0, "limit": 100})

            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_preferred_components.assert_called_once()
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]


class TestComponentStatsEndpoint:
    """Test suite for component statistics endpoint."""

    def test_get_component_stats(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any]
    ):
        """Test getting component statistics."""
        from src.core.schemas.general.component_schemas import ComponentStatsSchema
        from src.core.services.dependencies import get_component_service

        mock_stats = ComponentStatsSchema(
            total_components=100,
            active_components=95,
            preferred_components=25,
            components_by_category={"protection_devices": 30, "cables": 40},
            components_by_manufacturer={"ABB": 25, "Schneider": 20},
            average_price=Decimal("25.50"),
            price_range={"min": Decimal("5.00"), "max": Decimal("100.00")}
        )
        mock_component_service.get_component_stats.return_value = mock_stats

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/stats")

            assert response.status_code == status.HTTP_200_OK
            stats = response.json()
            assert stats["total_components"] == 100
            assert stats["active_components"] == 95
            assert stats["preferred_components"] == 25
            assert "components_by_category" in stats
            assert "components_by_manufacturer" in stats
            mock_component_service.get_component_stats.assert_called_once()
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]


class TestComponentAuthenticationAndAuthorization:
    """Test suite for authentication and authorization."""

    def test_create_component_without_auth(self, client: TestClient):
        """Test component creation without authentication."""
        component_data = {
            "name": "Test Component",
            "manufacturer": "Test Manufacturer",
            "model_number": "TEST-001",
            "component_type": ComponentType.CIRCUIT_BREAKER.value
        }
        
        response = client.post("/api/v1/components/", json=component_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_get_component_without_auth(self, client: TestClient):
        """Test component retrieval without authentication."""
        response = client.get("/api/v1/components/1")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_list_components_without_auth(self, client: TestClient):
        """Test component listing without authentication."""
        response = client.get("/api/v1/components/")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestComponentErrorHandling:
    """Test suite for error handling scenarios."""

    def test_invalid_component_id_format(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test invalid component ID format."""
        from src.core.services.dependencies import get_component_service

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/invalid_id")
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_invalid_pagination_parameters(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test invalid pagination parameters."""
        from src.core.services.dependencies import get_component_service

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/", params={"page": 0, "size": -1})
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_invalid_enum_values(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test invalid enum values in filters."""
        from src.core.services.dependencies import get_component_service

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get(
                "/api/v1/components/",
                params={"category": "INVALID_CATEGORY", "component_type": "INVALID_TYPE"}
            )
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]


# Performance and load testing scenarios
class TestComponentPerformance:
    """Test suite for performance scenarios."""

    def test_large_component_list_pagination(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test pagination with large component lists."""
        from src.core.schemas.general.component_schemas import ComponentPaginatedResponseSchema
        from src.core.schemas.base import PaginationSchema
        from src.core.services.dependencies import get_component_service

        # Mock large result set
        mock_result = ComponentPaginatedResponseSchema(
            items=[],
            pagination=PaginationSchema(
                page=1,
                size=100,
                total=10000,
                pages=100
            )
        )
        mock_component_service.search_components.return_value = mock_result

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/", params={"size": 100})
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["pagination"]["total"] == 10000
            assert response_data["pagination"]["pages"] == 100
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

    def test_complex_search_query_performance(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test performance with complex search queries."""
        from src.core.schemas.general.component_schemas import ComponentPaginatedResponseSchema
        from src.core.schemas.base import PaginationSchema
        from src.core.services.dependencies import get_component_service

        from src.core.schemas.general.component_schemas import ComponentAdvancedSearchResponseSchema

        mock_result = ComponentAdvancedSearchResponseSchema(
            items=[],
            pagination=PaginationSchema(page=1, size=20, total=0, pages=0),
            search_metadata={
                "total_matches": 0,
                "search_time_ms": 10,
                "filters_applied": []
            },
            suggestions=[]
        )
        mock_component_service.search_components_with_builder.return_value = mock_result

        complex_search = {
            "search_term": "high voltage circuit breaker protection device",
            "category": ComponentCategoryType.PROTECTION_DEVICES.value,
            "component_type": ComponentType.CIRCUIT_BREAKER.value,
            "manufacturer": "ABB",
            "min_price": 100.0,
            "max_price": 1000.0,
            "specifications": {
                "electrical": {
                    "voltage_rating": "400V",
                    "current_rating": "63A",
                    "breaking_capacity": "10kA"
                },
                "standards_compliance": ["IEC-60898-1"]
            }
        }

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.post("/api/v1/components/search/advanced", json=complex_search)
            assert response.status_code == status.HTTP_200_OK
            mock_component_service.search_components_with_builder.assert_called_once()
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]

class TestAdvancedSearchEndpoints:
    """Test cases for advanced search API endpoints."""
    

    def test_advanced_search_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any],
        sample_advanced_search_request: Dict[str, Any],
        sample_search_response: ComponentAdvancedSearchResponseSchema
    ):
        """Test successful advanced search endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.search_components_with_builder.return_value = sample_search_response
                mock_service.return_value = mock_service_instance
                
                response = client.post(
                    "/api/v1/components/search/advanced",
                    json=sample_advanced_search_request,
                    params={"page": 1, "size": 20}
                )
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert "items" in response_data
                assert "pagination" in response_data
                assert "search_metadata" in response_data
                assert len(response_data["items"]) == 1
                assert response_data["items"][0]["component"]["manufacturer"] == "Schneider Electric"
                assert response_data["search_metadata"]["search_type"] == "advanced_builder"
    
    def test_advanced_search_endpoint_validation_error(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test advanced search endpoint with validation error."""
        from src.core.services.dependencies import get_component_service

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            # Invalid request data (missing required fields)
            invalid_request = {
                "basic_filters": [
                    {
                        "field": "manufacturer",
                        "operator": "invalid_operator",  # Invalid operator
                        "value": "Schneider Electric"
                    }
                ]
            }

            response = authenticated_client.post(
                "/api/v1/components/search/advanced",
                json=invalid_request
            )

            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]
    
    def test_relevance_search_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any],
        sample_search_response: ComponentAdvancedSearchResponseSchema
    ):
        """Test successful relevance search endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.search_components_with_relevance.return_value = sample_search_response
                mock_service.return_value = mock_service_instance
                
                response = client.get(
                    "/api/v1/components/search/relevance",
                    params={
                        "search_term": "circuit breaker",
                        "search_fields": "name,description",
                        "fuzzy": False,
                        "page": 1,
                        "size": 20
                    }
                )
                
                assert response.status_code == status.HTTP_200_OK
                
                response_data = response.json()
                assert "items" in response_data
                assert len(response_data["items"]) == 1
                assert response_data["items"][0]["relevance_score"] == 0.95
    
    def test_relevance_search_endpoint_invalid_fields(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test relevance search endpoint with invalid search fields."""
        from src.core.services.dependencies import get_component_service

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get(
                "/api/v1/components/search/relevance",
                params={
                    "search_term": "circuit breaker",
                    "search_fields": "invalid_field,another_invalid",
                    "page": 1,
                    "size": 20
                }
            )

            assert response.status_code == status.HTTP_400_BAD_REQUEST
            response_data = response.json()
            assert "Invalid search fields" in response_data["detail"]
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]


class TestEnhancedBulkOperationsEndpoints:
    """Test cases for enhanced bulk operations API endpoints."""


    def test_enhanced_bulk_create_endpoint_success(
        self,
        client: TestClient,
        mock_auth_user: Dict[str, Any],
        sample_bulk_create_request: List[Dict[str, Any]]
    ):
        """Test successful enhanced bulk create endpoint."""
        with patch('src.api.v1.component_routes.require_authenticated_user') as mock_auth:
            mock_auth.return_value = mock_auth_user
            
            with patch('src.api.v1.component_routes.get_component_service') as mock_service:
                mock_service_instance = Mock(spec=ComponentService)
                mock_service_instance.bulk_create_with_validation.return_value = {
                    "total_processed": 2,
                    "created": 2,
                    "errors": 0,
                    "success_rate": 1.0,
                    "created_components": [],
                    "validation_errors": []
                }
                mock_service.return_value = mock_service_instance
                
                response = client.post(
                    "/api/v1/components/bulk/create-validated",
                    json=sample_bulk_create_request,
                    params={
                        "validate_duplicates": True,
                        "batch_size": 100
                    }
                )
                
                assert response.status_code == status.HTTP_201_CREATED
                
                response_data = response.json()
                assert response_data["total_processed"] == 2
                assert response_data["created"] == 2
                assert response_data["errors"] == 0
                assert response_data["success_rate"] == 1.0
    
    def test_selective_bulk_update_endpoint_success(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test successful selective bulk update endpoint."""
        from src.core.services.dependencies import get_component_service

        update_data = [
            {
                "id": 1,
                "unit_price": 28.99,
                "is_preferred": True
            },
            {
                "id": 2,
                "description": "Updated description"
            }
        ]

        mock_component_service.bulk_update_selective.return_value = {
            "total_processed": 2,
            "updated": 2,
            "errors": 0,
            "success_rate": 1.0,
            "validation_errors": []
        }

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.put(
                "/api/v1/components/bulk/update-selective",
                json=update_data,
                params={"batch_size": 100}
            )

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["total_processed"] == 2
            assert response_data["updated"] == 2
            assert response_data["success_rate"] == 1.0
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]
    
    def test_bulk_delete_endpoint_success(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test successful bulk delete endpoint."""
        from src.core.services.dependencies import get_component_service

        component_ids = [1, 2, 3, 4, 5]

        mock_component_service.bulk_delete_components.return_value = {
            "total_processed": 5,
            "deleted": 5,
            "not_found": 0,
            "success_rate": 1.0,
            "not_found_ids": [],
            "delete_type": "soft"
        }

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            # For DELETE with body, we need to use a different approach
            # The endpoint expects component_ids as a request body parameter
            response = authenticated_client.request(
                "DELETE",
                "/api/v1/components/bulk/delete",
                json=component_ids,
                params={"soft_delete": True}
            )

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["total_processed"] == 5
            assert response_data["deleted"] == 5
            assert response_data["delete_type"] == "soft"
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]


class TestPerformanceOptimizationEndpoints:
    """Test cases for performance optimization API endpoints."""

    def test_get_performance_metrics_endpoint(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test get performance metrics endpoint."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.get_performance_metrics.return_value = {
            "component_statistics": {
                "total_components": 1000,
                "active_components": 950,
                "preferred_components": 100
            },
            "cache_performance": {
                "memory_cache_size": 50,
                "redis_connected": True
            },
            "query_performance": {
                "slow_queries_count": 2,
                "slowest_query_time": 1.5
            },
            "system_health": {
                "database_connected": True,
                "cache_connected": True,
                "performance_monitoring_active": True
            }
        }

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.get("/api/v1/components/performance/metrics")

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert "component_statistics" in response_data
            assert "cache_performance" in response_data
            assert "query_performance" in response_data
            assert "system_health" in response_data
            assert response_data["component_statistics"]["total_components"] == 1000
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]
    
    def test_optimize_system_performance_endpoint(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test optimize system performance endpoint."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.optimize_system_performance.return_value = {
            "cache_warming": True,
            "cache_cleanup": True,
            "index_analysis": True,
            "query_optimization": True,
            "errors": [],
            "index_suggestions": [
                "CREATE INDEX CONCURRENTLY idx_components_manufacturer_model ON components(manufacturer, model_number);"
            ]
        }

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.post("/api/v1/components/performance/optimize")

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["cache_warming"] == True
            assert response_data["cache_cleanup"] == True
            assert response_data["index_analysis"] == True
            assert len(response_data["errors"]) == 0
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]
    
    def test_invalidate_cache_endpoint(
        self,
        authenticated_client: TestClient,
        mock_component_service: MagicMock
    ):
        """Test invalidate cache endpoint."""
        from src.core.services.dependencies import get_component_service

        mock_component_service.invalidate_component_cache.return_value = {
            "success": True,
            "component_id": 123,
            "scope": "specific",
            "timestamp": "2024-01-01T00:00:00Z"
        }

        # Override the dependency using the app from the test client
        app = authenticated_client.app
        if hasattr(app, 'dependency_overrides'):
            app.dependency_overrides[get_component_service] = lambda: mock_component_service
        else:
            # Fallback: try to access through the client's internal app
            app = getattr(authenticated_client, '_client', authenticated_client).app
            app.dependency_overrides[get_component_service] = lambda: mock_component_service

        try:
            response = authenticated_client.delete(
                "/api/v1/components/cache/invalidate",
                params={"component_id": 123}
            )

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["success"] == True
            assert response_data["component_id"] == 123
            assert response_data["scope"] == "specific"
        finally:
            # Clean up the override
            if hasattr(app, 'dependency_overrides') and get_component_service in app.dependency_overrides:
                del app.dependency_overrides[get_component_service]
