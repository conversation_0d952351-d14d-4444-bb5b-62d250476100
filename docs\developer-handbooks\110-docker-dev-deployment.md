# 110 - Docker Deployment for Development (Mocking Production)

**Section:** 110-docker-dev-deployment
**Version:** 1.0
**Last Updated:** July 2025
**Prerequisites:** Basic understanding of Docker, Docker Compose, and command-line interface. Docker Desktop or equivalent installed.
**Estimated Reading Time:** 15 minutes

## Overview

This section covers the deployment of our `ultimate-electrical-designer` application using Docker Compose for the development environment. The goal is to create a local setup that closely mirrors our production environment, including all backend services (Python API, C# CAD/Computation services), databases, and frontend build, minimizing "it works on my machine" issues. This approach ensures consistent environments for all developers and streamlines the path to production deployment.

## Table of Contents

- [Quick Reference](#quick-reference)
- [Core Concepts](#core-concepts)
- [Implementation Guide](#implementation-guide)
- [Best Practices](#best-practices)
- [Common Patterns](#common-patterns)
- [Troubleshooting](#troubleshooting)
- [Examples](#examples)
- [Related Documentation](#related-documentation)

## Quick Reference

### Key Commands
```bash
docker compose up -d --build # Builds (if necessary) and starts all services in detached mode
docker compose up             # Builds (if necessary) and starts all services in foreground (for logs)
docker compose down           # Stops and removes all service containers, networks, and volumes (by default)
docker compose down -v        # Stops and removes all containers, networks, and volumes (including named volumes)
docker compose build          # Builds or rebuilds services
docker compose logs -f <service_name> # Follow logs for a specific service (e.g., 'python-api', 'cad-service')
docker compose exec <service_name> <command> # Execute a command inside a running service container
```

### Essential Files

  - `ultimate-electrical-designer/docker-compose.yml` - Defines the multi-service application, networks, and volumes.
  - `ultimate-electrical-designer/server/Dockerfile` - Dockerfile for the Python API service.
  - `ultimate-electrical-designer/cad-integrator-service/Dockerfile` - Dockerfile for the C\# CAD Integration Service.
  - `ultimate-electrical-designer/computation-engine-service/Dockerfile` - Dockerfile for the C\# Computation Engine Service.
  - `ultimate-electrical-designer/.env` - Environment variables for local Docker Compose setup.

### Important Concepts

  - **Containerization:** Packaging an application and its dependencies into isolated units.
  - **Services:** Independent components of the application (e.g., frontend, backend API, database) defined in `docker-compose.yml`.
  - **Volumes:** Persistent data storage for containers, ensuring data survives container recreation.
  - **Networks:** Isolated communication channels between Docker containers.
  - **Service Discovery:** Containers within the same `docker-compose` network can communicate using service names as hostnames.

## Core Concepts

### Docker Compose for Development Environment

Docker Compose is a tool for defining and running multi-container Docker applications. It allows developers to declare all services, networks, and volumes required for the application in a single `docker-compose.yml` file. This provides a consistent and reproducible development environment that closely mimics production setups, especially when moving towards Kubernetes.

#### Key Principles

1.  **Environment Consistency:** Ensures all developers run the same application stack, eliminating "works on my machine" problems caused by environment discrepancies.
2.  **Production Parity:** By using Docker images and configurations similar to production, it helps identify deployment issues earlier in the development cycle.
3.  **Isolation:** Each service runs in its own isolated container, preventing conflicts between dependencies and local machine configurations.
4.  **Simplified Setup:** A single command (`docker compose up`) brings up the entire application stack, significantly reducing setup time for new developers.

### Services and Inter-Service Communication

In our Docker Compose setup, each major component of the `ultimate-electrical-designer` application (Python API, C\# CAD Service, C\# Computation Service, PostgreSQL, Redis, React frontend build server) runs as a separate Docker service.

Containers within the same Docker Compose network can communicate with each other using their service names as hostnames. For example, the Python API service can reach the PostgreSQL database at `postgresql` and the C\# CAD integration service at `cad-service`.

## Implementation Guide

### Step 1: Define Services in `docker-compose.yml`

Create a `docker-compose.yml` file in the root of your `ultimate-electrical-designer` project. This file will declare all the services, their build contexts, ports, volumes, and network configurations.

```yaml
# ultimate-electrical-designer/docker-compose.yml
version: '3.8'

services:
  # PostgreSQL Database Service
  postgresql:
    image: postgres:15-alpine
    restart: always
    env_file:
      - .env
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432" # Expose for local access if needed
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ultimate_designer_network

  # Redis Cache Service
  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379" # Expose for local access if needed
    volumes:
      - redis_data:/data
    networks:
      - ultimate_designer_network

  # Python API Service
  python-api:
    build:
      context: ./server
      dockerfile: Dockerfile
    restart: always
    env_file:
      - .env
    ports:
      - "8000:8000" # Expose FastAPI port
    volumes:
      - ./server:/app # Mount source for live reload during dev
    depends_on:
      - postgresql
      - redis
    networks:
      - ultimate_designer_network

  # C# CAD Integration Service
  cad-service:
    build:
      context: ./cad-integrator-service
      dockerfile: Dockerfile
    restart: always
    env_file:
      - .env
    ports:
      - "5000:5000" # Expose C# service port (adjust if using gRPC, etc.)
    volumes:
      - ./cad-integrator-service:/app # Mount source for dev
    networks:
      - ultimate_designer_network

  # C# Computation Engine Service
  computation-service:
    build:
      context: ./computation-engine-service
      dockerfile: Dockerfile
    restart: always
    env_file:
      - .env
    ports:
      - "5001:5001" # Expose C# service port (adjust if using gRPC, etc.)
    volumes:
      - ./computation-engine-service:/app # Mount source for dev
    networks:
      - ultimate_designer_network

  # React Frontend Development Server (optional, for hot-reloading)
  # For production-like mocking, you'd typically serve static assets via Nginx/Caddy
  # but for dev, running the Next.js dev server is more practical.
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile.dev # Assuming a separate Dockerfile for dev setup
    ports:
      - "3000:3000" # Expose React dev server port
    volumes:
      - ./client:/app # Mount source for hot-reloading
      - /app/node_modules # Exclude node_modules from host mount to prevent issues
    environment:
      # Pass backend API URL to frontend (adjust to your actual FastAPI port)
      NEXT_PUBLIC_API_BASE_URL: http://localhost:8000
    networks:
      - ultimate_designer_network

volumes:
  postgres_data:
  redis_data:

networks:
  ultimate_designer_network:
    driver: bridge
```

### Step 2: Create Service-Specific Dockerfiles

Ensure each service (`server`, `cad-integrator-service`, `computation-engine-service`, `client`) has an appropriate `Dockerfile`.

**`ultimate-electrical-designer/server/Dockerfile` (Python API)**

```dockerfile
# Use a lightweight Python base image
FROM python:3.13-slim-bookworm

# Set working directory
WORKDIR /app

# Copy poetry.lock and pyproject.toml files to the working directory
COPY pyproject.toml poetry.lock ./

# Install Poetry and dependencies
RUN pip install poetry
RUN poetry install --no-root --no-dev --no-interaction --no-ansi

# Copy the rest of the application code
COPY . .

# Expose the port FastAPI runs on
EXPOSE 8000

# Command to run the application (using Uvicorn for FastAPI)
# Use host 0.0.0.0 to make it accessible outside the container
CMD ["poetry", "run", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"] # --reload for dev
```

**`ultimate-electrical-designer/cad-integrator-service/Dockerfile` (C\# CAD Integration Service)**

```dockerfile
# Use SDK image to build
FROM [mcr.microsoft.com/dotnet/sdk:8.0](https://mcr.microsoft.com/dotnet/sdk:8.0) AS build
WORKDIR /src
COPY ["ultimate_electrical_designer.CadIntegrator.csproj", "./"]
RUN dotnet restore "ultimate_electrical_designer.CadIntegrator.csproj"
COPY . .
WORKDIR "/src/."
RUN dotnet build "ultimate_electrical_designer.CadIntegrator.csproj" -c Release -o /app/build

# Use ASP.NET Runtime image to run
FROM [mcr.microsoft.com/dotnet/aspnet:8.0](https://mcr.microsoft.com/dotnet/aspnet:8.0) AS final
WORKDIR /app
COPY --from=build /app/build .
# Expose the port your C# app listens on (e.g., for an API or gRPC)
EXPOSE 5000
ENTRYPOINT ["dotnet", "ultimate_electrical_designer.CadIntegrator.dll"]
```

**`ultimate-electrical-designer/computation-engine-service/Dockerfile` (C\# Computation Engine Service)**

```dockerfile
# Use SDK image to build
FROM [mcr.microsoft.com/dotnet/sdk:8.0](https://mcr.microsoft.com/dotnet/sdk:8.0) AS build
WORKDIR /src
COPY ["ultimate_electrical_designer.ComputationEngine.csproj", "./"]
RUN dotnet restore "ultimate_electrical_designer.ComputationEngine.csproj"
COPY . .
WORKDIR "/src/."
RUN dotnet build "ultimate_electrical_designer.ComputationEngine.csproj" -c Release -o /app/build

# Use ASP.NET Runtime image to run
FROM [mcr.microsoft.com/dotnet/aspnet:8.0](https://mcr.microsoft.com/dotnet/aspnet:8.0) AS final
WORKDIR /app
COPY --from=build /app/build .
# Expose the port your C# app listens on
EXPOSE 5001
ENTRYPOINT ["dotnet", "ultimate_electrical_designer.ComputationEngine.dll"]
```

**`ultimate-electrical-designer/client/Dockerfile.dev` (React Frontend Dev Server)**

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package.json and install dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Copy the rest of the application code
COPY . .

EXPOSE 3000

# Command to run the Next.js dev server
CMD ["yarn", "dev"]
```

### Step 3: Configure Environment Variables

Create a `.env` file in the root of your `ultimate-electrical-designer` project to store sensitive or environment-specific variables.

```ini
# ultimate-electrical-designer/.env
# Database Credentials
POSTGRES_DB=ultimate_designer_db
POSTGRES_USER=designer_user
POSTGRES_PASSWORD=securepassword

# Redis Configuration (optional, if you need specific Redis settings)
# REDIS_HOST=redis
# REDIS_PORT=6379

# Python API Settings (update with service names for inter-service communication)
DATABASE_URL=postgresql+psycopg://designer_user:securepassword@postgresql:5432/ultimate_designer_db
REDIS_URL=redis://redis:6379/0
CAD_SERVICE_URL=http://cad-service:5000 # Use service name as hostname
COMPUTATION_SERVICE_URL=http://computation-service:5001 # Use service name as hostname

# Frontend Settings (adjust if necessary, NEXT_PUBLIC_ is important for client-side access)
# This will be picked up by the frontend Dockerfile
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000 # Or your external host if not accessing via localhost
```

**Note:** For the `NEXT_PUBLIC_API_BASE_URL` in the frontend, if you're accessing your frontend via `localhost:3000` in your browser, the frontend will make calls to `localhost:8000` for the Python API. If your Docker network is more complex or you access it via a different hostname, adjust `NEXT_PUBLIC_API_BASE_URL` accordingly. For inter-service communication from Python to C\# services, use the service names as hostnames (e.g., `cad-service:5000`).

### Step 4: Run the Application

Navigate to the root directory of your project in your terminal and run:

```bash
docker compose up -d --build
```

This command will:

1.  Read your `docker-compose.yml` file.
2.  Build the Docker images for your Python API, C\# services, and frontend (if `build` context is specified and no existing image, or if `--build` is used).
3.  Create and start containers for all defined services.
4.  Set up an internal network (`ultimate_designer_network`) for containers to communicate.
5.  Run containers in detached mode (`-d`), so they run in the background.

### Step 5: Access the Application

  * **Frontend:** Open your web browser and navigate to `http://localhost:3000` (or the port you exposed for the frontend).
  * **Python API:** You can interact with your API at `http://localhost:8000`. Access the Swagger UI at `http://localhost:8000/docs`.

## Best Practices

### Engineering-Grade Standards

1.  **Single Source of Truth**

      - Your `docker-compose.yml` should be the definitive source for how your development environment is assembled. Avoid manual configurations outside of this file.
      - Environment variables should be managed consistently via `.env` files for development and orchestrated solutions (Kubernetes ConfigMaps/Secrets) for production.

2.  **Unified Patterns Compliance**

      - Ensure your Dockerfiles follow best practices for image building (e.g., multi-stage builds for smaller images, using `.dockerignore`).
      - Standardize container naming conventions and labeling for easier management.

3.  **IEEE/IEC/EN Standards**

      - While Docker deployment itself isn't directly covered by these, ensuring the *services* deployed comply with relevant electrical engineering standards is paramount. Docker provides the consistent environment needed for repeatable testing against these standards.

### Performance Considerations

  - **Resource Limits (for dev):** While less critical than production, avoid giving containers excessive resources in development, as it can starve your host machine.
  - **Volume Performance:** For development, bind mounts (`./server:/app`) are useful for live reloading. Be aware that performance can sometimes be slower on macOS/Windows due to file system sharing overhead. Consider using named volumes if performance becomes an issue and live reloading isn't strictly necessary for a specific service.
  - **`.dockerignore`:** Use `.dockerignore` files to prevent unnecessary files (e.g., `node_modules` on host, `.git`, `__pycache__`) from being copied into your Docker images, reducing build times and image sizes.

### Security Requirements

  - **Secrets Management:** Never hardcode sensitive information (passwords, API keys) directly in `docker-compose.yml` or Dockerfiles. Use `.env` files for development and more robust secret management solutions (e.g., Docker Secrets, Kubernetes Secrets, HashiCorp Vault) for production.
  - **Least Privilege:** Configure containers with the minimum necessary permissions. Avoid running containers as `root` if possible.
  - **Image Vulnerabilities:** Regularly scan your Docker images for known vulnerabilities. Use official base images and keep them updated.

## Common Patterns

### Pattern 1: Multi-Stage Builds

Used in C\# and often Python Dockerfiles to create smaller, more secure production-ready images by separating build dependencies from runtime dependencies.

```dockerfile
# build stage
FROM [mcr.microsoft.com/dotnet/sdk:8.0](https://mcr.microsoft.com/dotnet/sdk:8.0) AS build
WORKDIR /src
COPY ["ultimate_electrical_designer.CadIntegrator.csproj", "./"]
RUN dotnet restore "ultimate_electrical_designer.CadIntegrator.csproj"
COPY . .
WORKDIR "/src/."
RUN dotnet build "ultimate_electrical_designer.CadIntegrator.csproj" -c Release -o /app/build

# final (runtime) stage
FROM [mcr.microsoft.com/dotnet/aspnet:8.0](https://mcr.microsoft.com/dotnet/aspnet:8.0) AS final
WORKDIR /app
COPY --from=build /app/build .
EXPOSE 5000
ENTRYPOINT ["dotnet", "ultimate_electrical_designer.CadIntegrator.dll"]
```

**When to use:** For almost all production-ready Dockerfiles where you want to minimize image size and attack surface.
**Benefits:** Smaller images, faster deployments, improved security.
**Considerations:** Requires understanding how to copy artifacts between build stages.

### Pattern 2: Volume Mounts for Development

Leveraging bind mounts in `docker-compose.yml` allows for live code changes on the host to be reflected inside the container, facilitating rapid development with hot-reloading frameworks.

```yaml
services:
  python-api:
    volumes:
      - ./server:/app # Mount source for live reload during dev
```

**When to use:** During active development where code changes need to be immediately reflected without rebuilding containers.
**Benefits:** Faster feedback loop, improved developer experience.
**Considerations:** Can introduce performance overhead on non-Linux hosts; requires the container to have `inotify` or similar file watching capabilities (often handled by dev servers like Uvicorn's `--reload`).

## Troubleshooting

### Common Issues

#### Issue 1: Port Conflicts

**Symptoms:** `docker: Error response from daemon: driver failed programming external connectivity on endpoint ... bind: address already in use`
**Cause:** Another process on your host machine is already using one of the ports exposed by your `docker-compose.yml` (e.g., 5432, 6379, 8000, 3000, 5000, 5001).
**Solution:**

1.  Identify the process using the port (e.g., `lsof -i :<port>` on Linux/macOS, `netstat -ano | findstr :<port>` on Windows).
2.  Stop the conflicting process, or change the exposed port in your `docker-compose.yml` (e.g., `8001:8000`).

#### Issue 2: Service Startup Order Issues

**Symptoms:** Python API fails to start because PostgreSQL or Redis isn't ready, even with `depends_on`.
**Cause:** `depends_on` only ensures container *start order*, not *service readiness*. A database container might start before the database server inside it is fully initialized and ready to accept connections.
**Solution:** Implement a "wait-for-it" mechanism.

1.  **Python:** Use a library like `wait-for-it` or `dockerize` in your Python API's entrypoint script, or simply add a loop that retries database connections until successful.
2.  **C\#:** Implement a similar retry loop in your C\# service's startup code.

#### Issue 3: Volume Permissions

**Symptoms:** Containers cannot write to mounted volumes, or files created by containers have incorrect permissions on the host.
**Cause:** Mismatched user IDs between the container process and the host user.
**Solution:**

1.  Ensure the user running the process inside the container has appropriate permissions for the mounted directories.
2.  (Linux/macOS) Create a non-root user in your Dockerfile and set appropriate permissions.
3.  (Development) Sometimes, `sudo chmod -R 777 <volume_path>` on the host can temporarily fix permission issues for dev, but this is not recommended for production.

### Debugging Tips

  - **Check Container Logs:** Use `docker compose logs` (or `docker compose logs -f <service_name>`) to view output from your services. This is the first place to look for errors.
  - **Inspect Running Containers:** Use `docker inspect <container_id_or_name>` to view detailed information about a running container (network settings, volumes, environment variables).
  - **Enter Running Containers:** Use `docker compose exec <service_name> bash` (or `sh`) to get a shell inside a running container, allowing you to inspect files, run commands, and debug interactively.
  - **Build Verbosity:** Use `docker compose build --no-cache --progress=plain` to get more detailed output during the build process, which can help diagnose Dockerfile errors.

## Examples

### Basic Docker-Compose.yml for Python Backend & Database

```yaml
# Simplified example focusing on Python API and PostgreSQL
version: '3.8'
services:
  postgresql:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: dev_db
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_pass
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - app_network

  python-api:
    build: ./server
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: postgresql+psycopg://dev_user:dev_pass@postgresql:5432/dev_db
    depends_on:
      - postgresql
    networks:
      - app_network

volumes:
  postgres_dev_data:

networks:
  app_network:
```

### Advanced Docker-Compose.yml with All Services (as above in "Implementation Guide")

Refer to the "Step 1: Define Services in `docker-compose.yml`" section for a complete example including Python, C\# services, database, and frontend.

### Real-World Scenario

During the development of the `cable_sizing` module, a team member needed to integrate with a new version of the C\# `computation-engine-service`. By using the Docker Compose setup:

1.  They pulled the latest `computation-engine-service` code.
2.  Updated its `Dockerfile` and `docker-compose.yml` entry if necessary.
3.  Ran `docker compose up --build computation-service`.
4.  The Python `cable_sizing` logic, already configured to call `computation-service:5001`, seamlessly connected to the updated C\# service without needing any local environment changes for Python dependencies or port management. This allowed for isolated testing of the integration.

## Related Documentation

### Internal References

  - [Handbook Home](./001-cover.md)

### Backend Documentation


### Frontend Documentation



### External Standards

  - [Docker Documentation](https://docs.docker.com/) - Official documentation for Docker and Docker Compose.
  - [Kubernetes Documentation](https://kubernetes.io/docs/) - For understanding production orchestration concepts.

---

**Navigation:**
← [Previous Section](./100-database-management.md) | [Handbook Home](./001-cover.md) | [Next: Navigation System](../navigation-system.md) →

**Last Updated:** July 2025 | **Section Maintainer:** [Deployment Team]
