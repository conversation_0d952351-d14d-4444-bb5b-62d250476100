---
type: "always_apply"
description: "Example description"
---
### 1. Core Development Principles

* **Adherence to SOLID Principles:** All code changes and new implementations must strictly follow SOLID principles (Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion).
* **DRY (Don't Repeat Yourself) Principle:** AI Agents must identify and flag any code duplication, ensuring that common logic is abstracted and reused across backend and frontend code.
* **KISS (Keep It Simple, Stupid) Principle:** Promote simplicity in design and implementation. Code should be as straightforward as possible to understand and maintain in all parts of the system.
* **TDD (Test-Driven Development):** For every new feature or bug fix, tests must be written *before* the implementation code for both backend and frontend components.

### 2. Architectural & Design Rules

* **5-Layer Architecture Pattern Compliance (Backend) & Component-Based Architecture (Frontend):**
    * Backend components and features must be designed and implemented strictly according to the defined 5-layer architecture, ensuring clear separation of concerns.
    * Frontend components must adhere to a **Component-Based Architecture**, specifically following the **Atomic Design Methodology** and **Single Responsibility Principle (SRP)** for components.
* **Unified Patterns Enforcement:** Mandate the consistent application of "unified patterns" for calculations, service layers, and repositories (backend). For frontend, this includes consistent patterns for **Data Fetching & Caching (React Query hooks)**, **State Management (Zustand)**, and **Error Handling**. This includes the mandatory use of decorators for error handling, performance monitoring, and memory optimization (backend), and similar patterns for cross-cutting concerns (frontend).
* **Unidirectional Data Flow:** Enforce a clear distinction and adherence to unidirectional data flow for both server state (managed by React Query) and client state (managed by Zustand/useState/useReducer) in the frontend.

### 3. Quality & Standards Rules (Zero-Tolerance Policies)

* **Zero Warnings/Errors Policy:** The AI Agent must ensure that all code it reviews or generates results in zero warnings or errors from linters (**Ruff** for Python, **ESLint** for Next.js/TypeScript) and compilers.
* **Zero Technical Debt Tolerance:** Any identified technical debt must be flagged and, where possible, remediated or documented for immediate attention, covering both backend and frontend code.
* **Zero Security Vulnerabilities:** Code must be free from security vulnerabilities. AI Agents should actively scan for and report potential security issues across both backend (e.g., API vulnerabilities) and frontend (e.g., XSS, CSRF, insecure token storage).
* **Complete Type Safety:** All Python code must pass **MyPy** validation with 100% type annotations, and all **TypeScript** code (frontend) must be fully type-checked without errors.
* **Immaculate Attention to Detail:** AI Agents must promote and verify meticulous coding, design, and documentation across the entire project. This includes pixel-perfect UI implementation and adherence to design specifications for the frontend.
* **Professional Electrical Design Standards Adherence:** All engineering calculations and design elements must comply with relevant IEEE, IEC, and EN standards (e.g., IEC-60079, IEC-61508, IEC-60364, IEC-60287, EN-50110, EN-60204, EN-50522). This applies to the logic and data handled by both backend calculations and their representation/interaction in the frontend.
* **Performance Optimization:** Implement code with performance in mind across both backend (efficient algorithms, database queries) and frontend (**Core Web Vitals**, code splitting, lazy loading, image optimization, memoization, virtualization).

### 4. Testing & Verification Rules

* **Comprehensive Testing:** AI Agents should ensure that all new implementations target 100% code coverage and contribute to achieving overall test coverage of $\geq85\%$ for the codebase (both backend and frontend).
* **100% Test Pass Rates:** All automated tests (unit, integration, end-to-end) must pass. AI Agents should prevent merging of code with failing tests, covering **Pytest** for backend and **Vitest, React Testing Library, Playwright** for frontend.
* **Verification against Requirements:** During the "Verification" phase of any task, AI Agents must ensure that all defined requirements are met through comprehensive testing and compliance verification, encompassing both functional and non-functional requirements (e.g., UI responsiveness, accessibility).

### 5. Documentation Rules

* **All Public APIs and Component Interfaces Documented:** Every public API endpoint (backend) and reusable component interface (frontend) must be fully documented using tools like **JSDoc/TypeDoc** for TypeScript.
* **Engineering-Grade Documentation:** Documentation generated or reviewed by AI Agents must adhere to the high standard of "engineering-grade precision" and clarity across all project components (backend, frontend, engineering calculations).
* **Comprehensive Documentation & Handover Package:** For every completed task, comprehensive documentation and a handover package (including for future AI agent transfer) must be prepared for both backend and frontend components. This includes updating the "Developer Handbook" and "Frontend Specification".
