openapi: 3.0.3
info:
  title: Advanced Component Management API
  description: |
    Advanced component management features including sophisticated search capabilities,
    enhanced bulk operations, and performance optimization tools.
    
    ## Features
    - **Advanced Search**: Complex filtering with specification-based queries
    - **Bulk Operations**: Enhanced batch processing with validation
    - **Performance Optimization**: Caching and query optimization
    
  version: 1.0.0
  contact:
    name: Ultimate Electrical Designer API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.ultimate-electrical-designer.com/v1
    description: Production server
  - url: https://staging-api.ultimate-electrical-designer.com/v1
    description: Staging server
  - url: http://localhost:8000/api/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  /components/search/advanced:
    post:
      tags:
        - Advanced Search
      summary: Advanced Component Search
      description: |
        Perform advanced component search with complex filtering capabilities including:
        - Specification-based filtering with dot notation
        - Range queries for numeric values
        - Boolean logic operators (AND, OR, NOT)
        - Fuzzy text matching
        - Relevance scoring
      operationId: advancedComponentSearch
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComponentAdvancedSearchRequest'
            examples:
              basic_search:
                summary: Basic search with filters
                value:
                  search_term: "circuit breaker"
                  basic_filters:
                    - field: "manufacturer"
                      operator: "eq"
                      value: "Schneider Electric"
              specification_search:
                summary: Specification-based search
                value:
                  specification_filters:
                    - path: "electrical.voltage_rating"
                      operator: "gte"
                      value: 120
                      data_type: "number"
              range_search:
                summary: Price range search
                value:
                  range_filters:
                    - field: "unit_price"
                      min_value: 10.0
                      max_value: 100.0
      responses:
        '200':
          description: Search completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentAdvancedSearchResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /components/search/relevance:
    get:
      tags:
        - Advanced Search
      summary: Relevance-Based Component Search
      description: |
        Search components with relevance scoring and ranking based on match quality.
        Results are automatically ranked by relevance score.
      operationId: relevanceComponentSearch
      parameters:
        - name: search_term
          in: query
          description: Text to search for
          required: true
          schema:
            type: string
            minLength: 2
            maxLength: 200
        - name: search_fields
          in: query
          description: Comma-separated list of fields to search
          required: false
          schema:
            type: string
            example: "name,description,manufacturer,part_number"
        - name: fuzzy
          in: query
          description: Enable fuzzy matching
          required: false
          schema:
            type: boolean
            default: false
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Relevance search completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentAdvancedSearchResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /components/bulk/create-validated:
    post:
      tags:
        - Bulk Operations
      summary: Enhanced Bulk Create Components
      description: |
        Create multiple components with comprehensive validation including:
        - Duplicate detection and handling
        - Batch processing for performance
        - Detailed error reporting
        - Transaction safety
      operationId: enhancedBulkCreateComponents
      parameters:
        - name: validate_duplicates
          in: query
          description: Check for duplicate components
          required: false
          schema:
            type: boolean
            default: true
        - name: batch_size
          in: query
          description: Batch size for processing
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 500
            default: 100
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ComponentCreateRequest'
              minItems: 1
              maxItems: 1000
            example:
              - manufacturer: "Schneider Electric"
                model_number: "QO120"
                name: "Circuit Breaker 20A"
                description: "Single pole circuit breaker"
                category: "PROTECTION"
                component_type: "CIRCUIT_BREAKER"
                unit_price: 25.99
                specifications:
                  electrical:
                    voltage_rating: 120
                    current_rating: 20
                    poles: 1
      responses:
        '201':
          description: Bulk creation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkOperationResult'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /components/bulk/update-selective:
    put:
      tags:
        - Bulk Operations
      summary: Selective Bulk Update Components
      description: |
        Update multiple components with different data for each component.
        Each update object should include an 'id' field and the fields to update.
      operationId: selectiveBulkUpdateComponents
      parameters:
        - name: batch_size
          in: query
          description: Batch size for processing
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 500
            default: 100
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ComponentSelectiveUpdateRequest'
              minItems: 1
              maxItems: 1000
            example:
              - id: 1
                unit_price: 28.99
                is_preferred: true
              - id: 2
                description: "Updated description"
                specifications:
                  electrical:
                    voltage_rating: 240
      responses:
        '200':
          description: Bulk update completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkOperationResult'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /components/bulk/delete:
    delete:
      tags:
        - Bulk Operations
      summary: Bulk Delete Components
      description: |
        Delete multiple components with options for soft or hard delete.
        Soft delete preserves data while marking as deleted.
      operationId: bulkDeleteComponents
      parameters:
        - name: soft_delete
          in: query
          description: Perform soft delete (default) or hard delete
          required: false
          schema:
            type: boolean
            default: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
                format: int64
              minItems: 1
              maxItems: 1000
            example: [1, 2, 3, 4, 5]
      responses:
        '200':
          description: Bulk deletion completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkDeleteResult'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /components/performance/metrics:
    get:
      tags:
        - Performance Optimization
      summary: Get Performance Metrics
      description: |
        Get comprehensive performance metrics for the component system including:
        - Component statistics and counts
        - Cache performance metrics
        - Database query performance
        - System health indicators
      operationId: getPerformanceMetrics
      responses:
        '200':
          description: Performance metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceMetrics'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /components/performance/optimize:
    post:
      tags:
        - Performance Optimization
      summary: Optimize System Performance
      description: |
        Perform system performance optimization tasks including:
        - Cache warming for popular searches
        - Cache cleanup and maintenance
        - Database index analysis
        - Query optimization recommendations
      operationId: optimizeSystemPerformance
      responses:
        '200':
          description: Performance optimization completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OptimizationResult'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /components/cache/invalidate:
    delete:
      tags:
        - Performance Optimization
      summary: Invalidate Component Cache
      description: |
        Invalidate component cache entries for better performance.
        Can target specific component or all component-related cache.
      operationId: invalidateComponentCache
      parameters:
        - name: component_id
          in: query
          description: Specific component ID to invalidate
          required: false
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Cache invalidation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheInvalidationResult'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ComponentAdvancedSearchRequest:
      type: object
      properties:
        search_term:
          type: string
          maxLength: 200
          description: Text search across multiple fields
        search_fields:
          type: array
          items:
            type: string
          description: Specific fields to search in
        fuzzy_search:
          type: boolean
          default: false
          description: Enable fuzzy text matching
        basic_filters:
          type: array
          items:
            $ref: '#/components/schemas/AdvancedFilter'
          description: List of basic field filters
        specification_filters:
          type: array
          items:
            $ref: '#/components/schemas/SpecificationFilter'
          description: List of specification-based filters
        range_filters:
          type: array
          items:
            $ref: '#/components/schemas/RangeFilter'
          description: List of range filters
        price_range:
          type: object
          properties:
            min_price:
              type: number
              format: decimal
            max_price:
              type: number
              format: decimal
            currency:
              type: string
              default: "USD"
          description: Price range filter with currency support
        sort_by:
          type: string
          default: "name"
          description: Field to sort by
        sort_order:
          type: string
          enum: ["asc", "desc"]
          default: "asc"
          description: Sort order
        include_inactive:
          type: boolean
          default: false
          description: Include inactive components
        include_deleted:
          type: boolean
          default: false
          description: Include soft-deleted components

    AdvancedFilter:
      type: object
      required:
        - field
        - operator
        - value
      properties:
        field:
          type: string
          description: Field name to filter on
        operator:
          $ref: '#/components/schemas/FilterOperator'
        value:
          description: Filter value
        logical_operator:
          $ref: '#/components/schemas/LogicalOperator'

    SpecificationFilter:
      type: object
      required:
        - path
        - operator
        - value
      properties:
        path:
          type: string
          description: Dot notation path in specifications JSON
          example: "electrical.voltage_rating"
        operator:
          $ref: '#/components/schemas/FilterOperator'
        value:
          description: Filter value
        data_type:
          type: string
          enum: ["string", "number", "boolean", "array"]
          default: "string"
          description: Data type for proper casting
        unit:
          type: string
          description: Unit for conversion (if applicable)
        logical_operator:
          $ref: '#/components/schemas/LogicalOperator'

    RangeFilter:
      type: object
      required:
        - field
      properties:
        field:
          type: string
          description: Field name to filter on
        min_value:
          type: number
          description: Minimum value (inclusive by default)
        max_value:
          type: number
          description: Maximum value (inclusive by default)
        include_min:
          type: boolean
          default: true
          description: Whether to include minimum value
        include_max:
          type: boolean
          default: true
          description: Whether to include maximum value

    FilterOperator:
      type: string
      enum:
        - eq
        - ne
        - gt
        - gte
        - lt
        - lte
        - contains
        - starts_with
        - ends_with
        - in
        - not_in
        - between
        - fuzzy
        - regex
        - is_null
        - is_not_null
      description: Filter operator

    LogicalOperator:
      type: string
      enum: [and, or, not]
      default: and
      description: Logical operator for combining filters

    ComponentAdvancedSearchResponse:
      type: object
      required:
        - items
        - pagination
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ComponentSearchResult'
          description: Search results
        pagination:
          $ref: '#/components/schemas/PaginationInfo'
        search_metadata:
          type: object
          properties:
            query_time:
              type: string
              description: Query execution time
            total_filters_applied:
              type: integer
              description: Number of filters applied
            search_type:
              type: string
              description: Type of search performed
            fuzzy_search_enabled:
              type: boolean
              description: Whether fuzzy search was enabled
          description: Search metadata
        suggestions:
          type: array
          items:
            type: string
          description: Search suggestions for refinement

    ComponentSearchResult:
      type: object
      required:
        - component
      properties:
        component:
          $ref: '#/components/schemas/Component'
        relevance_score:
          type: number
          format: float
          minimum: 0.0
          maximum: 1.0
          description: Search relevance score
        matched_fields:
          type: array
          items:
            type: string
          description: Fields that matched the search criteria

    Component:
      type: object
      required:
        - id
        - manufacturer
        - model_number
        - name
        - category
        - component_type
      properties:
        id:
          type: integer
          format: int64
        manufacturer:
          type: string
        model_number:
          type: string
        name:
          type: string
        description:
          type: string
        category:
          type: string
        component_type:
          type: string
        unit_price:
          type: number
          format: decimal
        specifications:
          type: object
          description: Component specifications in JSON format
        is_active:
          type: boolean
        is_deleted:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ComponentCreateRequest:
      type: object
      required:
        - manufacturer
        - model_number
        - name
        - category
        - component_type
      properties:
        manufacturer:
          type: string
        model_number:
          type: string
        name:
          type: string
        description:
          type: string
        category:
          type: string
        component_type:
          type: string
        unit_price:
          type: number
          format: decimal
        specifications:
          type: object
          description: Component specifications in JSON format

    ComponentSelectiveUpdateRequest:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
          description: Component ID to update
      additionalProperties: true
      description: Update object with component ID and fields to update

    BulkOperationResult:
      type: object
      required:
        - total_processed
        - success_rate
      properties:
        total_processed:
          type: integer
          description: Total number of items processed
        created:
          type: integer
          description: Number of items created (for create operations)
        updated:
          type: integer
          description: Number of items updated (for update operations)
        errors:
          type: integer
          description: Number of errors encountered
        success_rate:
          type: number
          format: float
          minimum: 0.0
          maximum: 1.0
          description: Success rate (0.0 to 1.0)
        created_components:
          type: array
          items:
            $ref: '#/components/schemas/Component'
          description: Created components (for create operations)
        validation_errors:
          type: array
          items:
            type: object
            properties:
              index:
                type: integer
                description: Index of the item that failed
              error:
                type: string
                description: Error message
              data:
                type: object
                description: Original data that failed
          description: Validation errors encountered

    BulkDeleteResult:
      type: object
      required:
        - total_processed
        - deleted
        - not_found
        - success_rate
        - delete_type
      properties:
        total_processed:
          type: integer
          description: Total number of items processed
        deleted:
          type: integer
          description: Number of items deleted
        not_found:
          type: integer
          description: Number of items not found
        success_rate:
          type: number
          format: float
          minimum: 0.0
          maximum: 1.0
          description: Success rate (0.0 to 1.0)
        not_found_ids:
          type: array
          items:
            type: integer
            format: int64
          description: IDs of items that were not found
        delete_type:
          type: string
          enum: [soft, hard]
          description: Type of delete performed

    PerformanceMetrics:
      type: object
      properties:
        component_statistics:
          type: object
          properties:
            total_components:
              type: integer
            active_components:
              type: integer
            preferred_components:
              type: integer
            unique_manufacturers:
              type: integer
            unique_categories:
              type: integer
            price_statistics:
              type: object
              properties:
                average:
                  type: number
                  format: decimal
                minimum:
                  type: number
                  format: decimal
                maximum:
                  type: number
                  format: decimal
        cache_performance:
          type: object
          properties:
            memory_cache_size:
              type: integer
            memory_cache_max_size:
              type: integer
            redis_connected:
              type: boolean
            redis_used_memory:
              type: string
            redis_connected_clients:
              type: integer
            redis_total_commands_processed:
              type: integer
        query_performance:
          type: object
          properties:
            slow_queries_count:
              type: integer
            slowest_query_time:
              type: number
              format: float
            index_usage:
              type: object
        system_health:
          type: object
          properties:
            database_connected:
              type: boolean
            cache_connected:
              type: boolean
            performance_monitoring_active:
              type: boolean

    OptimizationResult:
      type: object
      properties:
        cache_warming:
          type: boolean
          description: Whether cache warming was successful
        cache_cleanup:
          type: boolean
          description: Whether cache cleanup was successful
        index_analysis:
          type: boolean
          description: Whether index analysis was successful
        query_optimization:
          type: boolean
          description: Whether query optimization was successful
        errors:
          type: array
          items:
            type: string
          description: Any errors encountered during optimization
        index_suggestions:
          type: array
          items:
            type: string
          description: Suggested database indexes for optimization

    CacheInvalidationResult:
      type: object
      required:
        - success
        - scope
        - timestamp
      properties:
        success:
          type: boolean
          description: Whether invalidation was successful
        component_id:
          type: integer
          format: int64
          description: Specific component ID invalidated (if applicable)
        scope:
          type: string
          enum: [specific, all]
          description: Scope of invalidation
        timestamp:
          type: string
          format: date-time
          description: Timestamp of invalidation

    PaginationInfo:
      type: object
      required:
        - page
        - per_page
        - total
        - pages
      properties:
        page:
          type: integer
          minimum: 1
          description: Current page number
        per_page:
          type: integer
          minimum: 1
          description: Number of items per page
        total:
          type: integer
          minimum: 0
          description: Total number of items
        pages:
          type: integer
          minimum: 0
          description: Total number of pages

    Error:
      type: object
      required:
        - detail
      properties:
        detail:
          type: string
          description: Error description
        error_code:
          type: string
          description: Machine-readable error code
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        request_id:
          type: string
          description: Unique request identifier

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Invalid request parameters"
            error_code: "BAD_REQUEST"
            timestamp: "2024-01-01T00:00:00Z"
            request_id: "req_123456789"

    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Authentication required"
            error_code: "AUTHENTICATION_REQUIRED"
            timestamp: "2024-01-01T00:00:00Z"
            request_id: "req_123456789"

    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Request validation failed"
            error_code: "VALIDATION_ERROR"
            timestamp: "2024-01-01T00:00:00Z"
            request_id: "req_123456789"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            detail: "Internal server error"
            error_code: "INTERNAL_SERVER_ERROR"
            timestamp: "2024-01-01T00:00:00Z"
            request_id: "req_123456789"

tags:
  - name: Advanced Search
    description: Advanced search capabilities with complex filtering
  - name: Bulk Operations
    description: Enhanced bulk operations with validation and error handling
  - name: Performance Optimization
    description: Performance monitoring and optimization tools
