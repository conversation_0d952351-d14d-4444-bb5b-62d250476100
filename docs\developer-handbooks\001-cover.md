# Ultimate Electrical Designer - Developer Handbook

**Version:** 1.0  
**Last Updated:** July 2025  
**Architecture:** 5-Layer Pattern with Unified Error Handling  
**Standards:** IEC/EN Compliance  

## Overview

This comprehensive developer handbook provides complete guidance for developing, maintaining, and extending the Ultimate Electrical Designer platform. The handbook follows engineering-grade documentation practices with immaculate attention to detail and maintains a single source of truth for all development workflows.

## Quick Navigation

### 🚀 Getting Started
- **[Project Introduction](011-introduction.md)** - Architecture overview and engineering standards
- **[Getting Started](020-getting-started.md)** - Installation, setup, and onboarding
- **[Development Standards](030-development-standards.md)** - IEEE/IEC/EN compliance and policies

### 🏗️ Core Development
- **[Unified Patterns](040-unified-patterns.md)** - Error handling, monitoring, and security decorators
- **[Backend Development](050-backend-development.md)** - 5-layer architecture and CRUD patterns
- **[Frontend Transition](060-frontend-transition.md)** - Integration guides and architecture preparation

### 🧪 Quality & Operations
- **[Testing Framework](070-testing-framework.md)** - 5-phase methodology and coverage targets
- **[Script Ecosystem](080-script-ecosystem.md)** - Makefile commands and automation tools
- **[Component Models](090-component-models.md)** - Electrical design and heat tracing
- **[Database Management](100-database-management.md)** - Schema, migrations, and testing

## Key Features

### 🎯 **Engineering-Grade Standards**
- Zero tolerance for 'however' scenarios
- Single source of truth per calculation type
- IEC/EN standards compliance
- Immaculate attention to detail with robust patterns

### 🏗️ **5-Layer Architecture**
- **API Layer:** FastAPI routes and HTTP handling
- **Service Layer:** Business logic and orchestration  
- **Repository Layer:** Data access abstraction
- **Schema Layer:** Pydantic validation models
- **Model Layer:** SQLAlchemy ORM models

### 🔧 **Unified Patterns System**
- `@handle_database_errors` - Repository layer error handling
- `@handle_service_errors` - Service layer error handling
- `@handle_api_errors` - API layer error handling
- `@handle_calculation_errors` - Calculation engine error handling
- `@monitor_performance` - Performance monitoring decorators

### 🧪 **5-Phase Testing Methodology**
1. **Discovery & Analysis** - Understand requirements and current state
2. **Task Planning** - Break down work into ~20-minute units
3. **Implementation** - Execute with unified patterns compliance
4. **Verification** - Test coverage targets (90%+ critical, 85%+ high priority)
5. **Documentation** - Update handbook and maintain single source of truth

## Development Workflows

### Quick Start Commands
```bash
# Complete development setup
make dev-setup

# Start development server
make run-dev

# Run comprehensive tests
make test

# Check code quality
make quality

# Verify unified patterns compliance
python scripts/inventory_analyzer.py unified-patterns
```

### Documentation Standards
- All sections use relative links for cross-referencing
- Code examples wrapped in proper markdown syntax
- Consistent section templates for expandability
- Integration with existing backend and frontend documentation

## Target Audience

### **New Developers**
- Complete onboarding workflow from installation to first contribution
- Architecture understanding and development standards
- Testing methodology and quality requirements

### **Experienced Developers**
- Advanced patterns and architectural decisions
- Performance optimization and monitoring
- Standards compliance and engineering requirements

### **Frontend Developers**
- Backend integration guides and API documentation
- Transition preparation and architecture understanding
- Component models and electrical design concepts

### **DevOps Engineers**
- Deployment workflows and automation tools
- Database management and migration procedures
- Monitoring and performance optimization

## Integration Points

### **Backend Documentation**
- Links to `docs/specifications/100-backend-specification.md` for backend specification
- References to `docs/developer-handbooks/backend/` for backend development guides
- Integration with `docs/developer-handbooks/frontend-transition/` for transitioning frontend development

### **Frontend Documentation**
- Links to `docs/specifications/200-frontend-specification.md` for frontend specification
- Connects to `docs/developer-handbooks/frontend-transition/003-frontend-development-roadmap.md` for transition plan
- References `docs/developer-handbooks/frontend/frontend-typing-handbook/` for TypeScript guides
- References `docs/developer-handbooks/frontend/frontend-developer-handbook/` for frontend development guides

### **Database Integration**
- References project database at `server/data/app_dev.db`
- Migration procedures and schema documentation
- Real database testing requirements (NO mocks)

## Contributing to the Handbook

### Adding New Sections
1. Follow the standardized template in `templates/section-template.md`
2. Maintain consistent cross-referencing patterns
3. Update main index and navigation links
4. Ensure engineering-grade documentation standards

### Updating Existing Content
1. Maintain single source of truth principle
2. Update all cross-references when moving content
3. Follow immaculate attention to detail standards
4. Test all links and code examples

### Quality Standards
- **Accuracy:** All information must be current and tested
- **Completeness:** Cover all aspects of the topic thoroughly
- **Consistency:** Follow established patterns and formatting
- **Clarity:** Write for the intended audience level

---

**Next Steps:** Start with [Project Introduction](011-introduction.md) for architecture overview, or jump to [Getting Started](020-getting-started.md) for immediate setup.

**Support:** For questions about this handbook, refer to the specific section documentation or consult the engineering team.
