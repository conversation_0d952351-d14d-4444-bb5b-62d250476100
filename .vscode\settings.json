{"// Ultimate Electrical Designer - VS Code Configuration": "", "// Optimized for type safety development with SQLAlchemy workarounds": "", "// Python Configuration": "", "python.defaultInterpreterPath": "./server/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "// Type Checking Configuration": "", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "python.linting.banditEnabled": true, "// MyPy Configuration": "", "python.linting.mypyArgs": ["--config-file=mypy.ini", "--show-error-codes", "--ignore-missing-imports", "--no-error-summary"], "// Flake8 Configuration": "", "python.linting.flake8Args": ["--max-line-length=88", "--extend-ignore=E203,W503"], "// Bandit Configuration": "", "python.linting.banditArgs": ["-r", "server/src"], "// Formatting Configuration": "", "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=88"], "// Import Sorting": "", "python.sortImports.args": ["--profile=black", "--line-length=88"], "// Auto-formatting on save": "", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "// File associations": "", "files.associations": {"*.py": "python", "*.pyi": "python", "mypy.ini": "ini", ".pre-commit-config.yaml": "yaml"}, "// Workspace-specific settings": "", "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.mypy_cache": true, "**/.pytest_cache": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "// Search configuration": "", "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/__pycache__": true, "**/.mypy_cache": true, "**/.pytest_cache": true}, "// Terminal configuration": "", "terminal.integrated.cwd": "./server", "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/server/src"}, "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}/server/src"}, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}/server/src"}, "// Language-specific settings": "", "[python]": {"editor.tabSize": 4, "editor.insertSpaces": true, "editor.rulers": [88], "editor.wordWrap": "off"}, "[yaml]": {"editor.tabSize": 2, "editor.insertSpaces": true}, "[json]": {"editor.tabSize": 2, "editor.insertSpaces": true}, "[markdown]": {"editor.wordWrap": "on", "editor.rulers": [80]}, "// Extension-specific settings": "", "pylint.args": ["--disable=C0111"], "// Type checking exclusions for SQLAlchemy issues": "", "python.analysis.exclude": ["**/src/core/models/**", "**/src/core/repositories/**", "**/src/core/services/**", "**/src/api/**"], "// Intellisense configuration": "", "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "python.analysis.completeFunctionParens": true, "// Git configuration": "", "git.ignoreLimitWarning": true, "// Task configuration": "", "tasks.version": "2.0.0"}