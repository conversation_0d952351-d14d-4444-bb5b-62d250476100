{"Name": "ultimate-electrical-designer", "Children": [{"Name": "cad-integrator-service", "Children": [{"Name": "api", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\api", "Type": "Folder"}, {"Name": "plugins", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\plugins", "Type": "Folder"}, {"Name": "src", "Children": [{"Name": "Controllers", "Children": [{"Name": "CadController.cs", "Extension": ".cs", "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Controllers\\CadController.cs", "Type": "File", "SizeKB": 0.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Controllers", "Type": "Folder"}, {"Name": "Services", "Children": [{"Name": "AutoCADService.cs", "Extension": ".cs", "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Services\\AutoCADService.cs", "Type": "File", "SizeKB": 0.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Services", "Type": "Folder"}, {"Name": "ultimate_electrical_designer.CadIntegrator.csproj", "Extension": ".c<PERSON><PERSON>j", "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\ultimate_electrical_designer.CadIntegrator.csproj", "Type": "File", "SizeKB": 0.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src", "Type": "Folder"}, {"Name": "Dockerfile", "Extension": "", "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\Dockerfile", "Type": "File", "SizeKB": 0.0}, {"Name": "README.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\README.md", "Type": "File", "SizeKB": 1.4}], "Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service", "Type": "Folder"}, {"Name": "client", "Children": [{"Name": "src", "Children": [{"Name": "app", "Children": [{"Name": "(auth)", "Children": [{"Name": "login", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\login", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)", "Type": "Folder"}, {"Name": "api", "Children": [{"Name": "auth", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\api\\auth", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\api", "Type": "Folder"}, {"Name": "dashboard", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\dashboard", "Type": "Folder"}, {"Name": "README.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\README.md", "Type": "File", "SizeKB": 4.98}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app", "Type": "Folder"}, {"Name": "assets", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\assets", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "common", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\index.ts.txt", "Type": "File", "SizeKB": 0.25}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common", "Type": "Folder"}, {"Name": "domain", "Children": [{"Name": "project", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain\\project", "Type": "Folder"}, {"Name": "user", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain\\user", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain\\index.ts.txt", "Type": "File", "SizeKB": 0.27}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain", "Type": "Folder"}, {"Name": "ui", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\index.ts.txt", "Type": "File", "SizeKB": 0.23}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.27}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks", "Type": "Folder"}, {"Name": "lib", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\index.ts.txt", "Type": "File", "SizeKB": 0.28}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib", "Type": "Folder"}, {"Name": "modules", "Children": [{"Name": "cable_sizing", "Children": [{"Name": "api", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\api\\index.ts.txt", "Type": "File", "SizeKB": 0.22}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\api", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.2}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.15}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\hooks", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\index.ts.txt", "Type": "File", "SizeKB": 0.42}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing", "Type": "Folder"}, {"Name": "circuits", "Children": [{"Name": "api", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\api\\index.ts.txt", "Type": "File", "SizeKB": 0.22}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\api", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.2}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.15}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\hooks", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\index.ts.txt", "Type": "File", "SizeKB": 0.42}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "api", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api\\index.ts.txt", "Type": "File", "SizeKB": 0.22}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.2}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.15}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.42}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components", "Type": "Folder"}, {"Name": "heat_tracing", "Children": [{"Name": "api", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\api\\index.ts.txt", "Type": "File", "SizeKB": 0.22}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\api", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.2}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.15}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\hooks", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\index.ts.txt", "Type": "File", "SizeKB": 0.42}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing", "Type": "Folder"}, {"Name": "load_calculations", "Children": [{"Name": "api", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\api\\index.ts.txt", "Type": "File", "SizeKB": 0.22}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\api", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.2}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.15}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\hooks", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\index.ts.txt", "Type": "File", "SizeKB": 0.42}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations", "Type": "Folder"}, {"Name": "projects", "Children": [{"Name": "api", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\api\\index.ts.txt", "Type": "File", "SizeKB": 0.22}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\api", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.2}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.15}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\hooks", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\index.ts.txt", "Type": "File", "SizeKB": 0.42}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects", "Type": "Folder"}, {"Name": "users", "Children": [{"Name": "api", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\api\\index.ts.txt", "Type": "File", "SizeKB": 0.22}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\api", "Type": "Folder"}, {"Name": "components", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\components\\index.ts.txt", "Type": "File", "SizeKB": 0.2}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\components", "Type": "Folder"}, {"Name": "hooks", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\hooks\\index.ts.txt", "Type": "File", "SizeKB": 0.15}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\hooks", "Type": "Folder"}, {"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\index.ts.txt", "Type": "File", "SizeKB": 0.42}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules", "Type": "Folder"}, {"Name": "services", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\services\\index.ts.txt", "Type": "File", "SizeKB": 0.31}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\services", "Type": "Folder"}, {"Name": "styles", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\styles", "Type": "Folder"}, {"Name": "types", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types\\index.ts.txt", "Type": "File", "SizeKB": 0.26}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types", "Type": "Folder"}, {"Name": "utils", "Children": [{"Name": "index.ts.txt", "Extension": ".txt", "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\utils\\index.ts.txt", "Type": "File", "SizeKB": 0.24}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\utils", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\client", "Type": "Folder"}, {"Name": "computation-engine-service", "Children": [{"Name": "api", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\api", "Type": "Folder"}, {"Name": "plugins", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\plugins", "Type": "Folder"}, {"Name": "src", "Children": [{"Name": "Controllers", "Children": [{"Name": "ComputationController.cs", "Extension": ".cs", "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Controllers\\ComputationController.cs", "Type": "File", "SizeKB": 0.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Controllers", "Type": "Folder"}, {"Name": "Services", "Children": [{"Name": "PowerFlowSolver.cs", "Extension": ".cs", "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Services\\PowerFlowSolver.cs", "Type": "File", "SizeKB": 0.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Services", "Type": "Folder"}, {"Name": "ultimate_electrical_designer.ComputationEngine.csproj", "Extension": ".c<PERSON><PERSON>j", "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\ultimate_electrical_designer.ComputationEngine.csproj", "Type": "File", "SizeKB": 0.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src", "Type": "Folder"}, {"Name": "Dockerfile", "Extension": "", "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\Dockerfile", "Type": "File", "SizeKB": 0.0}, {"Name": "README.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\README.md", "Type": "File", "SizeKB": 1.17}], "Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service", "Type": "Folder"}, {"Name": "docs", "Children": [{"Name": "999-tasks", "Children": [{"Name": "completed", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\999-tasks\\completed", "Type": "Folder"}, {"Name": "type-safety-next-steps-recommendations.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\999-tasks\\type-safety-next-steps-recommendations.md", "Type": "File", "SizeKB": 7.56}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\999-tasks", "Type": "Folder"}, {"Name": "ai-agent-team", "Children": [{"Name": "agent-implementation-guides.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\agent-implementation-guides.md", "Type": "File", "SizeKB": 20.96}, {"Name": "agent-training.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\agent-training.md", "Type": "File", "SizeKB": 21.14}, {"Name": "coordination-protocols.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\coordination-protocols.md", "Type": "File", "SizeKB": 12.04}, {"Name": "framework-summary.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\framework-summary.md", "Type": "File", "SizeKB": 9.41}, {"Name": "index.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\index.md", "Type": "File", "SizeKB": 10.21}, {"Name": "performance-monitoring.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\performance-monitoring.md", "Type": "File", "SizeKB": 17.86}, {"Name": "quality-assurance.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\quality-assurance.md", "Type": "File", "SizeKB": 14.22}, {"Name": "README.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\README.md", "Type": "File", "SizeKB": 22.62}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team", "Type": "Folder"}, {"Name": "calculations", "Children": [{"Name": "001-electrical-calculations.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\calculations\\001-electrical-calculations.md", "Type": "File", "SizeKB": 47.47}, {"Name": "002-heat-loss-calculations.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\calculations\\002-heat-loss-calculations.md", "Type": "File", "SizeKB": 27.33}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\calculations", "Type": "Folder"}, {"Name": "developer-handbooks", "Children": [{"Name": "backend", "Children": [{"Name": "000-backend-specification.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\backend\\000-backend-specification.md", "Type": "File", "SizeKB": 19.82}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\backend", "Type": "Folder"}, {"Name": "frontend", "Children": [{"Name": "frontend-developer-handbook", "Children": [{"Name": "001-frontend-development-handbook.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\001-frontend-development-handbook.md", "Type": "File", "SizeKB": 13.74}, {"Name": "002-ui-implementation-guide.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\002-ui-implementation-guide.md", "Type": "File", "SizeKB": 22.06}, {"Name": "003-linting-guide.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\003-linting-guide.md", "Type": "File", "SizeKB": 14.54}, {"Name": "004-documentation-guide.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\004-documentation-guide.md", "Type": "File", "SizeKB": 13.49}, {"Name": "005-state-management.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\005-state-management.md", "Type": "File", "SizeKB": 4.87}, {"Name": "006-data-fetching-api.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\006-data-fetching-api.md", "Type": "File", "SizeKB": 4.86}, {"Name": "007-testing.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\007-testing.md", "Type": "File", "SizeKB": 2.99}, {"Name": "008-routing-navigation.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\008-routing-navigation.md", "Type": "File", "SizeKB": 2.53}, {"Name": "009-performance-optimization.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\009-performance-optimization.md", "Type": "File", "SizeKB": 3.17}, {"Name": "010-deployment-environment.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\010-deployment-environment.md", "Type": "File", "SizeKB": 2.88}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook", "Type": "Folder"}, {"Name": "frontend-typing-handbook", "Children": [{"Name": "001-typescript-handbook.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\001-typescript-handbook.md", "Type": "File", "SizeKB": 6.78}, {"Name": "002-core-typescript-concepts.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\002-core-typescript-concepts.md", "Type": "File", "SizeKB": 8.98}, {"Name": "003-typing-react-components-props.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\003-typing-react-components-props.md", "Type": "File", "SizeKB": 6.0}, {"Name": "004-typing-react-hooks.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\004-typing-react-hooks.md", "Type": "File", "SizeKB": 10.23}, {"Name": "005-typing-state-management.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\005-typing-state-management.md", "Type": "File", "SizeKB": 5.56}, {"Name": "006-typing-data-fetching-api-interaction.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\006-typing-data-fetching-api-interaction.md", "Type": "File", "SizeKB": 10.1}, {"Name": "007-typing-routing.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\007-typing-routing.md", "Type": "File", "SizeKB": 7.06}, {"Name": "008-typing-forms-validation.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\008-typing-forms-validation.md", "Type": "File", "SizeKB": 10.65}, {"Name": "009-typing-utility-functions-advanced-patterns.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\009-typing-utility-functions-advanced-patterns.md", "Type": "File", "SizeKB": 8.87}, {"Name": "010-typing-testing.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\010-typing-testing.md", "Type": "File", "SizeKB": 7.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook", "Type": "Folder"}, {"Name": "000-frontend-specification.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\000-frontend-specification.md", "Type": "File", "SizeKB": 38.26}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend", "Type": "Folder"}, {"Name": "frontend-transition", "Children": [{"Name": "001-backend-analysis-guide.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition\\001-backend-analysis-guide.md", "Type": "File", "SizeKB": 8.81}, {"Name": "002-preparing-backend-workflows.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition\\002-preparing-backend-workflows.md", "Type": "File", "SizeKB": 9.29}, {"Name": "003-frontend-development-roadmap.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition\\003-frontend-development-roadmap.md", "Type": "File", "SizeKB": 9.61}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition", "Type": "Folder"}, {"Name": "templates", "Children": [{"Name": "getting-started-template.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\getting-started-template.md", "Type": "File", "SizeKB": 2.34}, {"Name": "reference-template.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\reference-template.md", "Type": "File", "SizeKB": 5.75}, {"Name": "section-template.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\section-template.md", "Type": "File", "SizeKB": 5.12}, {"Name": "technical-guide-template.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\technical-guide-template.md", "Type": "File", "SizeKB": 5.26}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates", "Type": "Folder"}, {"Name": "000-cover-index.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\000-cover-index.md", "Type": "File", "SizeKB": 11.03}, {"Name": "001-cover.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\001-cover.md", "Type": "File", "SizeKB": 6.0}, {"Name": "002-navigation.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\002-navigation.md", "Type": "File", "SizeKB": 10.83}, {"Name": "010-introduction-index.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\010-introduction-index.md", "Type": "File", "SizeKB": 2.67}, {"Name": "011-introduction.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\011-introduction.md", "Type": "File", "SizeKB": 16.94}, {"Name": "020-getting-started.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\020-getting-started.md", "Type": "File", "SizeKB": 11.4}, {"Name": "030-development-standards.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\030-development-standards.md", "Type": "File", "SizeKB": 17.07}, {"Name": "040-unified-patterns.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\040-unified-patterns.md", "Type": "File", "SizeKB": 16.84}, {"Name": "050-backend-development.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\050-backend-development.md", "Type": "File", "SizeKB": 72.76}, {"Name": "060-frontend-transition.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\060-frontend-transition.md", "Type": "File", "SizeKB": 21.94}, {"Name": "070-testing-framework.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\070-testing-framework.md", "Type": "File", "SizeKB": 20.4}, {"Name": "080-script-ecosystem.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\080-script-ecosystem.md", "Type": "File", "SizeKB": 13.89}, {"Name": "090-component-models.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\090-component-models.md", "Type": "File", "SizeKB": 25.81}, {"Name": "100-database-management.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\100-database-management.md", "Type": "File", "SizeKB": 20.96}, {"Name": "110-docker-dev-deployment.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\110-docker-dev-deployment.md", "Type": "File", "SizeKB": 22.29}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks", "Type": "Folder"}, {"Name": "001-development-roadmap.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\001-development-roadmap.md", "Type": "File", "SizeKB": 31.35}, {"Name": "002-robust-design-principles.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\002-robust-design-principles.md", "Type": "File", "SizeKB": 7.81}, {"Name": "003-implementation-methodology.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\003-implementation-methodology.md", "Type": "File", "SizeKB": 15.73}, {"Name": "004-methodology-template.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\004-methodology-template.md", "Type": "File", "SizeKB": 8.47}], "Path": "D:\\Projects\\ultimate-electrical-designer\\docs", "Type": "Folder"}, {"Name": "server", "Children": [{"Name": ".mypy_cache", "Children": [{"Name": "3.13", "Children": [{"Name": "_typeshed", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\_typeshed", "Type": "Folder"}, {"Name": "alembic", "Children": [{"Name": "autogenerate", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\alembic\\autogenerate", "Type": "Folder"}, {"Name": "ddl", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\alembic\\ddl", "Type": "Folder"}, {"Name": "operations", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\alembic\\operations", "Type": "Folder"}, {"Name": "runtime", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\alembic\\runtime", "Type": "Folder"}, {"Name": "script", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\alembic\\script", "Type": "Folder"}, {"Name": "util", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\alembic\\util", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\alembic", "Type": "Folder"}, {"Name": "annotated_types", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\annotated_types", "Type": "Folder"}, {"Name": "anyio", "Children": [{"Name": "_core", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\anyio\\_core", "Type": "Folder"}, {"Name": "abc", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\anyio\\abc", "Type": "Folder"}, {"Name": "streams", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\anyio\\streams", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\anyio", "Type": "Folder"}, {"Name": "asyncio", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\asyncio", "Type": "Folder"}, {"Name": "attr", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\attr", "Type": "Folder"}, {"Name": "attrs", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\attrs", "Type": "Folder"}, {"Name": "bcrypt", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\bcrypt", "Type": "Folder"}, {"Name": "certifi", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\certifi", "Type": "Folder"}, {"Name": "click", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\click", "Type": "Folder"}, {"Name": "collections", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\collections", "Type": "Folder"}, {"Name": "concurrent", "Children": [{"Name": "futures", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\concurrent\\futures", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\concurrent", "Type": "Folder"}, {"Name": "cryptography", "Children": [{"Name": "hazmat", "Children": [{"Name": "backends", "Children": [{"Name": "openssl", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\backends\\openssl", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\backends", "Type": "Folder"}, {"Name": "bindings", "Children": [{"Name": "_rust", "Children": [{"Name": "openssl", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\bindings\\_rust\\openssl", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\bindings\\_rust", "Type": "Folder"}, {"Name": "openssl", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\bindings\\openssl", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\bindings", "Type": "Folder"}, {"Name": "decrepit", "Children": [{"Name": "ciphers", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\decrepit\\ciphers", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\decrepit", "Type": "Folder"}, {"Name": "primitives", "Children": [{"Name": "asymmetric", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\primitives\\asymmetric", "Type": "Folder"}, {"Name": "ciphers", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\primitives\\ciphers", "Type": "Folder"}, {"Name": "serialization", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\primitives\\serialization", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat\\primitives", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\hazmat", "Type": "Folder"}, {"Name": "x509", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography\\x509", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\cryptography", "Type": "Folder"}, {"Name": "ctypes", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\ctypes", "Type": "Folder"}, {"Name": "dns", "Children": [{"Name": "quic", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\dns\\quic", "Type": "Folder"}, {"Name": "rdtypes", "Children": [{"Name": "ANY", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\dns\\rdtypes\\ANY", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\dns\\rdtypes", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\dns", "Type": "Folder"}, {"Name": "dotenv", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\dotenv", "Type": "Folder"}, {"Name": "email", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\email", "Type": "Folder"}, {"Name": "email_validator", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\email_validator", "Type": "Folder"}, {"Name": "encodings", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\encodings", "Type": "Folder"}, {"Name": "<PERSON><PERSON><PERSON>", "Children": [{"Name": "dependencies", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\fastapi\\dependencies", "Type": "Folder"}, {"Name": "openapi", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\fastapi\\openapi", "Type": "Folder"}, {"Name": "security", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\fastapi\\security", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\fastapi", "Type": "Folder"}, {"Name": "h11", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\h11", "Type": "Folder"}, {"Name": "html", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\html", "Type": "Folder"}, {"Name": "http", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\http", "Type": "Folder"}, {"Name": "httpcore", "Children": [{"Name": "_async", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\httpcore\\_async", "Type": "Folder"}, {"Name": "_backends", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\httpcore\\_backends", "Type": "Folder"}, {"Name": "_sync", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\httpcore\\_sync", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\httpcore", "Type": "Folder"}, {"Name": "httpx", "Children": [{"Name": "_transports", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\httpx\\_transports", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\httpx", "Type": "Folder"}, {"Name": "idna", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\idna", "Type": "Folder"}, {"Name": "importlib", "Children": [{"Name": "metadata", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\importlib\\metadata", "Type": "Folder"}, {"Name": "resources", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\importlib\\resources", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\importlib", "Type": "Folder"}, {"Name": "json", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\json", "Type": "Folder"}, {"Name": "logging", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\logging", "Type": "Folder"}, {"Name": "loguru", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\loguru", "Type": "Folder"}, {"Name": "markdown_it", "Children": [{"Name": "common", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markdown_it\\common", "Type": "Folder"}, {"Name": "helpers", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markdown_it\\helpers", "Type": "Folder"}, {"Name": "presets", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markdown_it\\presets", "Type": "Folder"}, {"Name": "rules_block", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markdown_it\\rules_block", "Type": "Folder"}, {"Name": "rules_core", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markdown_it\\rules_core", "Type": "Folder"}, {"Name": "rules_inline", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markdown_it\\rules_inline", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markdown_it", "Type": "Folder"}, {"Name": "markupsafe", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\markupsafe", "Type": "Folder"}, {"Name": "mdurl", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\mdurl", "Type": "Folder"}, {"Name": "multiprocessing", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\multiprocessing", "Type": "Folder"}, {"Name": "os", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\os", "Type": "Folder"}, {"Name": "pydantic", "Children": [{"Name": "_internal", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic\\_internal", "Type": "Folder"}, {"Name": "deprecated", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic\\deprecated", "Type": "Folder"}, {"Name": "plugin", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic\\plugin", "Type": "Folder"}, {"Name": "v1", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic\\v1", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic", "Type": "Folder"}, {"Name": "pydantic_core", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic_core", "Type": "Folder"}, {"Name": "pydantic_settings", "Children": [{"Name": "sources", "Children": [{"Name": "providers", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic_settings\\sources\\providers", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic_settings\\sources", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\pydantic_settings", "Type": "Folder"}, {"Name": "python_multipart", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\python_multipart", "Type": "Folder"}, {"Name": "rich", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\rich", "Type": "Folder"}, {"Name": "sniffio", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sniffio", "Type": "Folder"}, {"Name": "sqlalchemy", "Children": [{"Name": "connectors", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\connectors", "Type": "Folder"}, {"Name": "dialects", "Children": [{"Name": "mssql", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\dialects\\mssql", "Type": "Folder"}, {"Name": "mysql", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\dialects\\mysql", "Type": "Folder"}, {"Name": "oracle", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\dialects\\oracle", "Type": "Folder"}, {"Name": "postgresql", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\dialects\\postgresql", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\dialects", "Type": "Folder"}, {"Name": "engine", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\engine", "Type": "Folder"}, {"Name": "event", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\event", "Type": "Folder"}, {"Name": "ext", "Children": [{"Name": "asyncio", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\ext\\asyncio", "Type": "Folder"}, {"Name": "declarative", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\ext\\declarative", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\ext", "Type": "Folder"}, {"Name": "future", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\future", "Type": "Folder"}, {"Name": "orm", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\orm", "Type": "Folder"}, {"Name": "pool", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\pool", "Type": "Folder"}, {"Name": "sql", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\sql", "Type": "Folder"}, {"Name": "util", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy\\util", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlalchemy", "Type": "Folder"}, {"Name": "sqlite3", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sqlite3", "Type": "Folder"}, {"Name": "src", "Children": [{"Name": "alembic", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\alembic", "Type": "Folder"}, {"Name": "api", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\api", "Type": "Folder"}, {"Name": "config", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\config", "Type": "Folder"}, {"Name": "core", "Children": [{"Name": "calculations", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\calculations", "Type": "Folder"}, {"Name": "database", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\database", "Type": "Folder"}, {"Name": "enums", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\enums", "Type": "Folder"}, {"Name": "errors", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\errors", "Type": "Folder"}, {"Name": "models", "Children": [{"Name": "general", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\models\\general", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\models", "Type": "Folder"}, {"Name": "monitoring", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\monitoring", "Type": "Folder"}, {"Name": "repositories", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\repositories", "Type": "Folder"}, {"Name": "schemas", "Children": [{"Name": "general", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\schemas\\general", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\schemas", "Type": "Folder"}, {"Name": "security", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\security", "Type": "Folder"}, {"Name": "services", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\services", "Type": "Folder"}, {"Name": "standards", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\standards", "Type": "Folder"}, {"Name": "utils", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core\\utils", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\core", "Type": "Folder"}, {"Name": "middleware", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src\\middleware", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\src", "Type": "Folder"}, {"Name": "starlette", "Children": [{"Name": "middleware", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\starlette\\middleware", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\starlette", "Type": "Folder"}, {"Name": "sys", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\sys", "Type": "Folder"}, {"Name": "typer", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\typer", "Type": "Folder"}, {"Name": "typing_inspection", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\typing_inspection", "Type": "Folder"}, {"Name": "urllib", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\urllib", "Type": "Folder"}, {"Name": "u<PERSON><PERSON>", "Children": [{"Name": "middleware", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\uvicorn\\middleware", "Type": "Folder"}, {"Name": "protocols", "Children": [{"Name": "http", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\uvicorn\\protocols\\http", "Type": "Folder"}, {"Name": "websockets", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\uvicorn\\protocols\\websockets", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\uvicorn\\protocols", "Type": "Folder"}, {"Name": "supervisors", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\uvicorn\\supervisors", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\uvicorn", "Type": "Folder"}, {"Name": "websockets", "Children": [{"Name": "asyncio", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\websockets\\asyncio", "Type": "Folder"}, {"Name": "extensions", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\websockets\\extensions", "Type": "Folder"}, {"Name": "legacy", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\websockets\\legacy", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\websockets", "Type": "Folder"}, {"Name": "werkzeug", "Children": [{"Name": "datastructures", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\werkzeug\\datastructures", "Type": "Folder"}, {"Name": "debug", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\werkzeug\\debug", "Type": "Folder"}, {"Name": "routing", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\werkzeug\\routing", "Type": "Folder"}, {"Name": "sansio", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\werkzeug\\sansio", "Type": "Folder"}, {"Name": "wrappers", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\werkzeug\\wrappers", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\werkzeug", "Type": "Folder"}, {"Name": "wsgiref", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\wsgiref", "Type": "Folder"}, {"Name": "wsproto", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\wsproto", "Type": "Folder"}, {"Name": "zipfile", "Children": [{"Name": "_path", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\zipfile\\_path", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\zipfile", "Type": "Folder"}, {"Name": "zoneinfo", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13\\zoneinfo", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\3.13", "Type": "Folder"}, {"Name": "CACHEDIR.TAG", "Extension": ".TAG", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache\\CACHEDIR.TAG", "Type": "File", "SizeKB": 0.19}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.mypy_cache", "Type": "Folder"}, {"Name": "data", "Children": [{"Name": "app_dev.db", "Extension": ".db", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\data\\app_dev.db", "Type": "File", "SizeKB": 44.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\data", "Type": "Folder"}, {"Name": "docs", "Children": [{"Name": "security-audit-report.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\docs\\security-audit-report.md", "Type": "File", "SizeKB": 6.36}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\docs", "Type": "Folder"}, {"Name": "src", "Children": [{"Name": "alembic", "Children": [{"Name": "env.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic\\env.py", "Type": "File", "SizeKB": 4.81}, {"Name": "script.py.mako", "Extension": ".mako", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic\\script.py.mako", "Type": "File", "SizeKB": 0.64}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic", "Type": "Folder"}, {"Name": "api", "Children": [{"Name": "v1", "Children": [{"Name": "auth_routes.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\auth_routes.py", "Type": "File", "SizeKB": 12.46}, {"Name": "health_routes.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\health_routes.py", "Type": "File", "SizeKB": 9.26}, {"Name": "router.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\router.py", "Type": "File", "SizeKB": 4.05}, {"Name": "user_routes.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\user_routes.py", "Type": "File", "SizeKB": 15.09}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1", "Type": "Folder"}, {"Name": "main_router.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\main_router.py", "Type": "File", "SizeKB": 1.35}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api", "Type": "Folder"}, {"Name": "config", "Children": [{"Name": "logging_config.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config\\logging_config.py", "Type": "File", "SizeKB": 6.36}, {"Name": "settings.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config\\settings.py", "Type": "File", "SizeKB": 6.08}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config", "Type": "Folder"}, {"Name": "core", "Children": [{"Name": "calculations", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\calculations", "Type": "Folder"}, {"Name": "database", "Children": [{"Name": "dependencies.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\dependencies.py", "Type": "File", "SizeKB": 1.04}, {"Name": "engine.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\engine.py", "Type": "File", "SizeKB": 10.99}, {"Name": "initialization.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\initialization.py", "Type": "File", "SizeKB": 6.04}, {"Name": "session.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\session.py", "Type": "File", "SizeKB": 14.13}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database", "Type": "Folder"}, {"Name": "enums", "Children": [{"Name": "calculation_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\calculation_enums.py", "Type": "File", "SizeKB": 2.53}, {"Name": "common_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\common_enums.py", "Type": "File", "SizeKB": 2.3}, {"Name": "data_io_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\data_io_enums.py", "Type": "File", "SizeKB": 6.12}, {"Name": "electrical_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\electrical_enums.py", "Type": "File", "SizeKB": 24.55}, {"Name": "heat_tracing_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\heat_tracing_enums.py", "Type": "File", "SizeKB": 2.64}, {"Name": "mechanical_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\mechanical_enums.py", "Type": "File", "SizeKB": 4.11}, {"Name": "project_management_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\project_management_enums.py", "Type": "File", "SizeKB": 4.86}, {"Name": "standards_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\standards_enums.py", "Type": "File", "SizeKB": 2.89}, {"Name": "system_enums.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\system_enums.py", "Type": "File", "SizeKB": 4.81}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums", "Type": "Folder"}, {"Name": "errors", "Children": [{"Name": "exceptions.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors\\exceptions.py", "Type": "File", "SizeKB": 8.44}, {"Name": "unified_error_handler.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors\\unified_error_handler.py", "Type": "File", "SizeKB": 37.8}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors", "Type": "Folder"}, {"Name": "integrations", "Children": [{"Name": "cad_service", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\cad_service", "Type": "Folder"}, {"Name": "computation service", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\computation service", "Type": "Folder"}, {"Name": "message_brokers", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\message_brokers", "Type": "Folder"}, {"Name": "README.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\README.md", "Type": "File", "SizeKB": 1.88}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations", "Type": "Folder"}, {"Name": "models", "Children": [{"Name": "general", "Children": [{"Name": "project.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\project.py", "Type": "File", "SizeKB": 15.39}, {"Name": "user.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\user.py", "Type": "File", "SizeKB": 3.38}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general", "Type": "Folder"}, {"Name": "base.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\base.py", "Type": "File", "SizeKB": 7.45}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models", "Type": "Folder"}, {"Name": "monitoring", "Children": [{"Name": "unified_performance_monitor.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\monitoring\\unified_performance_monitor.py", "Type": "File", "SizeKB": 31.63}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\monitoring", "Type": "Folder"}, {"Name": "repositories", "Children": [{"Name": "general", "Children": [{"Name": "project_repository.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\project_repository.py", "Type": "File", "SizeKB": 11.36}, {"Name": "user_preference_repository.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\user_preference_repository.py", "Type": "File", "SizeKB": 3.56}, {"Name": "user_repository.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\user_repository.py", "Type": "File", "SizeKB": 5.93}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general", "Type": "Folder"}, {"Name": "base_repository.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\base_repository.py", "Type": "File", "SizeKB": 10.14}, {"Name": "repository_dependencies.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\repository_dependencies.py", "Type": "File", "SizeKB": 1.33}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories", "Type": "Folder"}, {"Name": "schemas", "Children": [{"Name": "general", "Children": [{"Name": "project_schemas.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\project_schemas.py", "Type": "File", "SizeKB": 4.04}, {"Name": "user_schemas.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\user_schemas.py", "Type": "File", "SizeKB": 9.62}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general", "Type": "Folder"}, {"Name": "base.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\base.py", "Type": "File", "SizeKB": 1.79}, {"Name": "error.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\error.py", "Type": "File", "SizeKB": 3.2}, {"Name": "health.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\health.py", "Type": "File", "SizeKB": 4.5}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas", "Type": "Folder"}, {"Name": "security", "Children": [{"Name": "enhanced_dependencies.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\enhanced_dependencies.py", "Type": "File", "SizeKB": 9.36}, {"Name": "input_validators.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\input_validators.py", "Type": "File", "SizeKB": 43.31}, {"Name": "unified_security_validator.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\unified_security_validator.py", "Type": "File", "SizeKB": 37.59}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security", "Type": "Folder"}, {"Name": "services", "Children": [{"Name": "design_automation", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\design_automation", "Type": "Folder"}, {"Name": "general", "Children": [{"Name": "project_service.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\project_service.py", "Type": "File", "SizeKB": 16.53}, {"Name": "user_service.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\user_service.py", "Type": "File", "SizeKB": 27.13}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general", "Type": "Folder"}, {"Name": "dependencies.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\dependencies.py", "Type": "File", "SizeKB": 1.4}, {"Name": "health_service.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\health_service.py", "Type": "File", "SizeKB": 11.41}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services", "Type": "Folder"}, {"Name": "standards", "Children": [], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\standards", "Type": "Folder"}, {"Name": "utils", "Children": [{"Name": "crud_endpoint_factory.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\crud_endpoint_factory.py", "Type": "File", "SizeKB": 23.0}, {"Name": "datetime_utils.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\datetime_utils.py", "Type": "File", "SizeKB": 8.21}, {"Name": "file_io_utils.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\file_io_utils.py", "Type": "File", "SizeKB": 14.66}, {"Name": "json_validation.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\json_validation.py", "Type": "File", "SizeKB": 12.07}, {"Name": "memory_manager.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\memory_manager.py", "Type": "File", "SizeKB": 14.74}, {"Name": "pagination_utils.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\pagination_utils.py", "Type": "File", "SizeKB": 9.32}, {"Name": "performance_optimizer.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\performance_optimizer.py", "Type": "File", "SizeKB": 16.33}, {"Name": "performance_utils.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\performance_utils.py", "Type": "File", "SizeKB": 12.39}, {"Name": "query_utils.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\query_utils.py", "Type": "File", "SizeKB": 15.14}, {"Name": "security.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\security.py", "Type": "File", "SizeKB": 7.07}, {"Name": "string_utils.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\string_utils.py", "Type": "File", "SizeKB": 8.51}, {"Name": "uuid_utils.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\uuid_utils.py", "Type": "File", "SizeKB": 5.43}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils", "Type": "Folder"}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core", "Type": "Folder"}, {"Name": "middleware", "Children": [{"Name": "caching_middleware.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\caching_middleware.py", "Type": "File", "SizeKB": 17.64}, {"Name": "context_middleware.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\context_middleware.py", "Type": "File", "SizeKB": 8.08}, {"Name": "logging_middleware.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\logging_middleware.py", "Type": "File", "SizeKB": 10.47}, {"Name": "rate_limiting_middleware.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\rate_limiting_middleware.py", "Type": "File", "SizeKB": 13.63}, {"Name": "security_middleware.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\security_middleware.py", "Type": "File", "SizeKB": 18.85}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware", "Type": "Folder"}, {"Name": "alembic.ini", "Extension": ".ini", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic.ini", "Type": "File", "SizeKB": 3.11}, {"Name": "app.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\app.py", "Type": "File", "SizeKB": 7.27}, {"Name": "main.py", "Extension": ".py", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\main.py", "Type": "File", "SizeKB": 12.62}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src", "Type": "Folder"}, {"Name": ".safety-project.ini", "Extension": ".ini", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.safety-project.ini", "Type": "File", "SizeKB": 0.14}, {"Name": "pyproject.toml", "Extension": ".toml", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\pyproject.toml", "Type": "File", "SizeKB": 14.75}, {"Name": "README.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\README.md", "Type": "File", "SizeKB": 9.42}, {"Name": "test_app.db", "Extension": ".db", "Path": "D:\\Projects\\ultimate-electrical-designer\\server\\test_app.db", "Type": "File", "SizeKB": 36.0}], "Path": "D:\\Projects\\ultimate-electrical-designer\\server", "Type": "Folder"}, {"Name": "Commands.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\Commands.md", "Type": "File", "SizeKB": 1.42}, {"Name": "dev_prompt-instructions.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\dev_prompt-instructions.md", "Type": "File", "SizeKB": 7.4}, {"Name": "README.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\README.md", "Type": "File", "SizeKB": 18.85}, {"Name": "Rules.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\Rules.md", "Type": "File", "SizeKB": 5.95}, {"Name": "TODO.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\TODO.md", "Type": "File", "SizeKB": 7.45}, {"Name": "Workflows.md", "Extension": ".md", "Path": "D:\\Projects\\ultimate-electrical-designer\\Workflows.md", "Type": "File", "SizeKB": 45.4}], "Path": "D:\\Projects\\ultimate-electrical-designer", "Type": "Folder"}