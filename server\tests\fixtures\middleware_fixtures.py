from unittest.mock import Mock
import pytest

from src.middleware.security_middleware import SecurityMiddleware
from src.middleware.context_middleware import ContextMiddleware
from src.middleware.logging_middleware import LoggingMiddleware
from src.middleware.caching_middleware import CachingMiddleware
from src.middleware.rate_limiting_middleware import RateLimitingMiddleware
from fixtures.api_mocks import (
    mock_app,
    mock_request,
    mock_response,
    mock_call_next,
)


@pytest.fixture
def caching_middleware(mock_app):
    """Create CachingMiddleware instance with default configuration."""
    return CachingMiddleware(
        app=mock_app,
        default_ttl=300,
        enable_caching=True,
        enable_etag=True,
        enable_user_specific_caching=True,
        redis_client=None,
        cache_key_prefix="test_cache:",
        exclude_paths=None,
        max_cache_size=1000,
    )


@pytest.fixture
def caching_middleware_with_redis(mock_app):
    """Create a CachingMiddleware instance with mock Redis client."""
    mock_redis = Mock()
    return CachingMiddleware(
        app=mock_app,
        default_ttl=300,
        enable_caching=True,
        enable_etag=True,
        redis_client=mock_redis,
        max_cache_size=100,
    )


@pytest.fixture
def context_middleware(mock_app):
    """Create ContextMiddleware instance with default configuration."""
    return ContextMiddleware(
        app=mock_app,
        enable_request_id=True,
        enable_user_context=True,
        enable_locale_detection=True,
        enable_timing=True,
        default_locale="en",
    )


@pytest.fixture
def logging_middleware(mock_app):
    """Create LoggingMiddleware instance with default configuration."""
    return LoggingMiddleware(
        app=mock_app,
        enable_request_logging=True,
        enable_response_logging=True,
        enable_error_logging=True,
        enable_performance_logging=True,
        log_request_body=False,
        log_response_body=False,
        max_body_size=1024,
        exclude_paths=None,
        exclude_health_checks=True,
    )


@pytest.fixture
def rate_limiting_middleware(mock_app):
    """Create RateLimitingMiddleware instance with default configuration."""
    return RateLimitingMiddleware(
        app=mock_app,
        default_requests_per_minute=60,
        default_burst_size=10,
        enable_per_ip_limiting=True,
        enable_per_user_limiting=True,
        enable_endpoint_specific_limits=True,
        redis_client=None,
        exclude_paths=None,
    )


@pytest.fixture
def security_middleware(mock_app):
    """Fixture for SecurityMiddleware instance with default configuration."""
    return SecurityMiddleware(
        app=mock_app,
        max_payload_size=1024 * 1024,  # 1MB for testing
        max_json_depth=10,
        rate_limit_requests=5,
        rate_limit_window=10,
        enable_xss_protection=True,
        enable_unicode_validation=True,
        jwt_secret_key="test-secret-key",
        jwt_algorithm="HS256",
        enable_csrf_protection=True,
        use_unified_security=False,  # Disable for isolated testing
    )
