# src/core/repositories/general/user_repository.py
"""User Repository.

This module provides data access layer for User entities, extending the base
repository with user-specific query methods and operations.
"""

from sqlalchemy import and_, func, or_, select, update
from sqlalchemy.orm import Session

from src.config.logging_config import logger

# Unified systems imports
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.user import User
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class UserRepository(BaseRepository[User]):
    """Repository for User entity data access operations.

    Extends BaseRepository with user-specific query methods and
    enhanced error handling for user operations.
    """

    def __init__(self, db_session: Session):
        """Initialize the User repository.

        Args:
            db_session: SQLAlchemy database session

        """
        super().__init__(db_session, User)
        logger.debug("UserRepository initialized")

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def get_by_email(self, email: str) -> User | None:
        """Get user by email address.

        Args:
            email: User email address

        Returns:
            Optional[User]: User with the specified email or None if not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving user by email: {email}")

        stmt = select(self.model).where(
            and_(
                self.model.email == email,
                self.model.is_active == True,
            )
        )

        result = self.db_session.scalar(stmt)

        if result:
            logger.debug(f"User found for email: {email}")
        else:
            logger.debug(f"No user found for email: {email}")

        return result

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def get_by_name(self, name: str) -> User | None:
        """Get user by name.

        Args:
            name: User name

        Returns:
            Optional[User]: User with the specified name or None if not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving user by name: {name}")

        stmt = select(self.model).where(
            and_(
                self.model.name == name,
                self.model.is_active == True,
            )
        )
        result = self.db_session.scalar(stmt)

        if result:
            logger.debug(f"User found for name: {name}")
        else:
            logger.debug(f"No user found for name: {name}")

        return result

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def get_active_users(self, skip: int = 0, limit: int = 100) -> list[User]:
        """Get all active users.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[User]: List of active users

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving active users: skip={skip}, limit={limit}")

        stmt = (
            select(self.model)
            .where(self.model.is_active == True)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} active users")
        return results

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def search_users(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> list[User]:
        """Search users by name or email.

        Args:
            search_term: Search term to match against name or email
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[User]: List of users matching the search term

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(
            f"Searching users with term: {search_term}, skip={skip}, limit={limit}"
        )

        # Use ilike for case-insensitive search
        search_pattern = f"%{search_term}%"
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_active == True,
                    or_(
                        self.model.name.ilike(search_pattern),
                        self.model.email.ilike(search_pattern),
                    ),
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Found {len(results)} users matching search term: {search_term}")
        return results

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def count_active_users(self) -> int:
        """Count total number of active users.

        Returns:
            int: Total count of active users

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug("Counting active users")

        from sqlalchemy import func

        stmt = select(func.count(self.model.id)).where(self.model.is_active == True)
        result = self.db_session.scalar(stmt)
        count = result if result is not None else 0

        logger.debug(f"Total active users count: {count}")
        return count

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def count_active_users(self) -> int:
        """Count total number of active users.

        Returns:
            int: Number of active users

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug("Counting active users")

        stmt = select(func.count(self.model.id)).where(self.model.is_active == True)
        result = self.db_session.scalar(stmt)

        logger.debug(f"Total active users: {result}")
        return result or 0

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def update_password(self, user_id: int, password_hash: str) -> bool:
        """Update user password hash.

        Args:
            user_id: User ID
            password_hash: New password hash

        Returns:
            bool: True if password was updated, False if user not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Updating password for user {user_id}")

        stmt = (
            update(self.model)
            .where(self.model.id == user_id)
            .values(password_hash=password_hash)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Password updated for user {user_id}")
            return True
        logger.debug(f"User {user_id} not found for password update")
        return False

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user account.

        Args:
            user_id: User ID

        Returns:
            bool: True if user was deactivated, False if user not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Deactivating user {user_id}")

        stmt = (
            update(self.model).where(self.model.id == user_id).values(is_active=False)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"User {user_id} deactivated successfully")
            return True
        logger.debug(f"User {user_id} not found for deactivation")
        return False

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    def activate_user(self, user_id: int) -> bool:
        """Activate a user account.

        Args:
            user_id: User ID

        Returns:
            bool: True if user was activated, False if user not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Activating user {user_id}")

        stmt = (
            update(self.model).where(self.model.id == user_id).values(is_active=True)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"User {user_id} activated successfully")
            return True
        logger.debug(f"User {user_id} not found for activation")
        return False
