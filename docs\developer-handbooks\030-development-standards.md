# 03 - Development Standards

**Section:** 03-development-standards  
**Version:** 1.0  
**Last Updated:** July 2025  
**Prerequisites:** [Getting Started](020-getting-started.md) completed  
**Estimated Reading Time:** 20 minutes  

## Overview

The Ultimate Electrical Designer project maintains engineering-grade standards with immaculate attention to detail. This section defines the comprehensive development standards, zero-tolerance policies, and compliance requirements that ensure professional-quality electrical design software suitable for mission-critical applications.

## Table of Contents

- [Zero Tolerance Policies](#zero-tolerance-policies)
- [Standards Compliance Framework](#standards-compliance-framework)
- [Code Quality Standards](#code-quality-standards)
- [Engineering Requirements](#engineering-requirements)
- [Testing Standards](#testing-standards)
- [Documentation Standards](#documentation-standards)
- [Performance Standards](#performance-standards)
- [Security Standards](#security-standards)

## Zero Tolerance Policies

### 1. No 'However' Scenarios
**Policy:** Implementation of missing business-critical functionality must always take absolute priority over updating test data or maintaining backward compatibility.

**Rationale:** Engineering software requires complete, robust implementations. Partial solutions or workarounds compromise the integrity of electrical design calculations and safety compliance.

**Implementation:**
- Business-critical functionality is implemented completely before any secondary tasks
- No temporary workarounds or partial implementations in production code
- All electrical calculations must be complete and standards-compliant

### 2. Single Source of Truth
**Policy:** Each calculation type has exactly one implementation. NO different versions like 'simple, basic, advanced, professional'.

**Rationale:** Multiple implementations of the same calculation create inconsistencies, maintenance overhead, and potential safety issues in electrical design.

**Implementation:**
```python
# ✅ Correct - Single implementation
@handle_calculation_errors("heat_loss_calculation")
def calculate_heat_loss(parameters: HeatLossParameters) -> HeatLossResult:
    """Complete heat loss calculation per IEC standards."""
    # Single, comprehensive implementation
    pass

# ❌ Incorrect - Multiple versions
def calculate_heat_loss_simple(params): pass
def calculate_heat_loss_advanced(params): pass
```

### 3. Standards Compliance Only
**Policy:** Only IEEE, IEC, and EN standards are referenced.

**Rationale:** The project focuses on international electrical standards.

**Approved Standards:**
- **IEEE:** Institute of Electrical and Electronics Engineers
- **IEC:** International Electrotechnical Commission  
- **EN:** European Norms (European Standards)

### 4. Immaculate Attention to Detail
**Policy:** Every implementation must meet engineering-grade standards with robust patterns maintaining flexibility for future expansions.

**Implementation Requirements:**
- Complete error handling with unified patterns
- Comprehensive input validation
- Performance monitoring and optimization
- Complete documentation with examples
- 100% test coverage for critical calculations

## Standards Compliance Framework

### IEEE Standards (Institute of Electrical and Electronics Engineers)

#### IEEE-519: Network Quality
**Scope:** Harmonic analysis and power quality standards  
**Application:** Power system analysis and harmonic distortion calculations  
**Implementation:** `core/calculations/power_quality/`

```python
@handle_calculation_errors("harmonic_analysis")
def calculate_harmonic_distortion(parameters: IEEE519Parameters) -> HarmonicAnalysisResult:
    """Calculate harmonic distortion per IEEE-519 standards."""
    # Implementation following IEEE-519 guidelines
    pass
```

### IEC Standards (International Electrotechnical Commission)

#### IEC-60079: Explosive Atmospheres
**Scope:** Equipment for explosive atmospheres (ATEX)  
**Application:** Hazardous area electrical equipment selection  
**Implementation:** `core/standards/hazardous_areas/`

```python
@handle_validation_errors("atex_compliance")
def validate_atex_compliance(equipment: Equipment, zone: HazardousZone) -> ATEXValidationResult:
    """Validate equipment compliance with IEC-60079 ATEX standards."""
    # ATEX compliance validation
    pass
```

#### IEC-61508: Functional Safety
**Scope:** Functional safety of electrical/electronic systems  
**Application:** Safety integrity level (SIL) calculations  
**Implementation:** `core/calculations/safety/`

#### IEC-60364: Low-Voltage Electrical Installations
**Scope:** Low-voltage electrical installation design  
**Application:** Electrical installation design and safety  
**Implementation:** `core/standards/installations/`

#### IEC-60287: Electric Cables - Current Rating Calculation
**Scope:** Cable current rating calculations  
**Application:** Cable sizing and derating calculations  
**Implementation:** `core/calculations/cables/current_rating.py`

### EN Standards (European Norms)

#### EN-50110: Operation of Electrical Installations
**Scope:** Safe operation of electrical installations  
**Application:** Operational safety procedures and requirements  
**Implementation:** `core/standards/operations/`

#### EN-60204: Safety of Machinery - Electrical Equipment
**Scope:** Electrical safety in machinery applications  
**Application:** Industrial machinery electrical design  
**Implementation:** `core/standards/machinery/`

#### EN-50522: Earthing of Power Installations Exceeding 1 kV AC
**Scope:** High-voltage earthing system design  
**Application:** High-voltage electrical installations  
**Implementation:** `core/calculations/grounding/high_voltage.py`

## Code Quality Standards

### Unified Patterns Compliance
**Requirement:** All code must use unified patterns for error handling, performance monitoring, and security.

```python
# Required pattern for all service methods
@handle_service_errors("operation_name")
@monitor_service_performance("operation_name")
def service_method(self, parameters: ServiceParameters) -> ServiceResult:
    """Service method with unified patterns."""
    # Implementation with automatic error handling and monitoring
    pass

# Required pattern for all repository methods
@handle_repository_errors("entity_name")
def repository_method(self, entity_id: int) -> Entity:
    """Repository method with unified error handling."""
    # Database operations with automatic error translation
    pass

# Required pattern for all calculation methods
@handle_calculation_errors("calculation_type")
@monitor_calculation_performance("calculation_type")
def calculation_method(self, calc_params: CalculationParameters) -> CalculationResult:
    """Calculation method with unified patterns."""
    # Engineering calculations with error handling and monitoring
    pass
```

### Type Safety Requirements
**Requirement:** Complete type hints for all public APIs and critical internal functions with MyPy validation.

#### Type Annotation Standards
```python
# ✅ Correct - Complete type hints with proper imports
from typing import Optional, Union, Any, Callable, Awaitable
from fastapi import Request, Response

def calculate_power_loss(
    current: float,
    resistance: float,
    temperature: Optional[float] = None
) -> PowerLossResult:
    """Calculate power loss with complete type safety."""
    pass

# ✅ Correct - Middleware type annotations
async def dispatch(
    self,
    request: Request,
    call_next: Callable[[Request], Awaitable[Response]]
) -> Response:
    """Middleware dispatch with proper type annotations."""
    pass

# ❌ Incorrect - Missing type hints
def calculate_power_loss(current, resistance, temperature=None):
    pass
```

#### Union Type Compatibility
**Requirement:** Use `Optional[T]` instead of `T | None` for Python 3.9+ compatibility.

```python
# ✅ Correct - Compatible union types
from typing import Optional

def process_data(
    value: Optional[str] = None,
    config: Optional[dict[str, Any]] = None
) -> Optional[ProcessResult]:
    """Process data with compatible type annotations."""
    pass

# ❌ Incorrect - Python 3.10+ only syntax
def process_data(
    value: str | None = None,
    config: dict[str, Any] | None = None
) -> ProcessResult | None:
    pass
```

#### MyPy Configuration and Validation
**Requirement:** Regular MyPy validation as part of development workflow.

```bash
# Required MyPy validation commands
poetry run mypy src/ --show-error-codes --ignore-missing-imports
poetry run mypy src/core/ --show-error-codes  # Core modules
poetry run mypy src/api/ --show-error-codes   # API layer
```

**MyPy Configuration Standards:**
- Use `--show-error-codes` for specific error identification
- Apply `--ignore-missing-imports` for third-party dependencies
- Target zero errors for critical modules
- Maintain 60%+ error reduction rate for large remediation projects

### Documentation Requirements
**Requirement:** Complete docstrings following Google style for all public APIs.

```python
def calculate_heat_tracing_power(
    pipe_params: PipeParameters,
    ambient_conditions: AmbientConditions,
    target_temperature: float
) -> HeatTracingResult:
    """Calculate heat tracing power requirements.
    
    Args:
        pipe_params: Pipe physical and thermal parameters
        ambient_conditions: Environmental conditions (temperature, wind, etc.)
        target_temperature: Required maintenance temperature in Celsius
        
    Returns:
        HeatTracingResult containing power requirements, cable selection,
        and thermal analysis results
        
    Raises:
        InvalidInputError: If input parameters are outside valid ranges
        CalculationError: If thermal calculations fail
        
    Example:
        >>> pipe = PipeParameters(diameter=0.1, length=100, insulation_thickness=0.05)
        >>> ambient = AmbientConditions(temperature=-20, wind_speed=5)
        >>> result = calculate_heat_tracing_power(pipe, ambient, 60)
        >>> print(f"Required power: {result.power_per_meter} W/m")
    """
    pass
```

## Engineering Requirements

### Calculation Accuracy
**Requirement:** All engineering calculations must meet professional accuracy standards.

- **Precision:** Minimum 6 significant digits for critical calculations
- **Validation:** Input parameter validation against engineering limits
- **Units:** Consistent SI units with clear unit conversion functions
- **Rounding:** Appropriate rounding for engineering applications

### Performance Requirements
**Requirement:** Engineering-grade performance suitable for large projects.

- **API Response Time:** < 200ms for standard operations
- **Calculation Performance:** < 500ms for complex thermal calculations
- **Memory Usage:** < 100MB for typical calculation operations
- **Database Queries:** < 100ms for standard CRUD operations

### Reliability Requirements
**Requirement:** Mission-critical reliability for professional electrical design.

- **Error Handling:** Comprehensive error handling with graceful degradation
- **Input Validation:** Complete validation of all engineering parameters
- **Data Integrity:** Database constraints and validation at all layers
- **Audit Trail:** Complete logging of all calculation operations

## Testing Standards

### Coverage Requirements
**Requirement:** Comprehensive test coverage with real database testing.

- **Critical Modules:** 90%+ test coverage required
- **High Priority Modules:** 85%+ test coverage required
- **Overall Target:** 85%+ project-wide test coverage
- **Pass Rate:** 100% test pass rate required

### Testing Methodology
**Requirement:** 5-phase testing methodology with real database connections.

1. **Discovery & Analysis:** Understand testing requirements and current coverage
2. **Task Planning:** Break down testing into ~20-minute units
3. **Implementation:** Write comprehensive tests with real database connections
4. **Verification:** Validate test coverage and effectiveness
5. **Documentation:** Document test scenarios and maintain test documentation

### Test Categories
```bash
# Required test categories
make test-unit              # Core utilities and validation
make test-integration       # Services, middleware, database
make test-api              # API endpoint tests
make test-calculations     # Electrical calculation tests
make test-standards        # IEEE/IEC/EN standards compliance
make test-security         # Security validation tests
make test-performance      # Performance benchmarking
make test-types            # MyPy type checking validation
```

### Type Safety Validation
**Requirement:** MyPy type checking as quality gate in development process.

```bash
# Type safety validation workflow
poetry run mypy src/ --show-error-codes --ignore-missing-imports

# Target metrics for type safety
# - Critical modules: Zero MyPy errors
# - High priority modules: <5 MyPy errors per 1000 lines
# - Overall project: 60%+ error reduction rate
# - New code: 100% type annotation coverage
```

### Real Database Testing
**Requirement:** NO mocks for database testing - all tests use real database connections.

```python
# ✅ Correct - Real database testing
@pytest.mark.database
def test_project_creation_with_database(db_session):
    """Test project creation with real database."""
    project_data = {"name": "Test Project", "description": "Test"}
    project = project_service.create_project(project_data)
    
    # Verify in actual database
    db_project = db_session.query(Project).filter_by(id=project.id).first()
    assert db_project is not None
    assert db_project.name == "Test Project"

# ❌ Incorrect - Mocked database
@patch('core.database.session.get_db')
def test_project_creation_mocked(mock_db):
    # Mocked testing not allowed
    pass
```

## Documentation Standards

### Single Source of Truth
**Requirement:** No duplicate information across documentation.

- Each concept documented in exactly one authoritative location
- Cross-references used instead of duplication
- Regular validation of documentation consistency

### Practical Examples
**Requirement:** All documentation includes working code examples.

```python
# Documentation must include complete, working examples
"""
Example:
    Calculate heat loss for a pipe in winter conditions:
    
    >>> from src.core.calculations.thermal import calculate_heat_loss
    >>> pipe = PipeParameters(
    ...     diameter=0.1,  # 100mm diameter
    ...     length=50,     # 50 meter length
    ...     insulation_thickness=0.05  # 50mm insulation
    ... )
    >>> conditions = AmbientConditions(
    ...     temperature=-20,  # -20°C ambient
    ...     wind_speed=5     # 5 m/s wind
    ... )
    >>> result = calculate_heat_loss(pipe, conditions, target_temp=60)
    >>> print(f"Heat loss: {result.heat_loss_per_meter:.2f} W/m")
    Heat loss: 45.67 W/m
"""
```

### Cross-Referencing
**Requirement:** Comprehensive linking between related concepts.

- Relative links for internal documentation
- Clear navigation paths between sections
- Consistent link formatting and validation

## Performance Standards

### Memory Management
**Requirement:** Efficient memory usage with automatic cleanup.

```python
# Required memory optimization for large calculations
@memory_optimized(auto_cleanup=True, threshold_mb=5.0)
@handle_calculation_errors("thermal_analysis")
def large_thermal_calculation(parameters: LargeCalculationParameters) -> CalculationResult:
    """Large thermal calculation with memory optimization."""
    # Automatic memory cleanup when threshold exceeded
    pass
```

### Monitoring Requirements
**Requirement:** Built-in performance monitoring for all critical operations.

```python
# Required performance monitoring
@monitor_service_performance("project_creation")
@handle_service_errors("project_creation")
def create_project(self, project_data: dict) -> Project:
    """Create project with performance monitoring."""
    # Automatic performance tracking and alerting
    pass
```

## Security Standards

### Input Validation
**Requirement:** Comprehensive validation at all entry points.

```python
# Required validation pattern
@handle_validation_errors("project_input")
def validate_project_input(project_data: dict) -> ProjectCreateSchema:
    """Validate project input with comprehensive checks."""
    # Pydantic validation with custom business rules
    schema = ProjectCreateSchema(**project_data)
    
    # Additional engineering validation
    if schema.voltage_level > MAX_VOLTAGE_LEVEL:
        raise InvalidInputError("Voltage level exceeds maximum allowed")
    
    return schema
```

### Authentication and Authorization
**Requirement:** Role-based access control for all operations.

```python
# Required security pattern for API endpoints
@handle_api_errors("get_project")
@require_permissions("project:read")
def get_project(project_id: int, current_user: User = Depends(get_current_user)):
    """Get project with proper authorization."""
    # Automatic permission checking and user validation
    pass
```

---

**Navigation:**  
← [Previous: Getting Started](020-getting-started.md) | [Handbook Home](001-cover.md) | [Next: Unified Patterns](040-unified-patterns.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Testing Standards Documentation](070-testing-framework.md)
