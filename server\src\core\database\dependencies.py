# backend/core/database/dependencies.py
"""Database Dependencies.

This module provides database-related dependency injection providers for FastAPI.
Implements per-request database session management with proper lifecycle handling.
"""

from collections.abc import Generator

from sqlalchemy.orm import Session

from src.core.database.session import get_db_session


def get_db() -> Generator[Session, None, None]:
    """FastAPI dependency provider for database sessions.

    Provides a per-request SQLAlchemy Session with automatic lifecycle management.
    The session is created at the start of the request and automatically closed
    when the request completes, ensuring proper resource cleanup.

    Yields:
        Session: SQLAlchemy database session

    Example:
        @router.get("/users/")
        async def get_users(db: Session = Depends(get_db)):
            return user_service.get_all_users()

    """
    with get_db_session() as session:
        yield session


# Alias for backward compatibility and clarity
get_database_session = get_db
