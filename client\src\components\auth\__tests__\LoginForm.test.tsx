import { useAuth } from '@/hooks/useAuth'
import { renderWithProviders, screen, userEvent, waitFor } from '@/test/utils'
import { act } from '@testing-library/react'
import { beforeEach, describe, expect, it } from 'vitest'
import { LoginForm } from '../LoginForm'

// Mock the useAuth hook
vi.mock('@/hooks/useAuth')

const mockUseAuth = vi.mocked(useAuth)

describe('LoginForm Component', () => {
  const mockLogin = vi.fn()
  const mockOnSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseAuth.mockReturnValue({
      login: mockLogin,
      isLoading: false,
      loginError: null,
      user: null,
      token: null,
      isAuthenticated: false,
      logout: vi.fn(),
      hasRole: vi.fn(),
      isAdmin: vi.fn(),
      requireAuth: vi.fn(),
      requireAdmin: vi.fn(),
      logoutError: null,
      isLoginPending: false,
      isLogoutPending: false,
    })
  })

  it('renders login form fields', () => {
    renderWithProviders(<LoginForm />)

    expect(screen.getByLabelText(/username or email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    renderWithProviders(<LoginForm />)

    const submitButton = screen.getByRole('button', { name: /sign in/i })

    // Submit the form with empty fields
    await act(async () => {
      await user.click(submitButton)
    })

    // Wait for validation errors to appear
    await waitFor(() => {
      expect(screen.getByText(/username or email is required/i)).toBeInTheDocument()
    }, { timeout: 3000 })

    await waitFor(() => {
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    }, { timeout: 3000 })

    expect(mockLogin).not.toHaveBeenCalled()
  })

  it('validates password length', async () => {
    const user = userEvent.setup()
    renderWithProviders(<LoginForm />)

    const usernameInput = screen.getByLabelText(/username or email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /sign in/i })

    await act(async () => {
      await user.type(usernameInput, '<EMAIL>')
      await user.type(passwordInput, '123') // Too short
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument()
    })
    expect(mockLogin).not.toHaveBeenCalled()
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    mockLogin.mockResolvedValue(undefined)

    renderWithProviders(<LoginForm onSuccess={mockOnSuccess} />)

    const usernameInput = screen.getByLabelText(/username or email/i)
    const passwordInput = screen.getByLabelText(/password/i)
    const submitButton = screen.getByRole('button', { name: /sign in/i })

    await act(async () => {
      await user.type(usernameInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: '<EMAIL>',
        password: 'password123',
      })
    })

    expect(mockOnSuccess).toHaveBeenCalled()
  })

  it('displays loading state', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isLoading: true,
      isLoginPending: true,
    })

    renderWithProviders(<LoginForm />)

    expect(screen.getByText(/signing in/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled()
  })

  it('displays login error', () => {
    const error = new Error('Invalid credentials')
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      loginError: error,
    })

    renderWithProviders(<LoginForm />)

    expect(screen.getByText(/login failed/i)).toBeInTheDocument()
    expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
  })

  it('clears field errors when user starts typing', async () => {
    const user = userEvent.setup()

    // Mock the validateForm function to set errors directly
    const { container } = renderWithProviders(<LoginForm />)

    const submitButton = screen.getByRole('button', { name: /sign in/i })

    await act(async () => {
      await user.click(submitButton)
    })

    // Wait for validation errors to appear
    await waitFor(() => {
      expect(screen.getByText(/username or email is required/i)).toBeInTheDocument()
    }, { timeout: 3000 })

    // Start typing in username field
    const usernameInput = screen.getByLabelText(/username or email/i)
    await act(async () => {
      await user.type(usernameInput, 'test')
    })

    // Wait for username error to be cleared
    await waitFor(() => {
      expect(screen.queryByText(/username or email is required/i)).not.toBeInTheDocument()
    }, { timeout: 3000 })

    // Password error should still be visible
    expect(screen.getByText(/password is required/i)).toBeInTheDocument()
  })
})
