'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import type { LoginRequest } from '@/types/api'
import { useState } from 'react'

interface LoginFormProps {
  onSuccess?: () => void
  className?: string
}

export function LoginForm({ onSuccess, className = '' }: LoginFormProps) {
  const { login, isLoading, loginError } = useAuth()
  
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }))
    }
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Direct validation in the submit handler
    const newErrors: Record<string, string> = {}

    if (!formData.username.trim()) {
      newErrors.username = 'Username or email is required'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters'
    }

    // Set errors and force a re-render
    setErrors({...newErrors})

    // If there are validation errors, don't proceed with login
    if (Object.keys(newErrors).length > 0) {
      return
    }

    try {
      // Attempt login (isLoading state is managed by useAuth hook)
      await login(formData)

      // Call success callback if provided
      onSuccess?.()
    } catch (error) {
      // Handle login error (loginError state is managed by useAuth hook)
      console.error('Login failed:', error)
    }
  }

  return (
    <form className={`space-y-6 ${className}`} onSubmit={handleSubmit}>
      <div>
        <label htmlFor="username" className="block text-sm font-medium text-gray-700">
          Username or Email
        </label>
        <input
          id="username"
          name="username"
          type="text"
          autoComplete="username"
          className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
            errors.username ? 'border-red-300' : 'border-gray-300'
          } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
          placeholder="Enter your username or email"
          value={formData.username}
          onChange={handleInputChange}
        />
        {errors.username && (
          <p data-testid="username-error" className="mt-1 text-sm text-red-600">{errors.username}</p>
        )}
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
          Password
        </label>
        <input
          id="password"
          name="password"
          type="password"
          autoComplete="current-password"
          className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
            errors.password ? 'border-red-300' : 'border-gray-300'
          } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
          placeholder="Enter your password"
          value={formData.password}
          onChange={handleInputChange}
        />
        {errors.password && (
          <p data-testid="password-error" className="mt-1 text-sm text-red-600">{errors.password}</p>
        )}
      </div>

      {loginError && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Login failed
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {loginError.message || 'Invalid username or password'}
              </div>
            </div>
          </div>
        </div>
      )}

      <Button
        type="submit"
        disabled={isLoading}
        className="w-full"
        variant="primary"
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Signing in...
          </>
        ) : (
          'Sign in'
        )}
      </Button>
    </form>
  )
}
