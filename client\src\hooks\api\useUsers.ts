'use client'

/**
 * React Query hooks for user management
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api/client'
import type {
  UserCreate,
  UserRead,
  UserUpdate,
  UserPaginatedResponse,
  UserSummary,
  ListQueryParams,
} from '@/types/api'
import { QueryKeys, MutationKeys } from '@/types/api'

/**
 * Hook for getting paginated users list
 */
export function useUsers(params?: ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.usersList(params),
    queryFn: async (): Promise<UserPaginatedResponse> => {
      const response = await apiClient.getUsers(params)
      return response
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for getting users summary
 */
export function useUsersSummary(limit?: number) {
  return useQuery({
    queryKey: QueryKeys.usersSummary,
    queryFn: async (): Promise<UserSummary[]> => {
      const response = await apiClient.getUsersSummary(limit)
      return response
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for getting a single user
 */
export function useUser(id: number) {
  return useQuery({
    queryKey: QueryKeys.user(id),
    queryFn: async (): Promise<UserRead> => {
      const response = await apiClient.getUser(id)
      return response
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating a new user
 */
export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createUser,
    mutationFn: async (data: UserCreate): Promise<UserRead> => {
      const response = await apiClient.createUser(data)
      return response
    },
    onSuccess: () => {
      // Invalidate users queries to refetch the list
      queryClient.invalidateQueries({ queryKey: QueryKeys.users })
      queryClient.invalidateQueries({ queryKey: QueryKeys.usersSummary })
    },
  })
}

/**
 * Hook for updating a user
 */
export function useUpdateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.updateUser,
    mutationFn: async ({ id, data }: { id: number; data: UserUpdate }): Promise<UserRead> => {
      const response = await apiClient.updateUser(id, data)
      return response
    },
    onSuccess: (data) => {
      // Update the specific user in cache
      queryClient.setQueryData(QueryKeys.user(data.id), data)
      
      // Invalidate users list to refetch
      queryClient.invalidateQueries({ queryKey: QueryKeys.users })
      queryClient.invalidateQueries({ queryKey: QueryKeys.usersSummary })
    },
  })
}

/**
 * Hook for deleting a user
 */
export function useDeleteUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.deleteUser,
    mutationFn: async (id: number): Promise<void> => {
      await apiClient.deleteUser(id)
    },
    onSuccess: (_, id) => {
      // Remove the user from cache
      queryClient.removeQueries({ queryKey: QueryKeys.user(id) })
      
      // Invalidate users list to refetch
      queryClient.invalidateQueries({ queryKey: QueryKeys.users })
      queryClient.invalidateQueries({ queryKey: QueryKeys.usersSummary })
    },
  })
}
