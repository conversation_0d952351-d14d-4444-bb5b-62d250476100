### 5. Workflow: Analyze the Codebase

This workflow outlines a continuous analysis process for maintaining code quality, security, and adherence to standards across both backend and frontend. This is primarily an AI-driven task within the "Verification" phase or as a standalone quality gate.

* **5.1. Discovery & Analysis:**
    * **Task:** Define the specific scope of the analysis (e.g., a specific module, recent changes, entire codebase).
    * **Guidance for AI:** Access the relevant code base sections for both backend (Python) and frontend (TypeScript/JavaScript).
* **5.2. Task Planning:**
    * **Task:** Outline the specific checks and metrics to be applied for the analysis.
    * **Guidance for AI:** Prepare the necessary tools and scripts for linting, type checking, security scanning, and test coverage analysis for both backend and frontend environments.
* **5.3. Implementation (Analysis Execution):**
    * **Task:** Perform automated analysis of the codebase for:
        * **Code duplication:** Identify repetitive code for "DRY" principle violations across Python and TypeScript.
        * **Adherence to coding standards:** Check against "Development Standards", including "SOLID" principles, "Unified Patterns", and engineering-grade quality for both backend and frontend. This includes **ESLint/Prettier** for frontend.
        * **Security vulnerabilities:** Scan for potential issues like insecure API endpoints, database injection risks, or JWT vulnerabilities (backend), as well as XSS, CSRF, and secure token storage issues (frontend).
        * **Code quality:** Assess against "Technical Debt & Quality Metrics" such as "Linting Status (Zero warnings/errors)" and "Type Coverage (100% type annotations)" for both Python (MyPy) and TypeScript.
        * **Maintainability:** Evaluate code readability, complexity, and modularity in alignment with the "5-layer architecture" for backend and **Component-Based Architecture (Atomic Design Methodology)** for frontend.
        * **Performance:** Identify potential bottlenecks, inefficient queries, or unoptimized algorithms (backend), and **Core Web Vitals** issues, bundle size, or rendering performance (frontend).
        * **Documentation:** Verify all public APIs are documented and internal code comments are sufficient, aiming for "All public APIs documented" using tools like **JSDoc/TypeDoc** for frontend.
    * **Guidance for AI:** Execute static analysis tools (e.g., **Ruff, MyPy** for Python; **ESLint, TypeScript checking** for frontend), security scanners, and custom scripts. Analyze bundle sizes and performance metrics. Generate comprehensive reports for all layers.
* **5.4. Verification (Reporting & Flagging):**
    * **Task:** Generate detailed reports of findings, highlighting violations, vulnerabilities, or areas needing improvement. Flag critical issues for immediate attention.
    * **Guidance for AI:** Summarize findings, categorize issues by severity (e.g., error, warning, suggestion), and link directly to relevant code sections and project standards for both backend and frontend. Ensure "Zero Tolerance Policies" are met, or issues are clearly flagged.
* **5.5. Documentation & Handover:**
    * **Task:** Document the analysis results and propose actionable recommendations for remediation.
    * **Guidance for AI:** Create an analysis report and, if applicable, generate tasks for remediation, linking back to the relevant development workflow (e.g., "Refactoring Code", "Fixing Failing Tests").