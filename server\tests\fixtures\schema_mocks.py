import pytest
from src.core.schemas.general.component_schemas import ComponentPaginatedResponseSchema
from src.core.schemas.base import PaginationSchema


@pytest.fixture
def mock_component_search_result():
    """Mock search result for testing."""
    
    return ComponentPaginatedResponseSchema(
        items=[],
        pagination=PaginationSchema(
            page=1,
            size=20,
            total=0,
            pages=0
        )
    )