# src/core/schemas/error.py
"""Error Response Schema Definitions.

This module defines Pydantic schemas for error responses in the Ultimate Electrical
Designer backend API. It provides standardized error response formats for
consistent error handling across all API endpoints.

Key schemas include error responses, validation error details, and structured
error information for client applications.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class ErrorResponseSchema(BaseModel):
    """Schema for standardized error responses.

    Provides a consistent structure for all API error responses
    including error codes, messages, and contextual information.
    """

    code: str = Field(..., description="Unique application-specific error code.")
    detail: str = Field(..., description="A human-readable explanation of the error.")
    category: str = Field(
        ...,
        description="Category of the error (e.g., ClientError, ServerError, Validation).",
    )
    status_code: int = Field(
        ..., description="HTTP status code equivalent (for UI/API compatibility)."
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional context or debugging information."
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "code": "404_001",
                "detail": "Project with ID 'XYZ-123' not found.",
                "category": "ClientError",
                "status_code": 404,
                "metadata": {
                    "project_id": "XYZ-123",
                    "requested_by": "<EMAIL>",
                },
            }
        }
    )


# If you have specific validation error details from Pydantic
class ValidationErrorDetail(BaseModel):
    """Schema for detailed validation error information.

    Provides specific details about validation failures including
    field locations, error types, and descriptive messages.
    """

    loc: list[str | int] = Field(
        ..., description="Location of the validation error in the input data."
    )
    msg: str = Field(..., description="Validation error message.")
    type: str = Field(..., description="Type of validation error.")


class ValidationErrorsResponseSchema(ErrorResponseSchema):
    """Schema for validation error responses with detailed error information.

    Extends the base error response schema to include detailed validation
    error information for client-side error handling and display.
    """

    validation_details: list[ValidationErrorDetail] = Field(
        ..., description="Detailed list of validation errors."
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "code": "400_001",
                "detail": "Input validation failed.",
                "category": "Validation",
                "status_code": 400,
                "validation_details": [
                    {
                        "loc": ["pipe_length"],
                        "msg": "value is not a valid float",
                        "type": "type_error.float",
                    }
                ],
            }
        }
    )
