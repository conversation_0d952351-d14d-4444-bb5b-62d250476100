# Development Handbook
## Ultimate Electrical Designer Application

### Document Information
- **Version**: 1.0
- **Date**: July 2025
- **Target**: Development Team & AI Agents
- **Standards**: Engineering-Grade Development Practices
- **Quality**: Zero-Tolerance Policy

---

## 1. Executive Summary

This development handbook provides comprehensive guidance for building the Ultimate Electrical Designer application with engineering-grade quality standards. The handbook covers complete development workflows, coding standards, testing methodologies, and professional practices required for electrical engineering software development.

### 1.1 Development Philosophy
- **Engineering Excellence**: Immaculate attention to detail in every component
- **Professional Standards**: IEEE, IEC, EN electrical engineering standards compliance
- **Zero-Tolerance Quality**: No warnings, no technical debt, no incomplete implementations
- **Type Safety**: 100% type annotations (Python), strict TypeScript mode
- **Real Testing**: No mocks policy for critical database operations

### 1.2 Current Development Status
- **Backend**: 373/373 tests passing (100% success rate)
- **Frontend**: 66/66 tests passing (100% success rate)
- **Type Coverage**: 97.6% MyPy coverage, strict TypeScript
- **Code Quality**: 99.9% linting compliance
- **Security**: Zero critical vulnerabilities

---

## 2. Development Environment Setup

### 2.1 Prerequisites

#### System Requirements
- **Operating System**: Windows 10+, macOS 10.15+, Ubuntu 20.04+
- **Python**: 3.11+ (latest stable version)
- **Node.js**: 20+ (latest LTS version)
- **Git**: 2.30+ with SSH key configuration
- **Docker**: 24.0+ for containerization
- **PostgreSQL**: 15+ for production database
- **Redis**: 7+ for caching and session management

#### Development Tools
- **IDE**: VS Code with recommended extensions
- **Database**: pgAdmin 4 for PostgreSQL management
- **API Testing**: Postman or Insomnia for API development
- **Version Control**: Git with professional commit conventions

### 2.2 Project Setup

#### Initial Repository Setup
```bash
# Clone repository
git clone https://github.com/company/ultimate-electrical-designer.git
cd ultimate-electrical-designer

# Verify project structure
ls -la
# Expected: server/, client/, docs/, Makefile, *.md files

# Install all dependencies
make install
```

#### Backend Setup (Python/FastAPI)
```bash
# Navigate to backend directory
cd server/

# Install Poetry (if not already installed)
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Activate virtual environment
poetry shell

# Verify installation
poetry run python --version  # Should show Python 3.11+
poetry run python -c "import fastapi; print(fastapi.__version__)"  # Should show FastAPI 0.115+

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Initialize database
poetry run python main.py migrate
poetry run python main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'

# Run development server
poetry run python main.py run --reload
```

#### Frontend Setup (Next.js/TypeScript)
```bash
# Navigate to frontend directory
cd client/

# Install dependencies
npm install

# Verify installation
node --version  # Should show Node.js 20+
npm run type-check  # Should pass without errors

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Run development server
npm run dev
```

### 2.3 Development Tools Configuration

#### VS Code Extensions (Required)
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.pylint",
    "ms-python.mypy-type-checker",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-playwright.playwright",
    "ms-vscode.vscode-json"
  ]
}
```

#### VS Code Settings
```json
{
  "python.defaultInterpreterPath": "./server/.venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": false,
  "python.linting.mypyEnabled": true,
  "typescript.preferences.strictMode": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

#### Git Configuration
```bash
# Global configuration
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main
git config --global pull.rebase true

# Project-specific hooks
# Pre-commit hooks are configured in .pre-commit-config.yaml
pre-commit install
```

---

## 3. Coding Standards & Best Practices

### 3.1 Python/Backend Standards

#### Code Style & Formatting
```python
# Use Ruff for formatting and linting
# Configuration in pyproject.toml

[tool.ruff]
line-length = 100
target-version = "py311"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]

# Example: Proper function definition
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

class ElectricalComponent(BaseModel):
    """Professional electrical component model with complete specifications."""
    
    id: str
    name: str
    specifications: Dict[str, Any]
    compliance_standards: List[str]
    
    def validate_ieee_compliance(self) -> bool:
        """Validate component against IEEE standards."""
        return "IEEE-141" in self.compliance_standards

async def calculate_electrical_load(
    components: List[ElectricalComponent],
    voltage: float,
    power_factor: float = 0.85
) -> Dict[str, float]:
    """
    Calculate electrical load for components.
    
    Args:
        components: List of electrical components
        voltage: System voltage in volts
        power_factor: Power factor (default: 0.85)
        
    Returns:
        Dictionary with calculation results
        
    Raises:
        ValueError: If voltage is not positive
        StandardsError: If calculation violates standards
    """
    if voltage <= 0:
        raise ValueError("Voltage must be positive")
    
    # Implementation with proper error handling
    total_load = sum(comp.specifications.get("power_rating", 0) for comp in components)
    
    return {
        "total_load": total_load,
        "current": total_load / (voltage * power_factor),
        "power_factor": power_factor
    }
```

#### Type Safety Requirements
```python
# ALL functions must have complete type annotations
from typing import Union, Optional, List, Dict, Any, Tuple
from datetime import datetime

# Correct: Complete type annotations
async def process_calculation(
    calculation_id: str,
    parameters: Dict[str, Any],
    user_id: str
) -> Tuple[Dict[str, float], List[str]]:
    """Process electrical calculation with complete type safety."""
    pass

# Incorrect: Missing type annotations
async def process_calculation(calculation_id, parameters, user_id):
    """This will fail type checking."""
    pass
```

#### Error Handling Patterns
```python
# Always use unified error handling decorator
from core.errors.unified_error_handler import handle_database_errors
from core.errors.exceptions import ElectricalError, StandardsError

@handle_database_errors
async def create_component(component_data: ComponentCreate) -> Component:
    """Create component with unified error handling."""
    try:
        # Validate electrical specifications
        if component_data.specifications.get("voltage_rating", 0) <= 0:
            raise ElectricalError("Voltage rating must be positive")
        
        # Check standards compliance
        if not component_data.compliance_standards:
            raise StandardsError("Component must comply with at least one standard")
        
        # Implementation
        return await component_service.create(component_data)
    
    except ElectricalError:
        # Re-raise electrical errors
        raise
    except Exception as e:
        # Handle unexpected errors
        raise ElectricalError(f"Component creation failed: {str(e)}")
```

#### Database Operations
```python
# Always use repository pattern with type safety
from typing import Generic, TypeVar, Type, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession

T = TypeVar('T')

class BaseRepository(Generic[T]):
    """Base repository with type safety."""
    
    def __init__(self, model: Type[T], session: AsyncSession) -> None:
        self.model = model
        self.session = session
    
    async def create(self, entity: T) -> T:
        """Create entity with transaction safety."""
        try:
            self.session.add(entity)
            await self.session.commit()
            await self.session.refresh(entity)
            return entity
        except Exception:
            await self.session.rollback()
            raise
    
    async def get_by_id(self, id: str) -> Optional[T]:
        """Get entity by ID with proper typing."""
        return await self.session.get(self.model, id)
```

### 3.2 TypeScript/Frontend Standards

#### Code Style & Formatting
```typescript
// Use ESLint and Prettier for consistent formatting
// Configuration in .eslintrc.js and .prettierrc

// Example: Proper component definition
import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ElectricalComponentProps {
  id: string;
  name: string;
  specifications: ElectricalSpecifications;
  onUpdate: (id: string, data: Partial<ElectricalSpecifications>) => void;
}

interface ElectricalSpecifications {
  voltage_rating: number;
  current_rating: number;
  power_rating: number;
  power_factor: number;
  efficiency: number;
}

export const ElectricalComponent: React.FC<ElectricalComponentProps> = ({
  id,
  name,
  specifications,
  onUpdate
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editData, setEditData] = useState<ElectricalSpecifications>(specifications);

  const handleSave = useCallback(() => {
    onUpdate(id, editData);
    setIsEditing(false);
  }, [id, editData, onUpdate]);

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>{name}</CardTitle>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <EditForm
            data={editData}
            onChange={setEditData}
            onSave={handleSave}
            onCancel={() => setIsEditing(false)}
          />
        ) : (
          <DisplayView
            specifications={specifications}
            onEdit={() => setIsEditing(true)}
          />
        )}
      </CardContent>
    </Card>
  );
};
```

#### Type Safety Requirements
```typescript
// ALL components must have complete type definitions
import { ReactNode } from 'react';

// Correct: Complete interface definition
interface CalculationResultProps {
  calculationId: string;
  results: CalculationResults;
  standards: string[];
  onExport: (format: 'pdf' | 'excel') => void;
  children?: ReactNode;
}

interface CalculationResults {
  total_load: number;
  current_load: number;
  power_factor: number;
  compliance_status: ComplianceStatus;
}

interface ComplianceStatus {
  overall_compliance: boolean;
  standard_results: StandardResult[];
}

interface StandardResult {
  standard: string;
  compliant: boolean;
  message: string;
  recommendations: string[];
}

// Incorrect: Missing type definitions
const CalculationResult = ({ calculationId, results, standards, onExport }) => {
  // This will fail type checking
};
```

#### API Integration Patterns
```typescript
// Use React Query for all API interactions
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

// Correct: Typed API hooks
export const useElectricalComponents = (filters?: ComponentFilters) => {
  return useQuery({
    queryKey: ['components', filters],
    queryFn: () => apiClient.get<ComponentResponse[]>('/components', { params: filters }),
    select: (response) => response.data,
    staleTime: 2 * 60 * 1000, // 2 minutes
    onError: (error: ApiError) => {
      toast.error(`Failed to load components: ${error.message}`);
    }
  });
};

export const useCreateComponent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: ComponentCreateData) => 
      apiClient.post<ComponentResponse>('/components', data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['components'] });
      toast.success('Component created successfully');
    },
    onError: (error: ApiError) => {
      toast.error(`Failed to create component: ${error.message}`);
    }
  });
};

// Error handling types
interface ApiError {
  message: string;
  code: string;
  details?: Record<string, any>;
}

interface ComponentResponse {
  id: string;
  name: string;
  specifications: ElectricalSpecifications;
  created_at: string;
  updated_at: string;
}
```

### 3.3 Documentation Standards

#### Code Comments
```python
# Python: Use docstrings for all public functions
def calculate_voltage_drop(
    cable_length: float,
    current: float,
    resistance: float,
    reactance: float
) -> float:
    """
    Calculate voltage drop in electrical cable.
    
    Implements IEEE-141 standard for voltage drop calculation.
    
    Args:
        cable_length: Length of cable in meters
        current: Current flowing through cable in amperes
        resistance: Cable resistance per unit length in ohms/meter
        reactance: Cable reactance per unit length in ohms/meter
        
    Returns:
        Voltage drop in volts
        
    Raises:
        ValueError: If any parameter is negative
        
    Example:
        >>> calculate_voltage_drop(100, 25, 0.001, 0.0001)
        2.5006249023437595
    """
    if any(param < 0 for param in [cable_length, current, resistance, reactance]):
        raise ValueError("All parameters must be non-negative")
    
    impedance = (resistance ** 2 + reactance ** 2) ** 0.5
    return cable_length * current * impedance
```

```typescript
// TypeScript: Use JSDoc for complex functions
/**
 * Calculate electrical load for multiple components
 * 
 * @param components - Array of electrical components
 * @param parameters - Calculation parameters
 * @returns Promise resolving to calculation results
 * 
 * @example
 * ```typescript
 * const result = await calculateElectricalLoad([motor1, motor2], {
 *   voltage: 480,
 *   power_factor: 0.85
 * });
 * ```
 */
export const calculateElectricalLoad = async (
  components: ElectricalComponent[],
  parameters: CalculationParameters
): Promise<LoadCalculationResult> => {
  // Implementation
};
```

---

## 4. Testing Methodology

### 4.1 Testing Philosophy

#### Zero-Mocks Policy
- **Real Database**: All database tests use real database connections
- **Real API Calls**: Integration tests use actual API endpoints
- **Real Services**: No mocking of critical business logic
- **Professional Validation**: Independent verification of electrical calculations

#### Test Categories
- **Unit Tests**: Individual function and class testing
- **Integration Tests**: Component interaction testing
- **API Tests**: End-to-end API workflow testing
- **E2E Tests**: Complete user journey testing
- **Performance Tests**: Response time and throughput validation
- **Security Tests**: Authentication, authorization, and vulnerability testing

### 4.2 Backend Testing (Python/Pytest)

#### Test Structure
```python
# tests/conftest.py - Test configuration
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app import create_app
from core.database import get_async_session

@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine."""
    engine = create_async_engine(
        "sqlite+aiosqlite:///./test.db",
        echo=False
    )
    return engine

@pytest.fixture(scope="session")
async def test_session(test_engine):
    """Create test database session."""
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session

@pytest.fixture
def client(test_session):
    """Create test client with database session."""
    app = create_app()
    
    # Override database dependency
    app.dependency_overrides[get_async_session] = lambda: test_session
    
    with TestClient(app) as client:
        yield client
```

#### Unit Test Example
```python
# tests/test_calculations.py
import pytest
from decimal import Decimal

from core.calculations.electrical import ElectricalCalculator
from core.errors.exceptions import ElectricalError

class TestElectricalCalculator:
    """Test electrical calculation functions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.calculator = ElectricalCalculator()
    
    def test_voltage_drop_calculation(self):
        """Test voltage drop calculation with valid parameters."""
        # Arrange
        cable_length = 100  # meters
        current = 25  # amperes
        resistance = 0.001  # ohms/meter
        reactance = 0.0001  # ohms/meter
        
        # Act
        result = self.calculator.calculate_voltage_drop(
            cable_length, current, resistance, reactance
        )
        
        # Assert
        expected = 2.5006249023437595
        assert abs(result - expected) < 0.0001
    
    def test_voltage_drop_negative_parameters(self):
        """Test voltage drop calculation with negative parameters."""
        # Arrange
        cable_length = -100  # Invalid: negative length
        current = 25
        resistance = 0.001
        reactance = 0.0001
        
        # Act & Assert
        with pytest.raises(ElectricalError) as exc_info:
            self.calculator.calculate_voltage_drop(
                cable_length, current, resistance, reactance
            )
        
        assert "negative" in str(exc_info.value).lower()
    
    @pytest.mark.parametrize("voltage,current,power_factor,expected", [
        (480, 25, 0.85, 10200),
        (240, 10, 0.9, 2160),
        (120, 5, 1.0, 600),
    ])
    def test_power_calculation(self, voltage, current, power_factor, expected):
        """Test power calculation with various parameters."""
        # Act
        result = self.calculator.calculate_power(voltage, current, power_factor)
        
        # Assert
        assert abs(result - expected) < 0.1
```

#### Integration Test Example
```python
# tests/test_component_api.py
import pytest
from httpx import AsyncClient

from core.models.general.component import Component
from core.schemas.general.component_schemas import ComponentCreate

@pytest.mark.asyncio
@pytest.mark.integration
class TestComponentAPI:
    """Test component API endpoints."""
    
    async def test_create_component_success(self, client: AsyncClient, admin_token: str):
        """Test successful component creation."""
        # Arrange
        component_data = {
            "name": "Test Motor",
            "description": "Test 3-phase motor",
            "manufacturer": "TestCorp",
            "model_number": "TM-100",
            "category_id": "cat_motors",
            "type_id": "type_induction",
            "specifications": {
                "voltage_rating": 480,
                "current_rating": 25,
                "power_rating": 11190,
                "power_factor": 0.85,
                "efficiency": 0.92
            },
            "compliance_standards": ["IEEE-112", "IEC-60034"]
        }
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Act
        response = await client.post(
            "/api/v1/components",
            json=component_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 201
        
        result = response.json()
        assert result["success"] is True
        assert result["data"]["name"] == "Test Motor"
        assert result["data"]["specifications"]["voltage_rating"] == 480
        assert "IEEE-112" in result["data"]["compliance_standards"]
    
    async def test_create_component_invalid_specifications(
        self, client: AsyncClient, admin_token: str
    ):
        """Test component creation with invalid specifications."""
        # Arrange
        component_data = {
            "name": "Invalid Motor",
            "category_id": "cat_motors",
            "type_id": "type_induction",
            "specifications": {
                "voltage_rating": -480,  # Invalid: negative voltage
                "current_rating": 25,
                "power_rating": 11190
            }
        }
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Act
        response = await client.post(
            "/api/v1/components",
            json=component_data,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 400
        
        result = response.json()
        assert result["success"] is False
        assert "voltage_rating" in result["error"]["details"]["field"]
```

### 4.3 Frontend Testing (TypeScript/Vitest)

#### Test Structure
```typescript
// tests/setup.ts - Test configuration
import { beforeAll, afterEach, afterAll } from 'vitest';
import { server } from './mocks/server';
import '@testing-library/jest-dom';

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

#### Component Test Example
```typescript
// tests/components/ElectricalComponent.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { ElectricalComponent } from '@/components/ElectricalComponent';
import { ElectricalSpecifications } from '@/types/component';

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
};

const renderWithProviders = (ui: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  
  return render(
    <QueryClientProvider client={queryClient}>
      {ui}
    </QueryClientProvider>
  );
};

describe('ElectricalComponent', () => {
  const mockSpecs: ElectricalSpecifications = {
    voltage_rating: 480,
    current_rating: 25,
    power_rating: 11190,
    power_factor: 0.85,
    efficiency: 0.92
  };

  it('should render component with specifications', () => {
    // Arrange
    const mockOnUpdate = vi.fn();
    
    // Act
    renderWithProviders(
      <ElectricalComponent
        id="test-component"
        name="Test Motor"
        specifications={mockSpecs}
        onUpdate={mockOnUpdate}
      />
    );
    
    // Assert
    expect(screen.getByText('Test Motor')).toBeInTheDocument();
    expect(screen.getByText('480')).toBeInTheDocument(); // Voltage
    expect(screen.getByText('25')).toBeInTheDocument(); // Current
    expect(screen.getByText('11190')).toBeInTheDocument(); // Power
  });

  it('should handle edit mode correctly', async () => {
    // Arrange
    const mockOnUpdate = vi.fn();
    
    renderWithProviders(
      <ElectricalComponent
        id="test-component"
        name="Test Motor"
        specifications={mockSpecs}
        onUpdate={mockOnUpdate}
      />
    );
    
    // Act
    fireEvent.click(screen.getByText('Edit'));
    
    // Assert
    await waitFor(() => {
      expect(screen.getByDisplayValue('480')).toBeInTheDocument();
    });
    
    // Act - Change voltage
    const voltageInput = screen.getByDisplayValue('480');
    fireEvent.change(voltageInput, { target: { value: '600' } });
    fireEvent.click(screen.getByText('Save'));
    
    // Assert
    await waitFor(() => {
      expect(mockOnUpdate).toHaveBeenCalledWith('test-component', {
        ...mockSpecs,
        voltage_rating: 600
      });
    });
  });

  it('should validate electrical specifications', async () => {
    // Arrange
    const mockOnUpdate = vi.fn();
    
    renderWithProviders(
      <ElectricalComponent
        id="test-component"
        name="Test Motor"
        specifications={mockSpecs}
        onUpdate={mockOnUpdate}
      />
    );
    
    // Act
    fireEvent.click(screen.getByText('Edit'));
    
    const voltageInput = screen.getByDisplayValue('480');
    fireEvent.change(voltageInput, { target: { value: '-100' } }); // Invalid
    fireEvent.click(screen.getByText('Save'));
    
    // Assert
    await waitFor(() => {
      expect(screen.getByText(/voltage must be positive/i)).toBeInTheDocument();
    });
    
    expect(mockOnUpdate).not.toHaveBeenCalled();
  });
});
```

#### API Integration Test Example
```typescript
// tests/hooks/useElectricalComponents.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { useElectricalComponents } from '@/hooks/useElectricalComponents';
import { server } from '../mocks/server';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useElectricalComponents', () => {
  beforeEach(() => {
    server.resetHandlers();
  });

  it('should fetch components successfully', async () => {
    // Arrange
    const wrapper = createWrapper();
    
    // Act
    const { result } = renderHook(() => useElectricalComponents(), {
      wrapper,
    });
    
    // Assert
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    
    expect(result.current.data).toHaveLength(2);
    expect(result.current.data?.[0]).toHaveProperty('name');
    expect(result.current.data?.[0]).toHaveProperty('specifications');
  });

  it('should handle API errors gracefully', async () => {
    // Arrange
    server.use(
      rest.get('/api/v1/components', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
      })
    );
    
    const wrapper = createWrapper();
    
    // Act
    const { result } = renderHook(() => useElectricalComponents(), {
      wrapper,
    });
    
    // Assert
    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });
    
    expect(result.current.error).toBeDefined();
  });
});
```

### 4.4 E2E Testing (Playwright)

#### E2E Test Example
```typescript
// tests/e2e/electrical-calculations.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Electrical Calculations', () => {
  test.beforeEach(async ({ page }) => {
    // Login as engineer
    await page.goto('/login');
    await page.fill('[data-testid="username"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'engineer123');
    await page.click('[data-testid="login-button"]');
    
    // Wait for dashboard
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
  });

  test('should perform load calculation', async ({ page }) => {
    // Navigate to calculations
    await page.click('[data-testid="calculations-nav"]');
    await page.click('[data-testid="new-calculation"]');
    
    // Select calculation type
    await page.selectOption('[data-testid="calculation-type"]', 'load_calculation');
    
    // Add components
    await page.click('[data-testid="add-component"]');
    await page.selectOption('[data-testid="component-select"]', 'comp_motor_10hp');
    await page.fill('[data-testid="component-quantity"]', '2');
    await page.fill('[data-testid="diversity-factor"]', '0.8');
    await page.click('[data-testid="add-component-confirm"]');
    
    // Set parameters
    await page.fill('[data-testid="voltage"]', '480');
    await page.fill('[data-testid="power-factor"]', '0.85');
    await page.selectOption('[data-testid="installation-method"]', 'cable_tray');
    
    // Perform calculation
    await page.click('[data-testid="calculate-button"]');
    
    // Wait for results
    await expect(page.locator('[data-testid="calculation-results"]')).toBeVisible();
    
    // Verify results
    const totalLoad = await page.locator('[data-testid="total-load"]').textContent();
    expect(totalLoad).toContain('11936'); // Expected load for 2 motors with diversity
    
    const compliance = await page.locator('[data-testid="compliance-status"]').textContent();
    expect(compliance).toContain('IEEE-141 Compliant');
  });

  test('should validate standards compliance', async ({ page }) => {
    // Navigate to existing calculation
    await page.goto('/calculations/calc_123');
    
    // Validate against standards
    await page.click('[data-testid="validate-standards"]');
    await page.check('[data-testid="ieee-141"]');
    await page.check('[data-testid="iec-60364"]');
    await page.click('[data-testid="validate-button"]');
    
    // Wait for validation results
    await expect(page.locator('[data-testid="validation-results"]')).toBeVisible();
    
    // Verify compliance
    const ieeeResult = await page.locator('[data-testid="ieee-141-result"]').textContent();
    expect(ieeeResult).toContain('Compliant');
    
    const iecResult = await page.locator('[data-testid="iec-60364-result"]').textContent();
    expect(iecResult).toContain('Compliant');
  });

  test('should export calculation report', async ({ page }) => {
    // Navigate to calculation
    await page.goto('/calculations/calc_123');
    
    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-pdf"]');
    
    // Verify download
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toBe('electrical-calculation-report.pdf');
    
    // Verify file is not empty
    const path = await download.path();
    expect(path).toBeTruthy();
  });
});
```

### 4.5 Performance Testing

#### Load Testing Example
```python
# tests/performance/test_calculation_performance.py
import pytest
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

from core.calculations.electrical import ElectricalCalculator
from core.schemas.general.component_schemas import ComponentCreate

@pytest.mark.performance
class TestCalculationPerformance:
    """Test calculation performance under load."""
    
    def setup_method(self):
        """Set up performance test fixtures."""
        self.calculator = ElectricalCalculator()
        self.test_components = [
            self.create_test_component(f"Motor_{i}", power=1000 + i * 100)
            for i in range(100)
        ]
    
    def create_test_component(self, name: str, power: int) -> ComponentCreate:
        """Create test component."""
        return ComponentCreate(
            name=name,
            category_id="cat_motors",
            type_id="type_induction",
            specifications={
                "voltage_rating": 480,
                "current_rating": power / (480 * 0.85),
                "power_rating": power,
                "power_factor": 0.85,
                "efficiency": 0.92
            },
            compliance_standards=["IEEE-112"]
        )
    
    def test_single_calculation_performance(self):
        """Test single calculation performance."""
        # Arrange
        start_time = time.time()
        
        # Act
        result = self.calculator.calculate_load(
            self.test_components[:10],
            voltage=480,
            power_factor=0.85
        )
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000  # Convert to ms
        
        # Assert
        assert execution_time < 50  # Must complete within 50ms
        assert result["total_load"] > 0
    
    def test_concurrent_calculations(self):
        """Test concurrent calculation performance."""
        # Arrange
        def perform_calculation():
            return self.calculator.calculate_load(
                self.test_components[:5],
                voltage=480,
                power_factor=0.85
            )
        
        start_time = time.time()
        
        # Act - Run 20 concurrent calculations
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(perform_calculation) for _ in range(20)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000
        
        # Assert
        assert execution_time < 500  # Must complete within 500ms
        assert len(results) == 20
        assert all(result["total_load"] > 0 for result in results)
    
    def test_large_component_set_performance(self):
        """Test performance with large component sets."""
        # Arrange
        start_time = time.time()
        
        # Act
        result = self.calculator.calculate_load(
            self.test_components,  # 100 components
            voltage=480,
            power_factor=0.85
        )
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000
        
        # Assert
        assert execution_time < 200  # Must complete within 200ms
        assert result["total_load"] > 0
        assert len(result["component_loads"]) == 100
```

---

## 5. Database Development

### 5.1 Database Schema Management

#### Migration Creation
```bash
# Create new migration
cd server/src
poetry run alembic revision --autogenerate -m "Add electrical nodes table"

# Review generated migration
# Edit migration file if needed

# Apply migration
poetry run alembic upgrade head

# Rollback if needed
poetry run alembic downgrade -1
```

#### Migration Example
```python
# migrations/versions/001_add_electrical_nodes.py
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

def upgrade():
    """Add electrical nodes table."""
    op.create_table(
        'electrical_nodes',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('node_type', sa.String(length=50), nullable=False),
        sa.Column('voltage_level', sa.Float(), nullable=False),
        sa.Column('location', sa.String(length=255), nullable=True),
        sa.Column('coordinates', sa.JSON(), nullable=True),
        sa.Column('equipment_id', sa.String(), nullable=True),
        sa.Column('project_id', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['equipment_id'], ['main_equipment.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('idx_electrical_nodes_type', 'electrical_nodes', ['node_type'])
    op.create_index('idx_electrical_nodes_voltage', 'electrical_nodes', ['voltage_level'])
    op.create_index('idx_electrical_nodes_project', 'electrical_nodes', ['project_id'])

def downgrade():
    """Remove electrical nodes table."""
    op.drop_index('idx_electrical_nodes_project', table_name='electrical_nodes')
    op.drop_index('idx_electrical_nodes_voltage', table_name='electrical_nodes')
    op.drop_index('idx_electrical_nodes_type', table_name='electrical_nodes')
    op.drop_table('electrical_nodes')
```

### 5.2 Database Seeding

#### Seed Data Creation
```python
# scripts/seed_data.py
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from core.models.general.component_category import ComponentCategory
from core.models.general.component_type import ComponentType
from core.models.general.component import Component

async def seed_component_categories():
    """Seed component categories."""
    categories = [
        {
            "name": "Motors",
            "description": "Electric motors and drives",
            "category_code": "MOT",
            "specifications_template": {
                "voltage_rating": {"type": "number", "unit": "V", "required": True},
                "current_rating": {"type": "number", "unit": "A", "required": True},
                "power_rating": {"type": "number", "unit": "W", "required": True},
                "power_factor": {"type": "number", "unit": "", "required": True},
                "efficiency": {"type": "number", "unit": "", "required": True},
                "frequency": {"type": "number", "unit": "Hz", "required": True}
            }
        },
        {
            "name": "Transformers",
            "description": "Power and distribution transformers",
            "category_code": "TRF",
            "specifications_template": {
                "voltage_primary": {"type": "number", "unit": "V", "required": True},
                "voltage_secondary": {"type": "number", "unit": "V", "required": True},
                "power_rating": {"type": "number", "unit": "VA", "required": True},
                "frequency": {"type": "number", "unit": "Hz", "required": True},
                "efficiency": {"type": "number", "unit": "", "required": True}
            }
        },
        # Add more categories...
    ]
    
    for category_data in categories:
        category = ComponentCategory(**category_data)
        session.add(category)
    
    await session.commit()

async def seed_component_types():
    """Seed component types."""
    types = [
        {
            "name": "Induction Motor",
            "description": "Three-phase induction motors",
            "type_code": "IND",
            "category_id": "cat_motors",
            "specifications_template": {
                "voltage_rating": {"type": "number", "unit": "V", "required": True},
                "current_rating": {"type": "number", "unit": "A", "required": True},
                "power_rating": {"type": "number", "unit": "W", "required": True},
                "power_factor": {"type": "number", "unit": "", "required": True},
                "efficiency": {"type": "number", "unit": "", "required": True},
                "frequency": {"type": "number", "unit": "Hz", "required": True},
                "poles": {"type": "integer", "unit": "", "required": True},
                "rpm": {"type": "number", "unit": "rpm", "required": True}
            },
            "default_specifications": {
                "frequency": 60,
                "poles": 4,
                "power_factor": 0.85,
                "efficiency": 0.90
            }
        },
        # Add more types...
    ]
    
    for type_data in types:
        component_type = ComponentType(**type_data)
        session.add(component_type)
    
    await session.commit()

async def main():
    """Run database seeding."""
    engine = create_async_engine("sqlite+aiosqlite:///./data/app_dev.db")
    
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        await seed_component_categories()
        await seed_component_types()
        print("Database seeding completed successfully")

if __name__ == "__main__":
    asyncio.run(main())
```

### 5.3 Database Maintenance

#### Backup and Restore
```bash
# PostgreSQL backup
pg_dump -h localhost -U postgres -d ultimate_electrical_designer > backup_$(date +%Y%m%d).sql

# PostgreSQL restore
psql -h localhost -U postgres -d ultimate_electrical_designer < backup_20250716.sql

# SQLite backup (development)
cp server/data/app_dev.db server/data/backup_$(date +%Y%m%d).db
```

#### Database Monitoring
```python
# scripts/db_monitor.py
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text

async def check_database_health():
    """Check database health and performance."""
    engine = create_async_engine("postgresql://user:pass@localhost/db")
    
    async with engine.begin() as conn:
        # Check connection
        result = await conn.execute(text("SELECT 1"))
        print(f"Database connection: {'OK' if result.scalar() == 1 else 'FAILED'}")
        
        # Check table sizes
        result = await conn.execute(text("""
            SELECT 
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        """))
        
        print("\nTable sizes:")
        for row in result:
            print(f"  {row.tablename}: {row.size}")
        
        # Check slow queries
        result = await conn.execute(text("""
            SELECT 
                query,
                calls,
                mean_exec_time,
                total_exec_time
            FROM pg_stat_statements
            WHERE mean_exec_time > 100
            ORDER BY mean_exec_time DESC
            LIMIT 5
        """))
        
        print("\nSlow queries (>100ms):")
        for row in result:
            print(f"  {row.mean_exec_time:.2f}ms: {row.query[:100]}...")

if __name__ == "__main__":
    asyncio.run(check_database_health())
```

---

## 6. Security Development

### 6.1 Authentication Implementation

#### JWT Token Management
```python
# core/security/jwt_handler.py
import jwt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from core.config.settings import settings
from core.errors.exceptions import AuthenticationError

class JWTHandler:
    """JWT token handler with professional security practices."""
    
    SECRET_KEY = settings.SECRET_KEY
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 1440  # 24 hours
    
    @classmethod
    def create_access_token(
        cls, 
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token."""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=cls.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        })
        
        return jwt.encode(to_encode, cls.SECRET_KEY, algorithm=cls.ALGORITHM)
    
    @classmethod
    def verify_token(cls, token: str) -> Dict[str, Any]:
        """Verify JWT token."""
        try:
            payload = jwt.decode(token, cls.SECRET_KEY, algorithms=[cls.ALGORITHM])
            
            # Verify token type
            if payload.get("type") != "access":
                raise AuthenticationError("Invalid token type")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.JWTError:
            raise AuthenticationError("Invalid token")
    
    @classmethod
    def refresh_token(cls, refresh_token: str) -> str:
        """Refresh JWT token."""
        try:
            payload = jwt.decode(refresh_token, cls.SECRET_KEY, algorithms=[cls.ALGORITHM])
            
            if payload.get("type") != "refresh":
                raise AuthenticationError("Invalid refresh token")
            
            # Create new access token
            new_payload = {
                "sub": payload["sub"],
                "role": payload["role"],
                "permissions": payload["permissions"]
            }
            
            return cls.create_access_token(new_payload)
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Refresh token has expired")
        except jwt.JWTError:
            raise AuthenticationError("Invalid refresh token")
```

#### Password Security
```python
# core/security/password_handler.py
from passlib.context import CryptContext
from passlib.hash import bcrypt
import re

class PasswordHandler:
    """Password handling with professional security practices."""
    
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    # Password complexity requirements
    MIN_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGITS = True
    REQUIRE_SPECIAL = True
    
    @classmethod
    def hash_password(cls, password: str) -> str:
        """Hash password using bcrypt."""
        return cls.pwd_context.hash(password)
    
    @classmethod
    def verify_password(cls, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return cls.pwd_context.verify(plain_password, hashed_password)
    
    @classmethod
    def validate_password_strength(cls, password: str) -> Dict[str, bool]:
        """Validate password strength."""
        validations = {
            "length": len(password) >= cls.MIN_LENGTH,
            "uppercase": bool(re.search(r'[A-Z]', password)) if cls.REQUIRE_UPPERCASE else True,
            "lowercase": bool(re.search(r'[a-z]', password)) if cls.REQUIRE_LOWERCASE else True,
            "digits": bool(re.search(r'\d', password)) if cls.REQUIRE_DIGITS else True,
            "special": bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password)) if cls.REQUIRE_SPECIAL else True
        }
        
        return validations
    
    @classmethod
    def is_password_valid(cls, password: str) -> bool:
        """Check if password meets all requirements."""
        validations = cls.validate_password_strength(password)
        return all(validations.values())
```

### 6.2 Input Validation

#### Request Validation
```python
# core/security/input_validators.py
import re
from typing import Any, Dict, List
from pydantic import BaseModel, validator

class ElectricalSpecificationValidator(BaseModel):
    """Validator for electrical specifications."""
    
    voltage_rating: float
    current_rating: float
    power_rating: float
    power_factor: float
    efficiency: float
    
    @validator('voltage_rating')
    def validate_voltage(cls, v):
        if v <= 0:
            raise ValueError('Voltage rating must be positive')
        if v > 1000000:  # 1MV limit
            raise ValueError('Voltage rating exceeds maximum limit')
        return v
    
    @validator('current_rating')
    def validate_current(cls, v):
        if v <= 0:
            raise ValueError('Current rating must be positive')
        if v > 100000:  # 100kA limit
            raise ValueError('Current rating exceeds maximum limit')
        return v
    
    @validator('power_factor')
    def validate_power_factor(cls, v):
        if not 0 <= v <= 1:
            raise ValueError('Power factor must be between 0 and 1')
        return v
    
    @validator('efficiency')
    def validate_efficiency(cls, v):
        if not 0 <= v <= 1:
            raise ValueError('Efficiency must be between 0 and 1')
        return v

class SecurityValidator:
    """Security validation utilities."""
    
    @staticmethod
    def validate_sql_injection(value: str) -> bool:
        """Check for SQL injection patterns."""
        sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(--|/\*|\*/)",
            r"(\b(OR|AND)\b.*=.*)",
            r"(;|\|\||&&)"
        ]
        
        for pattern in sql_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                return False
        return True
    
    @staticmethod
    def validate_xss(value: str) -> bool:
        """Check for XSS patterns."""
        xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>"
        ]
        
        for pattern in xss_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                return False
        return True
    
    @staticmethod
    def sanitize_input(value: str) -> str:
        """Sanitize input string."""
        # Remove potentially dangerous characters
        value = re.sub(r'[<>"\']', '', value)
        value = re.sub(r'javascript:', '', value, flags=re.IGNORECASE)
        value = re.sub(r'on\w+\s*=', '', value, flags=re.IGNORECASE)
        
        return value.strip()
```

### 6.3 Security Testing

#### Security Test Example
```python
# tests/security/test_security_validation.py
import pytest
from fastapi.testclient import TestClient

from core.security.input_validators import SecurityValidator
from core.errors.exceptions import SecurityError

class TestSecurityValidation:
    """Test security validation functions."""
    
    def test_sql_injection_detection(self):
        """Test SQL injection detection."""
        # Arrange
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'; DELETE FROM users WHERE 't'='t",
            "1 UNION SELECT * FROM users",
        ]
        
        # Act & Assert
        for input_value in malicious_inputs:
            assert not SecurityValidator.validate_sql_injection(input_value)
    
    def test_xss_detection(self):
        """Test XSS detection."""
        # Arrange
        malicious_inputs = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src='javascript:alert(\"XSS\")'></iframe>",
        ]
        
        # Act & Assert
        for input_value in malicious_inputs:
            assert not SecurityValidator.validate_xss(input_value)
    
    def test_input_sanitization(self):
        """Test input sanitization."""
        # Arrange
        test_cases = [
            ("Normal text", "Normal text"),
            ("<script>alert('XSS')</script>", "alert('XSS')"),
            ("javascript:alert('XSS')", "alert('XSS')"),
            ('onclick="alert(\'XSS\')"', '"alert(\'XSS\')"'),
        ]
        
        # Act & Assert
        for input_value, expected in test_cases:
            result = SecurityValidator.sanitize_input(input_value)
            assert result == expected

@pytest.mark.security
class TestAPISecurityVulnerabilities:
    """Test API security vulnerabilities."""
    
    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to protected endpoints."""
        # Arrange
        protected_endpoints = [
            "/api/v1/components",
            "/api/v1/users",
            "/api/v1/calculations",
        ]
        
        # Act & Assert
        for endpoint in protected_endpoints:
            response = client.get(endpoint)
            assert response.status_code == 401
    
    def test_sql_injection_in_api(self, client: TestClient, admin_token: str):
        """Test SQL injection protection in API endpoints."""
        # Arrange
        malicious_payload = {
            "name": "'; DROP TABLE components; --",
            "description": "Malicious component",
            "category_id": "cat_motors",
            "type_id": "type_induction"
        }
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Act
        response = client.post(
            "/api/v1/components",
            json=malicious_payload,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 400
        assert "validation" in response.json()["error"]["message"].lower()
    
    def test_xss_protection_in_api(self, client: TestClient, admin_token: str):
        """Test XSS protection in API endpoints."""
        # Arrange
        malicious_payload = {
            "name": "<script>alert('XSS')</script>",
            "description": "<img src=x onerror=alert('XSS')>",
            "category_id": "cat_motors",
            "type_id": "type_induction"
        }
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Act
        response = client.post(
            "/api/v1/components",
            json=malicious_payload,
            headers=headers
        )
        
        # Assert
        assert response.status_code == 400
        assert "validation" in response.json()["error"]["message"].lower()
```

---

## 7. Performance Optimization

### 7.1 Backend Performance

#### Database Query Optimization
```python
# core/repositories/optimized_queries.py
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload, joinedload
from typing import List, Optional, Dict, Any

class OptimizedComponentRepository:
    """Optimized component repository with performance enhancements."""
    
    async def get_components_with_relationships(
        self, 
        session: AsyncSession,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Component]:
        """Get components with optimized relationship loading."""
        
        # Use selectinload for one-to-many relationships
        # Use joinedload for many-to-one relationships
        query = select(Component).options(
            joinedload(Component.category),
            joinedload(Component.type),
            selectinload(Component.calculation_results)
        )
        
        # Apply filters efficiently
        if filters:
            if "category_id" in filters:
                query = query.where(Component.category_id == filters["category_id"])
            
            if "voltage_min" in filters:
                query = query.where(
                    Component.specifications["voltage_rating"].astext.cast(Float) >= filters["voltage_min"]
                )
            
            if "voltage_max" in filters:
                query = query.where(
                    Component.specifications["voltage_rating"].astext.cast(Float) <= filters["voltage_max"]
                )
            
            if "search" in filters:
                search_term = f"%{filters['search']}%"
                query = query.where(
                    or_(
                        Component.name.ilike(search_term),
                        Component.description.ilike(search_term),
                        Component.manufacturer.ilike(search_term)
                    )
                )
        
        # Apply pagination
        query = query.limit(limit).offset(offset)
        
        result = await session.execute(query)
        return result.scalars().all()
    
    async def get_component_statistics(
        self, 
        session: AsyncSession,
        category_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get component statistics with optimized aggregation."""
        
        query = select(
            func.count(Component.id).label("total_components"),
            func.avg(Component.specifications["voltage_rating"].astext.cast(Float)).label("avg_voltage"),
            func.avg(Component.specifications["power_rating"].astext.cast(Float)).label("avg_power"),
            func.count(func.distinct(Component.manufacturer)).label("unique_manufacturers")
        )
        
        if category_id:
            query = query.where(Component.category_id == category_id)
        
        result = await session.execute(query)
        row = result.first()
        
        return {
            "total_components": row.total_components,
            "average_voltage": float(row.avg_voltage) if row.avg_voltage else 0,
            "average_power": float(row.avg_power) if row.avg_power else 0,
            "unique_manufacturers": row.unique_manufacturers
        }
```

#### Caching Implementation
```python
# core/cache/cache_manager.py
import redis
import json
import pickle
from typing import Any, Optional, Union
from datetime import timedelta

class CacheManager:
    """Professional caching manager with Redis backend."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.default_ttl = 3600  # 1 hour
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            cached_data = self.redis_client.get(key)
            if cached_data:
                return pickle.loads(cached_data)
        except Exception as e:
            print(f"Cache get error: {e}")
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            ttl = ttl or self.default_ttl
            serialized_data = pickle.dumps(value)
            return self.redis_client.setex(key, ttl, serialized_data)
        except Exception as e:
            print(f"Cache set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            return self.redis_client.delete(key)
        except Exception as e:
            print(f"Cache delete error: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """Clear cache entries matching pattern."""
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
        except Exception as e:
            print(f"Cache clear pattern error: {e}")
        return 0

# Caching decorator
def cache_result(key_prefix: str, ttl: int = 3600):
    """Decorator to cache function results."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            cache_manager = CacheManager()
            
            # Generate cache key
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result
            cache_manager.set(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator
```

### 7.2 Frontend Performance

#### Code Splitting Strategy
```typescript
// utils/code-splitting.ts
import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

// Lazy load calculation components
export const ElectricalCalculator = dynamic(
  () => import('@/components/calculations/ElectricalCalculator'),
  {
    loading: () => <div>Loading calculation engine...</div>,
    ssr: false // Calculation components don't need SSR
  }
);

export const ComponentLibrary = dynamic(
  () => import('@/components/components/ComponentLibrary'),
  {
    loading: () => <div>Loading component library...</div>,
    ssr: true // Component library benefits from SSR
  }
);

export const ReportGenerator = dynamic(
  () => import('@/components/reports/ReportGenerator'),
  {
    loading: () => <div>Loading report generator...</div>,
    ssr: false // Reports are client-side only
  }
);

// Route-based code splitting
export const withDynamicImport = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback: ComponentType = () => <div>Loading...</div>
) => {
  return dynamic(importFunc, {
    loading: fallback,
    ssr: true
  });
};
```

#### Performance Monitoring
```typescript
// hooks/usePerformanceMonitoring.ts
import { useState, useCallback } from 'react';

interface PerformanceMetrics {
  componentRenders: Record<string, number>;
  apiCalls: Record<string, number>;
  lastCalculation?: {
    executionTime: number;
    memoryUsage: number;
    timestamp: string;
  };
}

export const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    componentRenders: {},
    apiCalls: {}
  });

  const measureCalculationPerformance = useCallback(
    async (calculationFn: () => Promise<any>) => {
      const startTime = performance.now();
      const startMemory = (performance as any).memory?.usedJSHeapSize || 0;

      try {
        const result = await calculationFn();

        const endTime = performance.now();
        const endMemory = (performance as any).memory?.usedJSHeapSize || 0;

        const executionTime = endTime - startTime;
        const memoryUsage = endMemory - startMemory;

        setMetrics(prev => ({
          ...prev,
          lastCalculation: {
            executionTime,
            memoryUsage,
            timestamp: new Date().toISOString()
          }
        }));

        // Log performance warnings
        if (executionTime > 2000) {
          console.warn('Slow calculation detected:', {
            executionTime,
            memoryUsage,
            function: calculationFn.name
          });
        }

        return result;
      } catch (error) {
        console.error('Calculation performance measurement failed:', error);
        throw error;
      }
    },
    []
  );

  const measureComponentRender = useCallback((componentName: string) => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      setMetrics(prev => ({
        ...prev,
        componentRenders: {
          ...prev.componentRenders,
          [componentName]: renderTime
        }
      }));

      if (renderTime > 100) {
        console.warn('Slow component render:', {
          componentName,
          renderTime
        });
      }
    };
  }, []);

  return { metrics, measureCalculationPerformance, measureComponentRender };
};
```

---

## 8. Deployment & DevOps

### 8.1 Docker Configuration

#### Production Dockerfile
```dockerfile
# server/Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Copy dependency files
COPY pyproject.toml poetry.lock ./

# Configure Poetry
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev --no-interaction --no-ansi

# Copy application code
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser \
    && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Docker Compose for Development
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  backend:
    build: 
      context: ./server
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/ultimate_electrical_designer
      - REDIS_URL=redis://redis:6379
      - DEBUG=true
    volumes:
      - ./server:/app
      - ./server/logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped

  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
      - NODE_ENV=development
    volumes:
      - ./client:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=ultimate_electrical_designer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 8.2 CI/CD Pipeline

#### GitHub Actions Workflow
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
        echo "$HOME/.local/bin" >> $GITHUB_PATH
    
    - name: Install dependencies
      run: |
        cd server
        poetry install
    
    - name: Run type checking
      run: |
        cd server
        poetry run mypy src/
    
    - name: Run linting
      run: |
        cd server
        poetry run ruff check .
    
    - name: Run tests
      run: |
        cd server
        poetry run pytest --cov=src --cov-report=xml
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./server/coverage.xml
        flags: backend

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: client/package-lock.json
    
    - name: Install dependencies
      run: |
        cd client
        npm ci
    
    - name: Run type checking
      run: |
        cd client
        npm run type-check
    
    - name: Run linting
      run: |
        cd client
        npm run lint
    
    - name: Run tests
      run: |
        cd client
        npm run test:coverage
    
    - name: Run E2E tests
      run: |
        cd client
        npm run test:e2e
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./client/coverage/lcov.info
        flags: frontend

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Run security scan (backend)
      run: |
        cd server
        pip install bandit
        bandit -r src/
    
    - name: Run security scan (frontend)
      run: |
        cd client
        npm audit --audit-level high
    
    - name: Run dependency check
      uses: actions/dependency-check-action@v1
      with:
        project: 'Ultimate Electrical Designer'
        path: '.'
        format: 'ALL'

  deploy:
    needs: [test-backend, test-frontend, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Deploy to production
      run: |
        # Deploy using your preferred method (ECS, Lambda, etc.)
        echo "Deploying to production..."
```

---

## 9. Monitoring & Logging

### 9.1 Application Monitoring

#### Logging Configuration
```python
# core/logging/logger.py
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

class ElectricalEngineeringLogger:
    """Professional logging for electrical engineering application."""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # Create file handler
        file_handler = logging.FileHandler('logs/application.log')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def log_calculation(
        self, 
        calculation_type: str, 
        user_id: str, 
        parameters: Dict[str, Any],
        result: Dict[str, Any],
        execution_time: float
    ):
        """Log electrical calculation with structured data."""
        log_data = {
            "event_type": "calculation",
            "calculation_type": calculation_type,
            "user_id": user_id,
            "parameters": parameters,
            "result": result,
            "execution_time_ms": execution_time,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.info(json.dumps(log_data))
    
    def log_error(
        self, 
        error: Exception, 
        context: Optional[Dict[str, Any]] = None
    ):
        """Log error with context information."""
        log_data = {
            "event_type": "error",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.error(json.dumps(log_data))
    
    def log_security_event(
        self, 
        event_type: str, 
        user_id: str, 
        details: Dict[str, Any]
    ):
        """Log security-related events."""
        log_data = {
            "event_type": "security",
            "security_event": event_type,
            "user_id": user_id,
            "details": details,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.warning(json.dumps(log_data))
```

### 9.2 Health Monitoring

#### Health Check Implementation
```python
# core/monitoring/health_check.py
import asyncio
import psutil
from datetime import datetime
from typing import Dict, Any

from core.database import get_async_session
from core.cache.cache_manager import CacheManager

class HealthChecker:
    """Comprehensive health checking system."""
    
    def __init__(self):
        self.cache_manager = CacheManager()
    
    async def check_system_health(self) -> Dict[str, Any]:
        """Perform comprehensive system health check."""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "checks": {}
        }
        
        # Check database health
        try:
            async with get_async_session() as session:
                await session.execute("SELECT 1")
            health_status["checks"]["database"] = {
                "status": "healthy",
                "response_time_ms": 10
            }
        except Exception as e:
            health_status["checks"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "unhealthy"
        
        # Check Redis health
        try:
            self.cache_manager.redis_client.ping()
            health_status["checks"]["redis"] = {
                "status": "healthy",
                "response_time_ms": 5
            }
        except Exception as e:
            health_status["checks"]["redis"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "unhealthy"
        
        # Check system resources
        memory_usage = psutil.virtual_memory().percent
        cpu_usage = psutil.cpu_percent(interval=1)
        
        health_status["checks"]["system"] = {
            "memory_usage_percent": memory_usage,
            "cpu_usage_percent": cpu_usage,
            "status": "healthy" if memory_usage < 80 and cpu_usage < 80 else "warning"
        }
        
        return health_status
    
    async def check_calculation_engine(self) -> Dict[str, Any]:
        """Check calculation engine health."""
        try:
            from core.calculations.electrical import ElectricalCalculator
            
            calculator = ElectricalCalculator()
            
            # Perform simple calculation test
            start_time = datetime.utcnow()
            result = calculator.calculate_voltage_drop(100, 25, 0.001, 0.0001)
            end_time = datetime.utcnow()
            
            execution_time = (end_time - start_time).total_seconds() * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": execution_time,
                "test_result": result
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
```

---

## 10. Troubleshooting Guide

### 10.1 Common Issues

#### Database Connection Issues
```python
# Fix: Database connection problems
# 1. Check database configuration
# 2. Verify database is running
# 3. Check network connectivity
# 4. Validate credentials

# Debug database connection
async def debug_database_connection():
    try:
        from core.database import get_async_session
        async with get_async_session() as session:
            result = await session.execute("SELECT version()")
            print(f"Database version: {result.scalar()}")
    except Exception as e:
        print(f"Database connection error: {e}")
```

#### Type Checking Issues
```bash
# Fix: MyPy type checking errors
# 1. Ensure all imports have type stubs
# 2. Add type annotations to all functions
# 3. Check for circular imports

# Debug type checking
cd server/
poetry run mypy src/ --verbose
```

#### Performance Issues
```python
# Fix: Slow API responses
# 1. Check database query optimization
# 2. Review caching strategy
# 3. Monitor resource usage

# Debug performance
import time
import psutil

def debug_performance():
    start_time = time.time()
    start_memory = psutil.Process().memory_info().rss
    
    # Your code here
    
    end_time = time.time()
    end_memory = psutil.Process().memory_info().rss
    
    print(f"Execution time: {(end_time - start_time) * 1000:.2f}ms")
    print(f"Memory usage: {(end_memory - start_memory) / 1024 / 1024:.2f}MB")
```

### 10.2 Development Workflow Issues

#### Git Workflow Problems
```bash
# Fix: Merge conflicts
git status
git diff
git add .
git commit -m "Resolve merge conflicts"

# Fix: Pre-commit hook failures
pre-commit run --all-files
git add .
git commit -m "Fix pre-commit issues"
```

#### Testing Issues
```bash
# Fix: Test failures
# 1. Check test database setup
# 2. Verify test data fixtures
# 3. Review test isolation

# Debug test issues
cd server/
poetry run pytest -v --tb=short
poetry run pytest --lf  # Run only last failed tests
```

---

## 11. Best Practices Summary

### 11.1 Code Quality Checklist
- [ ] All functions have complete type annotations
- [ ] Code passes MyPy/TypeScript strict checking
- [ ] Linting passes with 99.9% compliance
- [ ] Tests cover all critical functionality
- [ ] Error handling is comprehensive
- [ ] Security validation is implemented
- [ ] Performance is optimized
- [ ] Documentation is complete

### 11.2 Development Workflow Checklist
- [ ] Feature branch created from main
- [ ] Tests written before implementation
- [ ] Code reviewed by team member
- [ ] All CI/CD checks pass
- [ ] Database migrations applied
- [ ] Performance impact assessed
- [ ] Security implications reviewed
- [ ] Documentation updated

### 11.3 Deployment Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Static files deployed
- [ ] Health checks passing
- [ ] Monitoring configured
- [ ] Backup procedures tested
- [ ] Rollback plan prepared
- [ ] Performance metrics baseline

---

## 12. Conclusion

This development handbook provides comprehensive guidance for building the Ultimate Electrical Designer application with engineering-grade quality. The handbook covers all aspects of development from environment setup to deployment, ensuring that every developer and AI agent can contribute effectively to the project.

The emphasis on professional electrical engineering standards, comprehensive testing, and zero-tolerance quality policies ensures that the final product will meet the demanding requirements of professional electrical engineers while maintaining the highest standards of software development.

---

**Document Control**
- **Version**: 1.0
- **Last Updated**: July 2025
- **Next Review**: Monthly
- **Owner**: Development Team Lead
- **Approval**: Technical Lead, Quality Assurance, Professional Engineer