/**
 * Component Management API Client
 * Provides low-level API functions for component management operations
 */

import type {
  ComponentCreate,
  ComponentUpdate,
  ComponentRead,
  ComponentPaginatedResponse,
  ComponentSearch,
  ComponentAdvancedSearch,
  ComponentAdvancedSearchResponse,
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentValidationResult,
  ComponentStats,
  ComponentSummary,
  ApiResponse,
  RequestOptions,
} from '@/types/api';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_VERSION = '/api/v1';

// Helper function to build API URLs
const buildUrl = (endpoint: string): string => `${API_BASE_URL}${API_VERSION}${endpoint}`;

// Helper function to get auth headers
const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem('auth_token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Generic API request function
async function apiRequest<T>(
  url: string,
  options: RequestInit & RequestOptions = {}
): Promise<ApiResponse<T>> {
  const { headers = {}, timeout = 30000, signal, ...restOptions } = options;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...restOptions,
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders(),
        ...headers,
      },
      signal: signal || controller.signal,
    });

    clearTimeout(timeoutId);

    const data = response.ok ? await response.json() : null;
    const error = !response.ok ? await response.json().catch(() => ({ detail: 'Unknown error' })) : null;

    return {
      data,
      error,
      status: response.status,
    };
  } catch (err) {
    clearTimeout(timeoutId);
    return {
      error: { detail: err instanceof Error ? err.message : 'Network error' },
      status: 0,
    };
  }
}

// Component API functions
export const componentApi = {
  // List components with pagination and filtering
  async list(params: {
    page?: number;
    size?: number;
    search_term?: string;
    category?: string;
    component_type?: string;
    manufacturer?: string;
    is_preferred?: boolean;
    is_active?: boolean;
  } = {}): Promise<ApiResponse<ComponentPaginatedResponse>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    const url = buildUrl(`/components/?${searchParams.toString()}`);
    return apiRequest<ComponentPaginatedResponse>(url);
  },

  // Get component by ID
  async getById(id: number): Promise<ApiResponse<ComponentRead>> {
    const url = buildUrl(`/components/${id}`);
    return apiRequest<ComponentRead>(url);
  },

  // Create new component
  async create(component: ComponentCreate): Promise<ApiResponse<ComponentRead>> {
    const url = buildUrl('/components/');
    return apiRequest<ComponentRead>(url, {
      method: 'POST',
      body: JSON.stringify(component),
    });
  },

  // Update component
  async update(id: number, component: ComponentUpdate): Promise<ApiResponse<ComponentRead>> {
    const url = buildUrl(`/components/${id}`);
    return apiRequest<ComponentRead>(url, {
      method: 'PUT',
      body: JSON.stringify(component),
    });
  },

  // Delete component
  async delete(id: number): Promise<ApiResponse<void>> {
    const url = buildUrl(`/components/${id}`);
    return apiRequest<void>(url, {
      method: 'DELETE',
    });
  },

  // Search components
  async search(
    searchParams: ComponentSearch,
    pagination: { page?: number; size?: number } = {}
  ): Promise<ApiResponse<ComponentPaginatedResponse>> {
    const queryParams = new URLSearchParams();
    if (pagination.page) queryParams.append('page', String(pagination.page));
    if (pagination.size) queryParams.append('size', String(pagination.size));

    const url = buildUrl(`/components/search?${queryParams.toString()}`);
    return apiRequest<ComponentPaginatedResponse>(url, {
      method: 'POST',
      body: JSON.stringify(searchParams),
    });
  },

  // Advanced search
  async advancedSearch(
    searchParams: ComponentAdvancedSearch,
    pagination: { page?: number; size?: number } = {}
  ): Promise<ApiResponse<ComponentAdvancedSearchResponse>> {
    const queryParams = new URLSearchParams();
    if (pagination.page) queryParams.append('page', String(pagination.page));
    if (pagination.size) queryParams.append('size', String(pagination.size));

    const url = buildUrl(`/components/search/advanced?${queryParams.toString()}`);
    return apiRequest<ComponentAdvancedSearchResponse>(url, {
      method: 'POST',
      body: JSON.stringify(searchParams),
    });
  },

  // Search by specifications
  async searchBySpecifications(
    specifications: Record<string, any>,
    pagination: { page?: number; size?: number } = {}
  ): Promise<ApiResponse<ComponentPaginatedResponse>> {
    const queryParams = new URLSearchParams();
    if (pagination.page) queryParams.append('page', String(pagination.page));
    if (pagination.size) queryParams.append('size', String(pagination.size));

    const url = buildUrl(`/components/search/specifications?${queryParams.toString()}`);
    return apiRequest<ComponentPaginatedResponse>(url, {
      method: 'POST',
      body: JSON.stringify(specifications),
    });
  },

  // Get search suggestions
  async getSuggestions(
    query: string,
    field: string = 'name',
    limit: number = 10
  ): Promise<ApiResponse<string[]>> {
    const searchParams = new URLSearchParams({
      query,
      field,
      limit: String(limit),
    });

    const url = buildUrl(`/components/search/suggestions?${searchParams.toString()}`);
    return apiRequest<string[]>(url);
  },

  // Get preferred components
  async getPreferred(params: { skip?: number; limit?: number } = {}): Promise<ApiResponse<ComponentSummary[]>> {
    const searchParams = new URLSearchParams();
    if (params.skip !== undefined) searchParams.append('skip', String(params.skip));
    if (params.limit !== undefined) searchParams.append('limit', String(params.limit));

    const url = buildUrl(`/components/preferred?${searchParams.toString()}`);
    return apiRequest<ComponentSummary[]>(url);
  },

  // Get component statistics
  async getStats(): Promise<ApiResponse<ComponentStats>> {
    const url = buildUrl('/components/stats');
    return apiRequest<ComponentStats>(url);
  },

  // Get component categories
  async getCategories(): Promise<ApiResponse<Array<{ name: string; value: string }>>> {
    const url = buildUrl('/components/categories');
    return apiRequest<Array<{ name: string; value: string }>>(url);
  },

  // Get component types
  async getTypes(category?: string): Promise<ApiResponse<Array<{ name: string; value: string }>>> {
    const searchParams = new URLSearchParams();
    if (category) searchParams.append('category', category);

    const url = buildUrl(`/components/types?${searchParams.toString()}`);
    return apiRequest<Array<{ name: string; value: string }>>(url);
  },

  // Bulk operations
  async bulkCreate(data: ComponentBulkCreate): Promise<ApiResponse<ComponentValidationResult[]>> {
    const url = buildUrl('/components/bulk/create');
    return apiRequest<ComponentValidationResult[]>(url, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  async bulkUpdate(data: ComponentBulkUpdate): Promise<ApiResponse<ComponentValidationResult[]>> {
    const url = buildUrl('/components/bulk/update');
    return apiRequest<ComponentValidationResult[]>(url, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  async bulkDelete(
    componentIds: number[],
    options: { soft_delete?: boolean } = {}
  ): Promise<ApiResponse<{ deleted_count: number; errors: any[] }>> {
    const searchParams = new URLSearchParams();
    if (options.soft_delete !== undefined) {
      searchParams.append('soft_delete', String(options.soft_delete));
    }

    const url = buildUrl(`/components/bulk/delete?${searchParams.toString()}`);
    return apiRequest<{ deleted_count: number; errors: any[] }>(url, {
      method: 'DELETE',
      body: JSON.stringify({ component_ids: componentIds }),
    });
  },
};
