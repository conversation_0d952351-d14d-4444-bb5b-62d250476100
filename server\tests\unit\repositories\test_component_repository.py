#!/usr/bin/env python3
"""Unit tests for Component Repository.

This module contains comprehensive unit tests for the ComponentRepository class,
testing all CRUD operations, search functionality, and component-specific queries.
"""

import json
import pytest
from decimal import Decimal
from unittest.mock import Mock, patch

from sqlalchemy.exc import IntegrityError

from src.core.enums.electrical_enums import ComponentType, ComponentCategoryType
from src.core.models.general.component import Component
from src.core.repositories.general.component_repository import ComponentRepository
from src.core.utils.pagination_utils import PaginationParams


class TestComponentRepository:
    """Test suite for ComponentRepository functionality."""

    @pytest.fixture
    def component_repository(self, db_session):
        """Create a ComponentRepository instance for testing."""
        return ComponentRepository(db_session)

    @pytest.fixture
    def sample_component(self, db_session):
        """Create a sample component for testing."""
        component = Component(
            name="Test Circuit Breaker",
            manufacturer="ABB",
            model_number="S203-B16",
            component_type=ComponentType.CIRCUIT_BREAKER,
            category=ComponentCategoryType.PROTECTION_DEVICES,
            unit_price=Decimal("45.50"),
            currency="EUR",
            supplier="RS Components",
            part_number="123-4567"
        )
        db_session.add(component)
        db_session.commit()
        return component

    @pytest.fixture
    def multiple_components(self, db_session):
        """Create multiple components for testing."""
        components = [
            Component(
                name="Circuit Breaker 1",
                manufacturer="ABB",
                model_number="S203-B16",
                component_type=ComponentType.CIRCUIT_BREAKER,
                category=ComponentCategoryType.PROTECTION_DEVICES,
                unit_price=Decimal("45.50"),
                is_preferred=True
            ),
            Component(
                name="Circuit Breaker 2",
                manufacturer="Schneider Electric",
                model_number="C60N-B20",
                component_type=ComponentType.CIRCUIT_BREAKER,
                category=ComponentCategoryType.PROTECTION_DEVICES,
                unit_price=Decimal("52.00"),
                is_preferred=False
            ),
            Component(
                name="Motor 1",
                manufacturer="Siemens",
                model_number="1LA7090-4AA60",
                component_type=ComponentType.ELECTRIC_MOTOR,
                category=ComponentCategoryType.LOADS,
                unit_price=Decimal("1250.00"),
                is_preferred=True
            ),
            Component(
                name="Contactor 1",
                manufacturer="ABB",
                model_number="A26-30-10",
                component_type=ComponentType.CONTACTOR,
                category=ComponentCategoryType.SWITCHING_AND_CONTROL,
                unit_price=Decimal("85.00"),
                is_preferred=False
            )
        ]
        
        for component in components:
            db_session.add(component)
        db_session.commit()
        return components

    def test_repository_initialization(self, db_session):
        """Test repository initialization."""
        repository = ComponentRepository(db_session)
        assert repository.db_session == db_session
        assert repository.model is Component

    def test_get_by_id_success(self, component_repository, sample_component):
        """Test successful retrieval by ID."""
        result = component_repository.get_by_id(sample_component.id)
        
        assert result is not None
        assert result.id == sample_component.id
        assert result.name == "Test Circuit Breaker"
        assert result.manufacturer == "ABB"

    def test_get_by_id_not_found(self, component_repository):
        """Test retrieval by non-existent ID."""
        result = component_repository.get_by_id(99999)
        assert result is None

    def test_get_by_type(self, component_repository, multiple_components):
        """Test retrieval by component type."""
        circuit_breakers = component_repository.get_by_type(ComponentType.CIRCUIT_BREAKER)
        
        assert len(circuit_breakers) == 2
        for cb in circuit_breakers:
            assert cb.component_type == ComponentType.CIRCUIT_BREAKER

    def test_get_by_category(self, component_repository, multiple_components):
        """Test retrieval by component category."""
        protection_devices = component_repository.get_by_category(ComponentCategoryType.PROTECTION_DEVICES)
        
        assert len(protection_devices) == 2
        for device in protection_devices:
            assert device.category == ComponentCategoryType.PROTECTION_DEVICES

    def test_get_by_manufacturer(self, component_repository, multiple_components):
        """Test retrieval by manufacturer."""
        abb_components = component_repository.get_by_manufacturer("ABB")
        
        assert len(abb_components) == 2
        for component in abb_components:
            assert "ABB" in component.manufacturer

    def test_get_by_part_number(self, component_repository, sample_component):
        """Test retrieval by part number."""
        result = component_repository.get_by_part_number("123-4567")
        
        assert result is not None
        assert result.part_number == "123-4567"
        assert result.id == sample_component.id

    def test_get_by_part_number_not_found(self, component_repository):
        """Test retrieval by non-existent part number."""
        result = component_repository.get_by_part_number("NON-EXISTENT")
        assert result is None

    def test_get_preferred_components(self, component_repository, multiple_components):
        """Test retrieval of preferred components."""
        preferred = component_repository.get_preferred_components()
        
        assert len(preferred) == 2
        for component in preferred:
            assert component.is_preferred is True

    def test_search_components(self, component_repository, multiple_components):
        """Test component search functionality."""
        # Search by name
        results = component_repository.search_components("Circuit Breaker")
        assert len(results) == 2
        
        # Search by manufacturer
        results = component_repository.search_components("Siemens")
        assert len(results) == 1
        assert results[0].manufacturer == "Siemens"
        
        # Search by part number (which is searchable)
        results = component_repository.search_components("Circuit Breaker 1")
        assert len(results) == 1
        assert results[0].name == "Circuit Breaker 1"

    def test_get_components_by_specifications(self, component_repository, db_session):
        """Test retrieval by specifications."""
        # Create component with specifications
        specifications = {
            "electrical": {
                "voltage_rating": "400V",
                "current_rating": "16A"
            }
        }
        
        component = Component(
            name="Spec Component",
            manufacturer="Test Manufacturer",
            model_number="TEST-001",
            component_type=ComponentType.CIRCUIT_BREAKER,
            specifications=json.dumps(specifications)
        )
        db_session.add(component)
        db_session.commit()
        
        # Search by specifications
        search_specs = {"voltage_rating": "400V"}
        results = component_repository.get_components_by_specifications(search_specs)
        
        # Note: This test may need adjustment based on actual JSON query implementation
        # For now, we'll just verify the method doesn't crash
        assert isinstance(results, list)

    def test_get_components_in_price_range(self, component_repository, multiple_components):
        """Test retrieval by price range."""
        # Test with both min and max price
        results = component_repository.get_components_in_price_range(
            min_price=50.0, max_price=100.0, currency="USD"
        )
        
        for component in results:
            assert component.unit_price >= 50.0
            assert component.unit_price <= 100.0
            assert component.currency == "USD"

    def test_count_components_by_category(self, component_repository, multiple_components):
        """Test counting components by category."""
        counts = component_repository.count_components_by_category()
        
        assert isinstance(counts, dict)
        assert counts.get(ComponentCategoryType.PROTECTION_DEVICES) == 2
        assert counts.get(ComponentCategoryType.LOADS) == 1
        assert counts.get(ComponentCategoryType.SWITCHING_AND_CONTROL) == 1

    def test_count_active_components(self, component_repository, multiple_components):
        """Test counting active components."""
        count = component_repository.count_active_components()
        assert count == 4  # All components are active by default

    def test_create_component(self, component_repository):
        """Test component creation."""
        component_data = {
            "name": "New Component",
            "manufacturer": "Test Manufacturer",
            "model_number": "TEST-NEW",
            "component_type": ComponentType.FUSE,
            "category": ComponentCategoryType.PROTECTION_DEVICES,
            "unit_price": Decimal("15.75")
        }
        
        created_component = component_repository.create(component_data)
        
        assert created_component.id is not None
        assert created_component.name == "New Component"
        assert created_component.manufacturer == "Test Manufacturer"
        assert created_component.unit_price == Decimal("15.75")

    def test_update_component(self, component_repository, sample_component):
        """Test component update."""
        update_data = {
            "unit_price": Decimal("55.00"),
            "supplier": "New Supplier"
        }
        
        updated_component = component_repository.update(sample_component.id, update_data)
        
        assert updated_component.unit_price == Decimal("55.00")
        assert updated_component.supplier == "New Supplier"
        assert updated_component.manufacturer == "ABB"  # Unchanged

    def test_update_component_status(self, component_repository, sample_component):
        """Test updating component active status."""
        result = component_repository.update_component_status(sample_component.id, False)
        
        assert result is True
        
        # Verify the update
        updated_component = component_repository.get_by_id(sample_component.id)
        assert updated_component.is_active is False

    def test_update_preferred_status(self, component_repository, sample_component):
        """Test updating component preferred status."""
        result = component_repository.update_preferred_status(sample_component.id, True)
        
        assert result is True
        
        # Verify the update
        updated_component = component_repository.get_by_id(sample_component.id)
        assert updated_component.is_preferred is True

    def test_soft_delete_component(self, component_repository, sample_component):
        """Test soft deleting a component."""
        result = component_repository.soft_delete_component(sample_component.id)
        
        assert result is True
        
        # Verify the component is soft deleted
        # get_by_id should not return soft deleted components
        deleted_component = component_repository.get_by_id(sample_component.id)
        assert deleted_component is None

    def test_restore_component(self, component_repository, sample_component, db_session):
        """Test restoring a soft deleted component."""
        # First soft delete the component
        sample_component.is_deleted = True
        sample_component.is_active = False
        db_session.commit()
        
        # Now restore it
        result = component_repository.restore_component(sample_component.id)
        
        assert result is True
        
        # Verify the component is restored
        restored_component = component_repository.get_by_id(sample_component.id)
        assert restored_component is not None
        assert restored_component.is_deleted is False
        assert restored_component.is_active is True

    def test_get_components_paginated_with_filters(self, component_repository, multiple_components):
        """Test paginated retrieval with filters."""
        pagination_params = PaginationParams(page=1, per_page=2)
        
        # Test with category filter
        result = component_repository.get_components_paginated_with_filters(
            pagination_params=pagination_params,
            category=ComponentCategoryType.PROTECTION_DEVICES
        )
        
        assert result.total == 2
        assert len(result.items) == 2
        assert result.page == 1
        assert result.per_page == 2

    def test_get_components_paginated_with_search(self, component_repository, multiple_components):
        """Test paginated retrieval with search."""
        pagination_params = PaginationParams(page=1, per_page=10)
        
        result = component_repository.get_components_paginated_with_filters(
            pagination_params=pagination_params,
            search_term="ABB"
        )
        
        assert result.total == 2
        assert len(result.items) == 2
        for item in result.items:
            assert "ABB" in item.manufacturer

    def test_get_components_with_multiple_filters(self, component_repository, multiple_components):
        """Test retrieval with multiple filters."""
        pagination_params = PaginationParams(page=1, per_page=10)
        
        result = component_repository.get_components_paginated_with_filters(
            pagination_params=pagination_params,
            category=ComponentCategoryType.PROTECTION_DEVICES,
            manufacturer="ABB",
            is_preferred=True
        )
        
        assert result.total == 1
        assert len(result.items) == 1
        item = result.items[0]
        assert item.category == ComponentCategoryType.PROTECTION_DEVICES
        assert item.manufacturer == "ABB"
        assert item.is_preferred is True

    def test_repository_error_handling(self, component_repository):
        """Test repository error handling."""
        # Test update with non-existent ID
        with pytest.raises(Exception):  # Should raise NotFoundError
            component_repository.update(99999, {"name": "Updated"})
        
        # Test delete with non-existent ID
        with pytest.raises(Exception):  # Should raise NotFoundError
            component_repository.delete(99999)

    def test_pagination_edge_cases(self, component_repository, multiple_components):
        """Test pagination edge cases."""
        # Test page beyond available data
        pagination_params = PaginationParams(page=10, per_page=10)
        
        result = component_repository.get_components_paginated_with_filters(
            pagination_params=pagination_params
        )
        
        assert result.total == 4
        assert len(result.items) == 0
        assert result.page == 10
        assert result.total_pages == 1

    def test_search_case_insensitive(self, component_repository, multiple_components):
        """Test case-insensitive search."""
        # Search with different cases
        results_lower = component_repository.search_components("abb")
        results_upper = component_repository.search_components("ABB")
        results_mixed = component_repository.search_components("Abb")
        
        assert len(results_lower) == len(results_upper) == len(results_mixed) == 2

    def test_get_by_manufacturer_partial_match(self, component_repository, multiple_components):
        """Test manufacturer search with partial matching."""
        results = component_repository.get_by_manufacturer("Schneider")
        
        assert len(results) == 1
        assert "Schneider Electric" in results[0].manufacturer

    def test_repository_with_inactive_components(self, component_repository, db_session):
        """Test repository behavior with inactive components."""
        # Create an inactive component
        inactive_component = Component(
            name="Inactive Component",
            manufacturer="Test Manufacturer",
            model_number="INACTIVE-001",
            component_type=ComponentType.FUSE,
            is_active=False
        )
        db_session.add(inactive_component)
        db_session.commit()
        
        # Verify it's not returned in normal queries
        all_components = component_repository.get_by_type(ComponentType.FUSE)
        assert len(all_components) == 0  # Should not include inactive components

    def test_repository_with_soft_deleted_components(self, component_repository, db_session):
        """Test repository behavior with soft deleted components."""
        # Create a soft deleted component
        deleted_component = Component(
            name="Deleted Component",
            manufacturer="Test Manufacturer",
            model_number="DELETED-001",
            component_type=ComponentType.FUSE,
            is_deleted=True
        )
        db_session.add(deleted_component)
        db_session.commit()
        
        # Verify it's not returned in normal queries
        all_components = component_repository.get_by_type(ComponentType.FUSE)
        assert len(all_components) == 0  # Should not include soft deleted components

    def test_repository_logging(self, component_repository, sample_component):
        """Test that repository methods work correctly (logging is handled by decorators)."""
        # Test that the method works correctly - logging is handled by decorators
        result = component_repository.get_by_id(sample_component.id)
        
        # Verify the method returns the expected result
        assert result is not None
        assert result.id == sample_component.id

    def test_repository_session_health_check(self, component_repository):
        """Test repository session health checking."""
        # This tests the inherited session health functionality
        health_status = component_repository.is_session_healthy()
        assert isinstance(health_status, bool)