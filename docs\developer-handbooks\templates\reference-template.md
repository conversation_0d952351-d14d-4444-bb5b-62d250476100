# Reference Template

**Section:** XX-reference  
**Type:** Quick Reference Guide  
**Last Updated:** July 2025  
**Scope:** [What this reference covers]  

## Quick Reference

### Essential Commands
```bash
# Core commands with descriptions
command-1                    # Primary function
command-2 --option          # With options
command-3 param1 param2     # With parameters
```

### Key Files and Directories
```
project-root/
├── important-dir/           # Description
│   ├── key-file.py         # Purpose and usage
│   └── config-file.yaml    # Configuration details
└── another-dir/            # Description
    └── reference-file.py   # Reference implementation
```

### Configuration Reference

#### Environment Variables
| Variable | Default | Description |
|----------|---------|-------------|
| `VAR_NAME_1` | `default_value` | Purpose and usage |
| `VAR_NAME_2` | `42` | Numeric configuration |
| `VAR_NAME_3` | `true` | Boolean setting |

#### Configuration Files
- **`config/settings.py`** - Main application settings
- **`config/database.py`** - Database configuration
- **`.env`** - Environment-specific variables

### API Reference

#### Core Endpoints
```http
GET    /api/v1/resource           # List resources
POST   /api/v1/resource           # Create resource
GET    /api/v1/resource/{id}      # Get specific resource
PUT    /api/v1/resource/{id}      # Update resource
DELETE /api/v1/resource/{id}      # Delete resource
```

#### Request/Response Examples
```json
// POST /api/v1/resource
{
  "name": "example",
  "type": "standard",
  "parameters": {
    "key": "value"
  }
}

// Response
{
  "id": 1,
  "name": "example",
  "type": "standard",
  "created_at": "2025-07-05T10:00:00Z"
}
```

### Code Patterns Reference

#### Unified Error Handling
```python
# Service layer
@handle_service_errors("operation_name")
def service_method():
    pass

# Repository layer
@handle_repository_errors("entity_name")
def repository_method():
    pass

# API layer
@handle_api_errors("endpoint_name")
def api_endpoint():
    pass

# Calculation layer
@handle_calculation_errors("calculation_type")
def calculation_method():
    pass
```

#### Performance Monitoring
```python
# Service performance monitoring
@monitor_service_performance("operation_name")
def monitored_service():
    pass

# Memory optimization
@memory_optimized(auto_cleanup=True, threshold_mb=5.0)
def memory_optimized_function():
    pass
```

#### Database Patterns
```python
# Repository base pattern
class EntityRepository(BaseRepository[Entity]):
    @handle_repository_errors("entity")
    def create(self, entity_data: dict) -> Entity:
        pass
    
    @handle_repository_errors("entity")
    def get_by_id(self, entity_id: int) -> Entity:
        pass
```

### Testing Reference

#### Test Categories
```bash
# Unit tests
make test-unit

# Integration tests
make test-integration

# API tests
make test-api

# Performance tests
make test-performance

# Security tests
make test-security

# Standards compliance
make test-standards
```

#### Test Markers
```python
# Pytest markers
@pytest.mark.unit           # Unit tests
@pytest.mark.integration    # Integration tests
@pytest.mark.database       # Database tests
@pytest.mark.calculations   # Calculation tests
@pytest.mark.standards      # Standards compliance
@pytest.mark.security       # Security tests
```

### Database Reference

#### Common Queries
```sql
-- Check database status
SELECT name FROM sqlite_master WHERE type='table';

-- View table structure
PRAGMA table_info(table_name);

-- Check constraints
PRAGMA foreign_key_list(table_name);
```

#### Migration Commands
```bash
# Create migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head

# Check migration status
alembic current

# Downgrade migration
alembic downgrade -1
```

### Standards Reference

#### IEEE Standards
- **IEEE-519:** Network quality standards
- **IEEE-80:** Safety in AC substation grounding
- **IEEE-142:** Grounding of industrial and commercial power systems

#### IEC Standards
- **IEC-60079:** Explosive atmospheres
- **IEC-61508:** Functional safety
- **IEC-60364:** Low-voltage electrical installations

#### EN Standards
- **EN-50110:** Operation of electrical installations
- **EN-60204:** Safety of machinery - Electrical equipment

### Error Codes Reference

#### Application Error Codes
| Code | Category | Description |
|------|----------|-------------|
| `APP_001` | Validation | Invalid input data |
| `APP_002` | Database | Database connection error |
| `APP_003` | Calculation | Calculation engine error |
| `APP_004` | Security | Authentication failure |

#### HTTP Status Codes
| Code | Meaning | Usage |
|------|---------|-------|
| `200` | OK | Successful request |
| `201` | Created | Resource created |
| `400` | Bad Request | Invalid request data |
| `401` | Unauthorized | Authentication required |
| `404` | Not Found | Resource not found |
| `500` | Internal Error | Server error |

### Performance Benchmarks

#### Target Metrics
| Operation | Target Time | Memory Limit |
|-----------|-------------|--------------|
| API Response | < 200ms | < 50MB |
| Database Query | < 100ms | < 20MB |
| Calculation | < 500ms | < 100MB |
| Report Generation | < 2s | < 200MB |

### Troubleshooting Quick Reference

#### Common Issues
```bash
# Database connection issues
make check-db-connection

# Performance issues
make analyze-performance

# Memory issues
make monitor-memory

# Security issues
make security-check
```

#### Log Locations
- **Application Logs:** `logs/ultimate_electrical_designer.log`
- **Test Logs:** `test_logs/`
- **Error Logs:** `logs/errors.log`
- **Performance Logs:** `logs/performance.log`

---

**Navigation:**  
← [Previous Section](../XX-previous.md) | [Handbook Home](001-cover.md) | [Next Section](../XX-next.md) →
