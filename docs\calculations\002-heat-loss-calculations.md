# **Heat Loss in Electrical Heat Tracing Systems (Universal Units)**

Electrical heat tracing systems are critical for maintaining process temperatures in pipes and tanks, preventing freezing, and ensuring viscosity control. Accurate calculation of heat loss is fundamental to designing an efficient, safe, and reliable heat tracing system. This document outlines the fundamental principles of heat transfer and provides a structured approach to calculating heat loss for pipes and tanks, adhering to industry best practices and standards.

## **Fundamentals of Heat Loss**

Heat loss from pipes and tanks is a complex phenomenon involving a combination of three fundamental modes of heat transfer: conduction, convection, and radiation. Understanding these mechanisms is essential for effective heat tracing design.

### **Conductive Heat Transfer**

**Conduction** is the transfer of heat energy through direct physical contact, typically in solid materials. In the context of pipes and tanks, heat is conducted from the hot fluid inside, through the tank wall, and then through any layers of insulation. This occurs as molecules with higher kinetic energy vibrate and transfer energy to adjacent, less energetic molecules.

Conduction is the primary mode of heat transfer within the pipe/tank material and its insulation. The purpose of insulation is to create a high thermal resistance, significantly slowing the rate at which heat is conducted from the hot surface to the cooler outer surface of the insulation. The thickness and thermal conductivity (k-value) of the materials are key factors in determining conductive heat loss.

* **Common Units for Thermal Conductivity (**k**):**
  * **SI:** Watts per meter Kelvin (W/(m·K)) or Watts per meter degree Celsius (W/(m·∘C))
  * **Imperial:** British thermal units per hour foot degree Fahrenheit (Btu/(hr·ft·∘F)) or British thermal units per hour square foot degree Fahrenheit per inch (Btu·in/(hr·ft$^2$·∘F))

### **Convective Heat Transfer**

**Convection** is the transfer of heat through the movement of fluids (liquids or gases). It involves two primary processes:

1. **Conduction to Fluid:** Heat is first transferred by conduction from the pipe's outer surface to the layer of fluid (e.g., air) directly in contact with it.
2. **Advection (Bulk Motion):** The heated fluid becomes less dense and rises (in natural convection) or is moved by external forces like wind or fans (in forced convection), carrying the heat away. Cooler fluid then moves in to replace it, gets heated, and the cycle continues.

Convection is a significant factor in heat loss from the outer surface of an insulated pipe or tank to the surrounding air.

* **Natural Convection:** Even in still air, the air immediately surrounding a warm surface heats up, becomes less dense, and rises, drawing cooler air towards the surface.
* **Forced Convection:** If there is wind or air currents around the pipe, the forced movement of air significantly increases the rate of heat transfer away from the surface, leading to higher heat loss. This is why heat loss calculations for outdoor installations often account for wind speed.

Convective heat transfer is often described by Newton's Law of Cooling, which states that the rate of heat loss is proportional to the temperature difference between the surface and the fluid, and a convective heat transfer coefficient (h).

### **Radiative Heat Transfer**

**Radiation** is the transfer of heat through electromagnetic waves, primarily in the infrared spectrum. Unlike conduction and convection, radiation does not require a medium for heat transfer and can occur through a vacuum. All objects with a temperature above absolute zero emit thermal radiation.

Radiative heat transfer occurs between the outer surface of the pipe's insulation and the surrounding surfaces or environment (e.g., ground, sky, adjacent structures). The amount of heat radiated depends on the absolute temperature of the surface and its emissivity (ϵ), which is a measure of how effectively a surface emits thermal radiation.

* A hot surface will radiate heat to cooler surroundings.
* The surface properties of the insulation, particularly its color and texture (which influence emissivity), can significantly affect the rate of radiative heat loss. Dark, dull surfaces tend to radiate more heat than light, shiny surfaces.

The Stefan-Boltzmann Law describes the rate of radiative heat transfer, stating it is proportional to the fourth power of the absolute temperature of the radiating surface.

### **Interrelation of Heat Transfer Modes in Overall Heat Loss**

For an insulated pipe or tank, the overall heat loss is a combination of all three mechanisms in series and parallel:

1. **Conduction through the Pipe/Tank Wall and Insulation:** Heat first travels from the hot fluid inside to the inner surface of the pipe/tank wall, then through the wall, and finally through the insulation layers by conduction.
2. **Convection from the Insulation Surface to the Ambient Air:** Once the heat reaches the outer surface of the insulation, it is transferred to the surrounding air by convection (natural or forced).
3. **Radiation from the Insulation Surface to the Surroundings:** Simultaneously, the outer surface of the insulation radiates heat to the surrounding environment.

Therefore, the total heat loss from a pipe or tank is the sum of the heat transferred by convection and radiation from the outer surface of the insulation, which is ultimately supplied by conduction through the pipe/tank and insulation layers from the hot fluid inside. Engineers use these principles to design insulation systems that minimize overall heat loss and maintain desired process temperatures with minimal energy input from the heat tracing system.

## **The Effect of Wind on Heat Loss**

Ambient conditions play a crucial role in determining heat loss. Wind is a particularly potent factor, as it drastically increases the rate of heat removal from an exposed surface through forced convection. This significantly elevates energy loss compared to still air conditions, necessitating higher heat tracing power. Wind speed can be expressed in meters per second (m/s) or miles per hour (mph).

## **IEC Guided Design and Calculation Process**

Standards like IEC 60079-30 (Electrical Resistance Trace Heating \- General and Testing Requirements) and IEC 62395 (Electrical Resistance Trace Heating Systems for Industrial Applications) provide essential guidance for the safe and efficient design of heat tracing systems. Following a structured process, as guided by these standards, ensures all relevant variables are considered for a safe, efficient, and reliable "stabilized design."

1. **Define Operating Conditions:** Identify the required process maintain temperature (TM​), minimum ambient temperature (TA​), and maximum exposure temperatures. These temperatures can be expressed in Celsius (∘C) or Fahrenheit (∘F), or Kelvin (K).
2. **Gather System Geometry:** Document pipe diameters, tank dimensions (height, diameter), and total lengths. These dimensions can be in meters (m) or feet (ft).
3. **Select Insulation System:** Choose insulation material (based on its thermal conductivity, k-value, and temperature rating) and specify the desired thickness. Thickness can be in millimeters (mm) or inches (in).
4. **Calculate Thermal Resistances (**Rth​**):** Determine the thermal resistance of each layer: pipe/tank wall, insulation, and the combined thermal resistance of the outer surface (including convection and radiation).
5. Calculate Total Heat Loss (Q): Use the appropriate formulas to find the heat loss in Watts per meter (W/m) for pipes or total Watts (W) for tanks. This typically involves the overall heat transfer coefficient (U) or total thermal resistance (Rtotal​).
   Q=Rtotal​Tprocess​−Tambient​​

   (Where Rtotal​ is the sum of all series thermal resistances, with units like K/W or ∘C/W or ∘F/Btu/hr)
6. **Design & Safety Verification:** Specify the heat tracing system power output to match the calculated heat loss (Q). Verify that the maximum sheath temperatures of the heat tracing elements are within system and material limits, especially for hazardous area applications (e.g., per IEC 60079-30).

## **General Heat Loss Equations**

While specific equations for pipes and tanks are detailed below, here are some general concepts for total energy requirements and heat loss from surfaces. For electrical heat tracing designed to *maintain* temperature, the primary focus is on compensating for heat loss, not necessarily adding heat to the process fluid or product (which falls under process heating).

### **Total Heat Energy Requirements (General Process Heating)**

The total heat energy (QT​) required for a general heating application is the sum of energy absorbed by the work product, energy lost from surfaces, and a safety factor. For **maintain applications** with heat tracing, QM​ (energy absorbed by the work product) is typically zero, as the goal is to only compensate for QL​.

QT​=QM​+QL​+Safety Factor
Where:

* QT​ \= Total energy required (W or kW)
* QM​ \= Total energy absorbed by the work product (including latent heat, make-up materials, containers, and equipment) (W or kW)
* QL​ \= Total energy lost from surfaces by conduction, convection, radiation, ventilation, and evaporation (W or kW)
* **Safety Factor** \= Typically 10% to 25% of (QM​+QL​), added to account for unforeseen circumstances, aging insulation, or colder-than-design conditions.

### **Heat Energy Lost from Surfaces (General)**

For a flat surface, the heat energy lost from surfaces by radiation, convection, and evaporation can be estimated from the surface area and a combined loss rate.

In SI Units:

QLS​=A×LS

Where: QLS​ in Watts (W), A in square meters (m2), LS in Watts per square meter (W/m2).
In Imperial Units:

QLS​=A×LS

Where: QLS​ in Watts (W), A in square feet (ft2), LS in Watts per square foot (W/ft2).

### **Heat Energy Lost by Conduction Through Flat Materials or Insulation**

The heat energy lost by conduction through a flat material is determined by the surface area, the thermal conductivity of the material, its thickness, and the temperature difference across the material. This is useful for tank walls or flat sections of insulation.

In SI Units:

QLC​=dA×k×ΔT​

Where: QLC​ in Watts (W), A in m2, k in W/(m·K) or W/(m·∘C), ΔT in K or ∘C, d in meters (m).
In Imperial Units (using Btu·in/(hr·ft$^2$·∘F) for k, Watts for QLC):

QLC​=d×3.412A×k×ΔT​

Where: QLC​ in Watts (W), A in ft2, k in Btu·in/(hr·ft$^2$·∘F), ΔT in ∘F, d in inches (in).
(Note: 3.412 converts Btu/hr to Watts)

## **Pipe Heat Loss Calculations**

### **Conductive Heat Loss Through a Single-Layer Cylinder or Pipe**

For an uninsulated pipe, heat conduction occurs through the pipe wall. The heat transfer rate can be expressed as:

In SI Units:

Q=kln(ro​/ri​)​2πL(Ti​−To​)​

Where: Q in Watts (W), L in meters (m), Ti​,To​ in ∘C or K, ro​,ri​ in meters (m), k in W/(m·K) or W/(m·∘C).
In Imperial Units (using Btu/(hr·ft·∘F) for k, Watts for Q):

Q=(kln(ro​/ri​)​)×3.4122πL(Ti​−To​)​

Where: Q in Watts (W), L in feet (ft), Ti​,To​ in ∘F, ro​,ri​ in feet (ft), k in Btu/(hr·ft·∘F).

### **Conductive Heat Loss Through an Insulated Cylinder or Pipe**

For an insulated pipe, heat conducts through the pipe wall and then through the insulation layer. The overall conductive heat loss to the *outer surface of the insulation* can be expressed as:

In SI Units:

Q=(kpipe​ln(ro​/ri​)​)+(kins​ln(rs​/ro​)​)2πL(Ti​−Ts​)​

Where: Q in Watts (W), L in meters (m), Ti​,Ts​ in ∘C or K, ro​,ri​,rs​ in meters (m), kpipe​,kins​ in W/(m·K) or W/(m·∘C).
In Imperial Units (using Btu/(hr·ft·∘F) for k, Watts for Q):

Q=(kpipe​ln(ro​/ri​)​)+(kins​ln(rs​/ro​)​)×3.4122πL(Ti​−Ts​)​

Where: Q in Watts (W), L in feet (ft), Ti​,Ts​ in ∘F, ro​,ri​,rs​ in feet (ft), kpipe​,kins​ in Btu/(hr·ft·∘F).
**Note:** For total heat loss to ambient air (TA​), the heat transfer from the insulation surface (Ts​) to the ambient air (TA​) via convection and radiation must also be accounted for, typically through an overall heat transfer coefficient (U) or by calculating the combined convective and radiative losses from the insulation surface.

## **Tank Heat Loss Calculations**

For storage tanks, heat escapes from every surface. The largest surface, the shell, is typically the primary source of loss, but uninsulated roofs, bases, and accessories can contribute significantly to overall inefficiency.

**Important Note on Units:** The following tables (Table 5 to Table 16\) provide values primarily in **Imperial Units (feet, square feet, Fahrenheit)** for dimensions and temperature differences, and the resulting heat losses are in **Watts (W)**, which is an SI unit. When using these tables, ensure your input dimensions and temperature differences are converted to the specified Imperial units if you are starting with Metric data.

To calculate tank heat loss, follow these steps:

### **Step 1: Gather Necessary Information**

* **Maintain Temperature (**TM​**):** Desired temperature inside the tank (∘C or ∘F).
* **Minimum Ambient Temperature (**TA​**):** Lowest expected temperature outside the tank (∘C or ∘F).
* **Tank Dimensions:** Diameter (m or ft), height/length (m or ft), and surface area (m2 or ft2).
* **Tank Material:** Type of material used for the tank walls.
* **Insulation Type and Thickness:** Specifications of the insulation applied to the tank (thermal conductivity, thickness in mm or in).

### **Step 2: Calculate Surface Area (A)**

* **Vertical Cylindrical Tanks:**
  * In Imperial Units:
    Atotal​=Abody​+Atop​+Abottom​=(π×D×H)+(2×4π×D2​)

    (Where D is diameter in ft, H is height in ft, Atotal​ in ft2)
  * In SI Units:
    Atotal​=(π×D×H)+(2×4π×D2​)

    (Where D is diameter in m, H is height in m, Atotal​ in m2)
* **Horizontal Cylindrical Tanks:**
  * In Imperial Units:
    Atotal​=Abody​+Aends​=(π×D×L)+(2×4π×D2​)

    (Where D is diameter in ft, L is length in ft, Atotal​ in ft2)
  * In SI Units:
    Atotal​=(π×D×L)+(2×4π×D2​)

    (Where D is diameter in m, L is length in m, Atotal​ in m2)
* **Truncated Cone Tanks:** Surface area \= Body area \+ Top area \+ Bottom area (if not resting on a slab). Specific formulas apply.

Refer to **Table 5: Cylindrical Tank Surface Areas** (in ft$^2$) for common Imperial dimensions. Note: For horizontal tanks, add the area of both ends.

### **Step 3: Calculate Temperature Differential (ΔT)**

ΔT=TM​−TA​

(Can be calculated in ∘C or ∘F; the numerical value of ΔT in ∘C is equal to ΔT in Kelvin, and the numerical value of ΔT in ∘F is equal to ΔT in Rankine. Tables use ∘F.)

### **Step 4: Calculate Heat Loss Through Tank Body (QV​)**

1. **Determine Heat Loss Rate (**qv**):** Use tables based on ΔT (in ∘F), insulation thickness (in inches), and material.
2. **Adjust for Insulation Type:** Apply insulation adjustment factors from **Table 7**.
3. **Calculate** QV​**:** Multiply surface area (in ft$^2$) by heat loss rate (W/ft$^2$) and the insulation adjustment factor.

QV​=Abody​×qv​×Finsulation​

Where:

* QV​ \= Heat loss through vertical/body surface (W)
* Abody​ \= Surface area of the tank body (ft2)
* qv​ \= Heat loss rate per square foot (W/ft2) from **Table 6**
* Finsulation​ \= Insulation adjustment factor from **Table 7**

**Table 5: Cylindrical Tank Surface Areas (ft$^2$)**

| H (ft) | D (ft) | 6 (1.8m) | 7 (2.1m) | 8 (2.4m) | 9 (2.7m) | 10 (3.0m) | 11 (3.4m) | 12 (3.7m) | 13 (4.0m) | 14 (4.3m) | 15 (4.6m) | 16 (4.9m) | 17 (5.2m) | 18 (5.5m) | 19 (5.8m) | 20 (6.1m) | 21 (6.4m) | 22 (6.7m) | 23 (7.0m) | 24 (7.3m) | 25 (7.6m) |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| Aend (ft$^2$) | *(Diameter)* | 29 | 39 | 51 | 64 | 79 | 95 | 114 | 133 | 154 | 177 | 202 | 227 | 255 | 284 | 315 | 346 | 381 | 415 | 450 | 484 |
| 6 | Abody (ft$^2$) | 113 | 132 | 151 | 170 | 189 | 208 | 227 | 245 | 264 | 283 | 302 | 321 | 340 | 359 | 311 | 396 | 415 | 434 | 453 | 471 |
| 7 | Abody (ft$^2$) | 132 | 154 | 176 | 198 | 220 | 242 | 264 | 286 | 308 | 330 | 352 | 374 | 396 | 418 | 440 | 462 | 484 | 506 | 528 | 550 |
| 8 | Abody (ft$^2$) | 151 | 176 | 202 | 227 | 252 | 277 | 302 | 327 | 352 | 377 | 403 | 427 | 452 | 478 | 503 | 528 | 553 | 579 | 604 | 629 |
| 9 | Abody (ft$^2$) | 170 | 198 | 227 | 255 | 283 | 311 | 340 | 368 | 396 | 425 | 453 | 481 | 509 | 538 | 566 | 594 | 622 | 650 | 679 | 707 |
| 10 | Abody (ft$^2$) | 189 | 220 | 252 | 283 | 315 | 346 | 377 | 409 | 440 | 472 | 503 | 535 | 565 | 597 | 629 | 660 | 692 | 723 | 754 | 786 |
| 11 | Abody (ft$^2$) | 208 | 242 | 277 | 311 | 346 | 381 | 415 | 450 | 484 | 519 | 553 | 588 | 622 | 657 | 692 | 726 | 761 | 795 | 830 | 864 |
| 12 | Abody (ft$^2$) | 227 | 264 | 302 | 340 | 377 | 415 | 453 | 491 | 528 | 566 | 604 | 641 | 679 | 717 | 754 | 792 | 830 | 868 | 905 | 943 |
| 13 | Abody (ft$^2$) | 245 | 286 | 327 | 368 | 409 | 450 | 491 | 531 | 572 | 613 | 654 | 695 | 736 | 776 | 817 | 858 | 899 | 940 | 981 | 1021 |
| 14 | Abody (ft$^2$) | 264 | 308 | 352 | 396 | 440 | 484 | 528 | 572 | 616 | 660 | 704 | 748 | 792 | 836 | 880 | 924 | 968 | 1012 | 1055 | 1100 |
| 15 | Abody (ft$^2$) | 283 | 330 | 377 | 425 | 472 | 519 | 566 | 613 | 660 | 707 | 754 | 802 | 849 | 896 | 943 | 990 | 1037 | 1084 | 1131 | 1179 |
| 16 | Abody (ft$^2$) | 302 | 352 | 403 | 453 | 503 | 553 | 604 | 654 | 704 | 754 | 805 | 855 | 905 | 955 | 1006 | 1056 | 1106 | 1157 | 1207 | 1257 |
| 17 | Abody (ft$^2$) | 321 | 374 | 427 | 481 | 535 | 588 | 641 | 695 | 748 | 802 | 855 | 908 | 962 | 1015 | 1069 | 1121 | 1175 | 1229 | 1282 | 1336 |
| 18 | Abody (ft$^2$) | 340 | 396 | 452 | 509 | 565 | 622 | 679 | 736 | 792 | 849 | 905 | 962 | 1018 | 1075 | 1131 | 1188 | 1244 | 1301 | 1357 | 1414 |
| 19 | Abody (ft$^2$) | 359 | 418 | 478 | 538 | 597 | 657 | 717 | 776 | 836 | 896 | 955 | 1015 | 1075 | 1135 | 1194 | 1254 | 1314 | 1373 | 1433 | 1493 |
| 20 | Abody (ft$^2$) | 311 | 440 | 503 | 566 | 629 | 692 | 754 | 817 | 880 | 943 | 1006 | 1069 | 1131 | 1194 | 1257 | 1320 | 1383 | 1446 | 1508 | 1571 |
| 21 | Abody (ft$^2$) | 396 | 462 | 528 | 594 | 660 | 726 | 792 | 864 | 924 | 990 | 1056 | 1121 | 1188 | 1254 | 1320 | 1383 | 1452 | 1508 | 1571 | 1633 |
| 22 | Abody (ft$^2$) | 415 | 484 | 553 | 622 | 692 | 754 | 830 | 896 | 968 | 1037 | 1106 | 1175 | 1244 | 1314 | 1383 | 1452 | 1521 | 1590 | 1658 | 1727 |
| 23 | Abody (ft$^2$) | 434 | 506 | 579 | 650 | 723 | 795 | 864 | 940 | 1012 | 1084 | 1157 | 1229 | 1301 | 1373 | 1446 | 1515 | 1590 | 1664 | 1738 | 1812 |
| 24 | Abody (ft$^2$) | 453 | 528 | 604 | 679 | 754 | 830 | 905 | 981 | 1055 | 1131 | 1207 | 1282 | 1357 | 1433 | 1508 | 1584 | 1664 | 1740 | 1812 | 1887 |
| 25 | Abody (ft$^2$) | 471 | 550 | 629 | 707 | 786 | 864 | 943 | 1021 | 1100 | 1179 | 1257 | 1336 | 1414 | 1493 | 1571 | 1649 | 1727 | 1805 | 1887 | 1965 |

**Table 6: Heat Loss Rate (**qv​**) Per Square Foot (Watts/ft$^2$)**

| Insulation thickness | ΔT ∘F (∘C) | 50 (10) | 100 (38) | 150 (66) | 200 (93) | 250 (121) | 300 (149) |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| 1" (25 mm) | 3.4 | 7.1 | 11.0 | 15.3 | 20.0 | 24.9 |  |
| 1.5" (38 mm) | 2.3 | 4.8 | 7.5 | 10.3 | 13.5 | 16.8 |  |
| 2" (51 mm) | 1.7 | 3.6 | 5.6 | 7.7 | 10.2 | 12.7 |  |
| 3" (76 mm) | 1.2 | 2.4 | 3.7 | 5.2 | 6.8 | 8.5 |  |
| 4" (102 mm) | 0.9 | 1.8 | 2.8 | 3.9 | 5.1 | 6.5 |  |

**Table 7: Insulation Adjustment Factors for Typical Insulations**

| Insulation types | Insulation Adjustment Factor | k factor\* (Btu·in/(hr·ft$^2$·∘F)) | k factor\* (W/(m·K)) |
| :---- | :---- | :---- | :---- |
| Fiberglass | 1.00 | 0.219 | 0.0315 |
| Cellular glass | 1.36 | 0.298 | 0.043 |
| Calcium silicate (Type 1\) | 1.76 | 0.386 | 0.0556 |
| Expanded perlite | 2.13 | 0.466 | 0.067 |
| Flexible elastomer | 1.25 | 0.273 | 0.039 |
| Mineral fiber blanket | 1.64 | 0.360 | 0.052 |
| Polyisocyanurate | 0.87 | 0.19 | 0.027 |
| Rigid polyurethane, W | 0.87 | 0.19 | 0.027 |
| Rigid polyurethane, spray | 0.73 | 0.16 | 0.023 |
| Rock wool/mineral wool | 1.00 | 0.219 | 0.0315 |

\*Based on a 50$^\\circF(10^\\circC)meantemperature.Conversion:1Btu⋅in/(hr⋅ft^2$·∘F) ≈ 0.1442 W/(m·K)

### **Step 5: Calculate Heat Loss Through Tank Supports (QS​)**

Identify the type of support (e.g., concrete slab, legs, saddles, uninsulated skirt) and use the corresponding tables.

QS​=Heat loss per support (from table)×Number of supports
**Table 11: Heat Loss (W) for a Concrete Slab or Earth Foundation**

| ΔT ∘F (∘C) | Tank diameter ft (m) | 5 (1.5) | 10 (3) | 20 (6) | 30 (9) | 40 (12) | 50 (15) |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| 50 (10) | 137 | 283 | 566 | 848 | 1131 | 1374 |  |
| 100 (38) | 278 | 573 | 1154 | 1760 | 2325 | 2945 |  |
| 150 (66) | 451 | 925 | 1856 | 2826 | 3740 | 4649 |  |
| 200 (93) | 566 | 1163 | 2325 | 3535 | 4649 | 5891 |  |
| 250 (121) | 711 | 1452 | 2922 | 4383 | 5906 | 7265 |  |
| 300 (149) | 857 | 1703 | 3488 | 5231 | 7037 | 8836 |  |

**Table 12: Heat Loss (W) for a Leg Support**

| ΔT ∘F (∘C) | Tank diameter ft (m) | 5 (1.5) | 10 (3) and above |
| :---- | :---- | :---- | :---- |
| 50 (10) | 26 | 85 |  |
| 100 (38) | 52 | 169 |  |
| 150 (66) | 77 | 251 |  |
| 200 (93) | 103 | 336 |  |
| 250 (121) | 129 | 420 |  |
| 300 (149) | 155 | 505 |  |

**Table 13: Heat Loss (W) for a Concrete Saddle**

| ΔT ∘F (∘C) | Tank diameter ft (m) | 5 (1.5) | 10 (3) | 15 (4.6) | 20 (6) |
| :---- | :---- | :---- | :---- | :---- | :---- |
| 50 (10) | 93 | 145 | 198 | 250 |  |
| 100 (38) | 186 | 290 | 395 | 500 |  |
| 150 (66) | 275 | 430 | 586 | 741 |  |
| 200 (93) | 368 | 576 | 783 | 991 |  |
| 250 (121) | 461 | 721 | 981 | 1241 |  |
| 300 (149) | 553 | 866 | 1179 | 1491 |  |

**Table 14: Heat Loss (W) for an Uninsulated Skirt**

| ΔT ∘F (∘C) | Tank diameter ft (m) | 5 (1.5) | 10 (3) | 15 (4.6) | 20 (6) |
| :---- | :---- | :---- | :---- | :---- | :---- |
| 50 (10) | 402 | 806 | 1193 | 1613 |  |
| 100 (38) | 805 | 1612 | 2389 | 3225 |  |
| 150 (66) | 1193 | 2400 | 3585 | 4780 |  |
| 200 (93) | 1612 | 3195 | 4794 | 6393 |  |
| 250 (121) | 1998 | 4000 | 6003 | 8006 |  |
| 300 (149) | 2400 | 4806 | 7212 | 9619 |  |

### **Step 6: Calculate Heat Loss Through Accessories (QA​)**

Accessories include manholes, handholes, nozzles, and instrumentation connections. These are often treated as discrete heat loss points.

QA​=∑(Heat loss per accessory×Number of accessories)
**Table 15: Heat Loss (W) for a Manhole**

| ΔT ∘F (∘C) | Heat loss (W) |
| :---- | :---- |
| 50 (10) | 564 |
| 100 (38) | 1120 |
| 150 (66) | 1680 |
| 200 (93) | 2237 |
| 250 (121) | 2807 |
| 300 (149) | 3401 |

**Table 16: Heat Loss (W) for a Handhole**

| ΔT ∘F (∘C) | Heat loss (W) |
| :---- | :---- |
| 50 (10) | 90 |
| 100 (38) | 178 |
| 150 (66) | 265 |
| 200 (93) | 351 |
| 250 (121) | 437 |
| 300 (149) | 526 |

### **Step 7: Calculate Total Heat Loss (QT​)**

The total heat loss combines the contributions from the body, supports, and accessories, adjusted for ambient conditions.

* Outdoor application:
  QT​=Fwind​×(QV​+QS​+QA​)

  Where Fwind​ \= Wind Factor. This factor accounts for increased convective heat transfer due to wind. Table 3: Wind Factors (not provided here, but would be an external reference or a table within the full document) is typically based on a reference wind velocity (e.g., 20 mph or 32 km/h). Adjust Fwind​ as necessary: add 5% for each 5 mph (approx. 8 km/h) over the reference wind speed, up to a maximum increase of 15% regardless of actual wind speed.
* Indoor application:
  QT​=0.9×(QV​+QS​+QA​)

  The factor of 0.9 is commonly used to approximate the lower heat loss due to reduced air movement and radiative heat exchange compared to outdoor environments, effectively compensating for the "base" wind factor implicit in some outdoor tables.

### **Step 8: Finalize Design Heat Loss (QF​)**

Always apply a safety factor to the calculated total heat loss to ensure robust and reliable heat tracing system performance. A typical safety factor is 20%.

QF​=QT​×1.20

## **Example Calculation: Vertical Cylindrical Tank (Imperial Units)**

Let's calculate the design heat loss for a vertical cylindrical tank using Imperial units as provided in the tables.

**Given Information:**

* Tank Dimensions: Diameter (D) \= 10 ft (3.05 m), Height (H) \= 20 ft (6.10 m).
* Maintain Temperature (TM​): 180$^\\circF(82.2^\\circ$C)
* Minimum Ambient Temperature (TA​): 100$^\\circF(37.8^\\circ$C)
* Insulation: 2-inch (51 mm) thick Fiberglass (from Table 7, Finsulation​=1.00)
* Supports: Concrete slab (from Table 11\)
* Accessories: 1 Manhole, 2 Handholes
* Application: Outdoor, assuming base wind conditions for the tables (no additional wind speed adjustment for this example).

Step 1: Calculate Temperature Differential (ΔT)

ΔT=TM​−TA​=180∘F−100∘F=80∘F(26.7∘C)
**Step 2: Calculate Surface Area**

* From Table 5 for D=10 ft, H=20 ft:
  * Abody​=628 ft2 (or calculate π×10 ft×20 ft=628.3 ft2)
  * Atop/bottom​ (Area of one end for D=10 ft) \= 79 ft2 (from Table 5\)

**Step 3: Calculate Heat Loss Through Tank Body (**QV​**)**

* From Table 6 for 2" insulation and ΔT=80∘F:
  * Interpolate qv​: For ΔT=50∘F, qv​=1.7 W/ft2. For ΔT=100∘F, qv​=3.6 W/ft2.
  * qv​(80∘F)=1.7+(3.6−1.7)×(100−50)(80−50)​=1.7+1.9×0.6=1.7+1.14=2.84 W/ft2
* Insulation Adjustment Factor (Finsulation​) for Fiberglass (Table 7): 1.00
* QV​=Abody​×qv​×Finsulation​=628.3 ft2×2.84 W/ft2×1.00=1783.5 W

**Step 4: Calculate Heat Loss Through Tank Supports (**QS​**)**

* Concrete Slab (Table 11), for D=10 ft, ΔT=80∘F:
  * Interpolate: For ΔT=50∘F, Qslab​=283 W. For ΔT=100∘F, Qslab​=573 W.
  * QS​=283+(573−283)×(100−50)(80−50)​=283+290×0.6=283+174=457 W

**Step 5: Calculate Heat Loss Through Accessories (**QA​**)**

* Manhole (Table 15), for ΔT=80∘F:
  * Interpolate: For ΔT=50∘F, QMH​=564 W. For ΔT=100∘F, QMH​=1120 W.
  * QMH​=564+(1120−564)×(100−50)(80−50)​=564+556×0.6=564+333.6=897.6 W
* Handhole (Table 16), for ΔT=80∘F:
  * Interpolate: For ΔT=50∘F, QHH​=90 W. For ΔT=100∘F, QHH​=178 W.
  * QHH​=90+(178−90)×(100−50)(80−50)​=90+88×0.6=90+52.8=142.8 W
* Total QA​=(1×QMH​)+(2×QHH​)=(1×897.6 W)+(2×142.8 W)=897.6+285.6=1183.2 W

**Step 6: Calculate Total Heat Loss (**QT​**)**

* Outdoor application, assume Fwind​=1.0 (base wind factor for tables)
  QT​=Fwind​×(QV​+QS​+QA​)=1.0×(1783.5 W+457 W+1183.2 W)=3423.7 W

**Step 7: Finalize Design Heat Loss (**QF​**)**

* Apply 20% safety factor:
  QF​=QT​×1.20=3423.7 W×1.20=4108.44 W

Therefore, the estimated design heat loss for this tank is approximately **4108.4 Watts** (or 4.11 kW). This is the power requirement that the electrical heat tracing system would need to supply to maintain the tank's temperature under the specified conditions.
