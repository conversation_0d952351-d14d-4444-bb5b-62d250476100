# Advanced Component Management API Documentation

This document provides comprehensive documentation for the advanced component management features including advanced search, bulk operations, and performance optimization.

## Table of Contents

1. [Advanced Search Endpoints](#advanced-search-endpoints)
2. [Enhanced Bulk Operations](#enhanced-bulk-operations)
3. [Performance Optimization](#performance-optimization)
4. [Authentication & Authorization](#authentication--authorization)
5. [Error Handling](#error-handling)
6. [Rate Limiting](#rate-limiting)

## Advanced Search Endpoints

### POST /api/v1/components/search/advanced

Perform advanced component search with complex filtering and relevance scoring.

#### Request Body

```json
{
  "search_term": "circuit breaker",
  "search_fields": ["name", "description", "manufacturer", "part_number"],
  "fuzzy_search": false,
  "basic_filters": [
    {
      "field": "manufacturer",
      "operator": "eq",
      "value": "Schneider Electric",
      "logical_operator": "and"
    }
  ],
  "specification_filters": [
    {
      "path": "electrical.voltage_rating",
      "operator": "gte",
      "value": 120,
      "data_type": "number",
      "unit": "V",
      "logical_operator": "and"
    }
  ],
  "range_filters": [
    {
      "field": "unit_price",
      "min_value": 10.0,
      "max_value": 100.0,
      "include_min": true,
      "include_max": true
    }
  ],
  "price_range": {
    "min_price": 10.0,
    "max_price": 500.0,
    "currency": "USD"
  },
  "sort_by": "name",
  "sort_order": "asc",
  "include_inactive": false,
  "include_deleted": false
}
```

#### Query Parameters

- `page` (integer, default: 1): Page number (1-based)
- `size` (integer, default: 20, max: 100): Number of items per page

#### Response

```json
{
  "items": [
    {
      "component": {
        "id": 1,
        "manufacturer": "Schneider Electric",
        "model_number": "QO120",
        "name": "Circuit Breaker 20A",
        "description": "Single pole circuit breaker",
        "category": "PROTECTION",
        "component_type": "CIRCUIT_BREAKER",
        "unit_price": 25.99,
        "specifications": {
          "electrical": {
            "voltage_rating": 120,
            "current_rating": 20,
            "poles": 1
          }
        },
        "is_active": true,
        "is_deleted": false,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      },
      "relevance_score": 0.95,
      "matched_fields": ["name", "manufacturer"]
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 1,
    "pages": 1
  },
  "search_metadata": {
    "query_time": "< 1ms",
    "total_filters_applied": 3,
    "search_type": "advanced_builder",
    "fuzzy_search_enabled": false
  },
  "suggestions": []
}
```

#### Filter Operators

| Operator | Description | Applicable Types |
|----------|-------------|------------------|
| `eq` | Equals | All |
| `ne` | Not equals | All |
| `gt` | Greater than | Numbers, Dates |
| `gte` | Greater than or equal | Numbers, Dates |
| `lt` | Less than | Numbers, Dates |
| `lte` | Less than or equal | Numbers, Dates |
| `contains` | Contains substring | Strings |
| `starts_with` | Starts with | Strings |
| `ends_with` | Ends with | Strings |
| `in` | In list | All |
| `not_in` | Not in list | All |
| `between` | Between values | Numbers, Dates |
| `fuzzy` | Fuzzy match | Strings |
| `regex` | Regular expression | Strings |
| `is_null` | Is null | All |
| `is_not_null` | Is not null | All |

### GET /api/v1/components/search/relevance

Search components with relevance scoring and ranking.

#### Query Parameters

- `search_term` (string, required): Text to search for (min 2 characters)
- `search_fields` (string, optional): Comma-separated list of fields to search
- `fuzzy` (boolean, default: false): Enable fuzzy matching
- `page` (integer, default: 1): Page number
- `size` (integer, default: 20, max: 100): Page size

#### Example Request

```
GET /api/v1/components/search/relevance?search_term=circuit%20breaker&search_fields=name,description&fuzzy=false&page=1&size=20
```

#### Response

Same format as advanced search endpoint, with relevance scores calculated based on match quality.

## Enhanced Bulk Operations

### POST /api/v1/components/bulk/create-validated

Create multiple components with comprehensive validation and duplicate checking.

#### Request Body

```json
[
  {
    "manufacturer": "Schneider Electric",
    "model_number": "QO120",
    "name": "Circuit Breaker 20A",
    "description": "Single pole circuit breaker",
    "category": "PROTECTION",
    "component_type": "CIRCUIT_BREAKER",
    "unit_price": 25.99,
    "specifications": {
      "electrical": {
        "voltage_rating": 120,
        "current_rating": 20,
        "poles": 1
      }
    }
  },
  {
    "manufacturer": "ABB",
    "model_number": "S201-B16",
    "name": "MCB 16A Type B",
    "description": "Miniature circuit breaker",
    "category": "PROTECTION",
    "component_type": "CIRCUIT_BREAKER",
    "unit_price": 18.50,
    "specifications": {
      "electrical": {
        "voltage_rating": 230,
        "current_rating": 16,
        "poles": 1
      }
    }
  }
]
```

#### Query Parameters

- `validate_duplicates` (boolean, default: true): Check for duplicate components
- `batch_size` (integer, default: 100, max: 500): Batch size for processing

#### Response

```json
{
  "total_processed": 2,
  "created": 2,
  "errors": 0,
  "success_rate": 1.0,
  "created_components": [
    {
      "id": 1,
      "manufacturer": "Schneider Electric",
      "model_number": "QO120",
      // ... full component data
    }
  ],
  "validation_errors": []
}
```

### PUT /api/v1/components/bulk/update-selective

Update multiple components with different data for each component.

#### Request Body

```json
[
  {
    "id": 1,
    "unit_price": 28.99,
    "is_preferred": true
  },
  {
    "id": 2,
    "description": "Updated description",
    "specifications": {
      "electrical": {
        "voltage_rating": 240,
        "current_rating": 16
      }
    }
  },
  {
    "id": 3,
    "manufacturer": "Siemens AG",
    "unit_price": 24.50
  }
]
```

#### Query Parameters

- `batch_size` (integer, default: 100, max: 500): Batch size for processing

#### Response

```json
{
  "total_processed": 3,
  "updated": 3,
  "errors": 0,
  "success_rate": 1.0,
  "validation_errors": []
}
```

### DELETE /api/v1/components/bulk/delete

Delete multiple components with soft or hard delete options.

#### Request Body

```json
[1, 2, 3, 4, 5]
```

#### Query Parameters

- `soft_delete` (boolean, default: true): Perform soft delete (preserves data) or hard delete

#### Response

```json
{
  "total_processed": 5,
  "deleted": 5,
  "not_found": 0,
  "success_rate": 1.0,
  "not_found_ids": [],
  "delete_type": "soft"
}
```

## Performance Optimization

### GET /api/v1/components/performance/metrics

Get comprehensive performance metrics for the component system.

#### Response

```json
{
  "component_statistics": {
    "total_components": 1000,
    "active_components": 950,
    "preferred_components": 100,
    "unique_manufacturers": 25,
    "unique_categories": 8,
    "price_statistics": {
      "average": 45.50,
      "minimum": 5.00,
      "maximum": 500.00
    }
  },
  "cache_performance": {
    "memory_cache_size": 50,
    "memory_cache_max_size": 1000,
    "redis_connected": true,
    "redis_used_memory": "2.5MB",
    "redis_connected_clients": 5,
    "redis_total_commands_processed": 12345
  },
  "query_performance": {
    "slow_queries_count": 2,
    "slowest_query_time": 1.5,
    "index_usage": {
      "total_indexes": 15,
      "indexes": [
        {
          "schema": "public",
          "table": "components",
          "index": "idx_components_manufacturer",
          "tuples_read": 1000,
          "tuples_fetched": 950,
          "scans": 100
        }
      ]
    }
  },
  "system_health": {
    "database_connected": true,
    "cache_connected": true,
    "performance_monitoring_active": true
  }
}
```

### POST /api/v1/components/performance/optimize

Perform system performance optimization tasks.

#### Response

```json
{
  "cache_warming": true,
  "cache_cleanup": true,
  "index_analysis": true,
  "query_optimization": true,
  "errors": [],
  "index_suggestions": [
    "CREATE INDEX CONCURRENTLY idx_components_manufacturer_model ON components(manufacturer, model_number);",
    "CREATE INDEX CONCURRENTLY idx_components_specifications_gin ON components USING GIN(specifications);"
  ]
}
```

### DELETE /api/v1/components/cache/invalidate

Invalidate component cache entries for better performance.

#### Query Parameters

- `component_id` (integer, optional): Specific component ID to invalidate

#### Response

```json
{
  "success": true,
  "component_id": 123,
  "scope": "specific",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Authentication & Authorization

All endpoints require authentication via JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

### Required Permissions

- **Advanced Search**: Any authenticated user
- **Bulk Operations**: Users with `COMPONENT_WRITE` permission
- **Performance Optimization**: Users with `ADMIN` role

## Error Handling

### Standard Error Response

```json
{
  "detail": "Error description",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Request validation failed | 400 |
| `AUTHENTICATION_REQUIRED` | Missing or invalid token | 401 |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions | 403 |
| `RESOURCE_NOT_FOUND` | Requested resource not found | 404 |
| `RATE_LIMIT_EXCEEDED` | Too many requests | 429 |
| `INTERNAL_SERVER_ERROR` | Server error | 500 |

## Rate Limiting

API endpoints are rate limited to ensure fair usage:

- **Search endpoints**: 100 requests per minute per user
- **Bulk operations**: 10 requests per minute per user
- **Performance endpoints**: 5 requests per minute per user

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```
