'use client';

/**
 * Component Catalog Page
 * Main page for component management with search, filtering, and CRUD operations
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Settings, Download } from 'lucide-react';
import { 
  ComponentList, 
  ComponentSearch, 
  ComponentFilters, 
  ComponentStats,
  BulkOperations 
} from '@/modules/components';
import { useComponents } from '@/modules/components/api/componentQueries';
import { useComponentStore, useComponentFilters, useComponentSelection } from '@/modules/components/hooks/useComponentStore';
import type { ComponentRead } from '@/modules/components/types';

export default function ComponentCatalogPage() {
  const [showStats, setShowStats] = useState(false);
  const [showFilters, setShowFilters] = useState(true);

  // Store state
  const { listState, updateFilters, setListState, selectComponent, clearSelection } = useComponentStore();
  const filters = useComponentFilters();
  const selectedIds = useComponentSelection();

  // Fetch components
  const {
    data: componentsData,
    isLoading,
    error,
    refetch,
  } = useComponents({
    page: listState.page,
    size: listState.pageSize,
    search_term: filters.search_term || undefined,
    category: filters.category || undefined,
    component_type: filters.component_type || undefined,
    manufacturer: filters.manufacturer || undefined,
    is_preferred: filters.is_preferred ?? undefined,
    is_active: filters.is_active ?? undefined,
  });

  // Handle search
  const handleSearch = (query: string) => {
    updateFilters({ search_term: query });
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: any) => {
    updateFilters(newFilters);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: 'grid' | 'list' | 'table') => {
    setListState({ viewMode: mode });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setListState({ page });
  };

  const handlePageSizeChange = (size: number) => {
    setListState({ pageSize: size, page: 1 });
  };

  // Handle component actions
  const handleComponentView = (component: ComponentRead) => {
    // Navigate to component details
    window.location.href = `/components/${component.id}`;
  };

  const handleComponentEdit = (component: ComponentRead) => {
    // Navigate to edit form
    window.location.href = `/components/${component.id}/edit`;
  };

  const handleComponentDelete = (component: ComponentRead) => {
    // Show delete confirmation
    if (confirm(`Are you sure you want to delete ${component.name}?`)) {
      // Handle delete logic here
      console.log('Deleting component:', component.id);
    }
  };

  const handleTogglePreferred = (component: ComponentRead) => {
    // Handle toggle preferred logic here
    console.log('Toggling preferred for component:', component.id);
  };

  const handleSelectionChange = (selectedIds: number[]) => {
    // Update selection in store
    selectedIds.forEach(id => selectComponent(id));
  };

  const handleCreateNew = () => {
    window.location.href = '/components/new';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Component Catalog</h1>
              <p className="text-sm text-gray-600">
                Manage electrical components and specifications
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowStats(!showStats)}
              >
                <Settings className="h-4 w-4 mr-2" />
                {showStats ? 'Hide Stats' : 'Show Stats'}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Handle export
                  console.log('Exporting components');
                }}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              
              <Button onClick={handleCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                Add Component
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Dashboard */}
      {showStats && (
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <ComponentStats />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex gap-6">
          {/* Sidebar with Filters */}
          {showFilters && (
            <div className="w-80 flex-shrink-0">
              <div className="sticky top-6 space-y-6">
                <ComponentFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClear={() => updateFilters({
                    search_term: '',
                    category: null,
                    component_type: null,
                    manufacturer: '',
                    is_preferred: null,
                    is_active: null,
                    min_price: null,
                    max_price: null,
                    stock_status: '',
                  })}
                />
                
                {selectedIds.length > 0 && (
                  <BulkOperations
                    selectedComponentIds={selectedIds}
                    onSelectionClear={clearSelection}
                    onOperationComplete={() => {
                      refetch();
                      clearSelection();
                    }}
                  />
                )}
              </div>
            </div>
          )}

          {/* Main Content Area */}
          <div className="flex-1 min-w-0">
            <div className="space-y-6">
              {/* Search Bar */}
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <ComponentSearch
                  value={filters.search_term || ''}
                  onSearch={handleSearch}
                  onChange={(value) => updateFilters({ search_term: value })}
                  onClear={() => updateFilters({ search_term: '' })}
                  className="w-full"
                />
              </div>

              {/* Component List */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <ComponentList
                  data={componentsData}
                  isLoading={isLoading}
                  error={error}
                  viewMode={listState.viewMode}
                  selectedComponents={selectedIds}
                  onViewModeChange={handleViewModeChange}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  onComponentView={handleComponentView}
                  onComponentEdit={handleComponentEdit}
                  onComponentDelete={handleComponentDelete}
                  onTogglePreferred={handleTogglePreferred}
                  onSelectionChange={handleSelectionChange}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Toggle Filters Button (Mobile) */}
      <div className="fixed bottom-4 left-4 lg:hidden">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="bg-white shadow-lg"
        >
          <Settings className="h-4 w-4 mr-2" />
          {showFilters ? 'Hide' : 'Show'} Filters
        </Button>
      </div>
    </div>
  );
}
