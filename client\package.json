{"name": "ultimate-electrical-designer-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:component-management": "vitest --config vitest.component-management.config.ts", "test:component-management:coverage": "vitest --config vitest.component-management.config.ts --coverage", "test:component-management:ui": "vitest --config vitest.component-management.config.ts --ui", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.525.0", "next": "15.3.0", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.0.0", "zustand": "^4.4.0"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "@vitest/coverage-v8": "^1.6.1", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.3.0", "jsdom": "^23.0.0", "postcss": "^8", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^4.1.0", "typescript": "^5", "vitest": "^1.0.0"}}