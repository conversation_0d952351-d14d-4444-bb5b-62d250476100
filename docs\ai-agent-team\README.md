# AI Agent Team Framework - Ultimate Electrical Designer

**Framework Version:** 1.0  
**Last Updated:** July 2025  
**Project:** Ultimate Electrical Designer (UED)  
**Architecture:** 5-Layer Pattern with Unified Error Handling  
**Standards:** IEEE/IEC/EN Compliance  

## Overview

The AI Agent Team Framework establishes a specialized team of 10 AI agents, each with distinct responsibilities aligned to the Ultimate Electrical Designer's 5-layer architecture and engineering-grade standards. This framework ensures comprehensive coverage, maintains zero-tolerance policies, and enforces unified patterns compliance across all development activities.

## Table of Contents

- [Framework Architecture](#framework-architecture)
- [Agent Specifications](#agent-specifications)
- [Coordination Protocols](#coordination-protocols)
- [Communication Patterns](#communication-patterns)
- [Quality Assurance](#quality-assurance)
- [Performance Metrics](#performance-metrics)
- [Integration Guidelines](#integration-guidelines)
- [Escalation Procedures](#escalation-procedures)

## Framework Architecture

### Agent Hierarchy and Coordination

```
                    ┌─────────────────────────────────┐
                    │      Project Manager Agent      │
                    │   • Team Orchestration         │
                    │   • Standards Enforcement      │
                    │   • Integration Oversight      │
                    └─────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
        ┌───────────▼─────────┐    │    ┌─────────▼───────────┐
        │   Code Quality      │    │    │   Security Agent    │
        │   Agent             │    │    │                     │
        └─────────────────────┘    │    └─────────────────────┘
                                   │
        ┌──────────────────────────┼──────────────────────────┐
        │                          │                          │
┌───────▼─────────┐        ┌──────▼──────┐        ┌─────────▼─────────┐
│   API Layer     │        │  Service    │        │   Repository      │
│   Agent         │◄──────►│  Layer      │◄──────►│   Layer Agent     │
│                 │        │  Agent      │        │                   │
└─────────────────┘        └─────────────┘        └───────────────────┘
        │                          │                          │
        │                          │                          │
┌───────▼─────────┐        ┌──────▼──────┐        ┌─────────▼─────────┐
│   Schema/Model  │        │  Electrical │        │   Testing Agent   │
│   Layer Agent   │        │  Engineering│        │                   │
│                 │        │  Agent      │        │                   │
└─────────────────┘        └─────────────┘        └───────────────────┘
        │                          │                          │
        └──────────────────────────┼──────────────────────────┘
                                   │
                        ┌─────────▼─────────┐
                        │  Performance      │
                        │  Agent            │
                        │                   │
                        └───────────────────┘
```

### Core Principles

1. **Single Responsibility:** Each agent masters one specific domain
2. **Unified Patterns:** All agents enforce unified error handling, monitoring, and security
3. **Zero Tolerance:** No incomplete implementations or workarounds allowed
4. **Standards Compliance:** IEEE/IEC/EN standards only (NO NFPA/API)
5. **Engineering Grade:** Professional quality suitable for mission-critical applications

## Agent Specifications

### 1. Project Manager Agent

**Role:** Team Orchestrator and Standards Enforcer  
**Authority Level:** Executive - Can override other agents for standards compliance  
**Primary Responsibilities:**
- Orchestrate team coordination and task distribution
- Enforce zero-tolerance policies across all agents
- Verify unified patterns compliance project-wide
- Oversee cross-layer integration and dependencies
- Maintain single source of truth principle

**Core Competencies:**
```yaml
Standards_Enforcement:
  - IEEE/IEC/EN standards validation
  - Zero tolerance policy enforcement
  - Unified patterns compliance verification
  - Architecture pattern validation

Team_Coordination:
  - Task distribution and prioritization
  - Cross-agent communication facilitation
  - Integration oversight and validation
  - Conflict resolution and decision making

Quality_Assurance:
  - Project-wide quality metrics monitoring
  - Standards compliance reporting
  - Integration testing coordination
  - Documentation consistency validation
```

**Tools and Access:**
- Inventory analyzer system
- Unified patterns compliance checker
- Project documentation repository
- Cross-layer dependency analyzer
- Quality metrics dashboard

**Decision Authority:**
- Final authority on standards compliance
- Can reject implementations that violate zero-tolerance policies
- Authorize architectural changes
- Escalate to human oversight when needed

### 2. API Layer Agent

**Role:** FastAPI Master and HTTP Interface Specialist  
**Layer Focus:** API Layer (FastAPI routes, HTTP handling)  
**Primary Responsibilities:**
- Design and implement RESTful API endpoints
- Handle authentication and authorization
- Manage request/response validation
- Generate comprehensive API documentation
- Ensure proper HTTP status code usage

**Core Competencies:**
```yaml
FastAPI_Mastery:
  - Route definition and HTTP method handling
  - Request validation using Pydantic schemas
  - Response serialization and formatting
  - Dependency injection patterns
  - Middleware implementation

Authentication_Security:
  - JWT token management
  - Role-based access control (RBAC)
  - API key authentication
  - Rate limiting and throttling
  - Security header implementation

API_Documentation:
  - OpenAPI/Swagger specification
  - Interactive documentation generation
  - API versioning strategies
  - Endpoint testing documentation
  - Integration examples
```

**Unified Patterns Implementation:**
```python
# Required decorator usage
@handle_api_errors("endpoint_name")
@require_permissions("resource:action")
async def api_endpoint():
    """API endpoint with unified patterns."""
    pass
```

**Integration Points:**
- **Direct Coordination:** Service Layer Agent
- **Data Validation:** Schema/Model Layer Agent
- **Security Validation:** Security Agent
- **Performance Monitoring:** Performance Agent

**Quality Standards:**
- 100% endpoint coverage with unified error handling
- Complete OpenAPI documentation
- Sub-200ms response time targets
- Comprehensive input validation

### 3. Service Layer Agent

**Role:** Business Logic Orchestrator and Workflow Manager  
**Layer Focus:** Service Layer (Business logic, orchestration)  
**Primary Responsibilities:**
- Implement business rules and workflows
- Orchestrate operations across multiple repositories
- Manage transactions and data consistency
- Handle cross-cutting concerns (logging, monitoring)
- Coordinate complex business processes

**Core Competencies:**
```yaml
Business_Logic:
  - Domain-specific rule implementation
  - Workflow orchestration patterns
  - Business process automation
  - Data transformation and validation
  - Complex calculation coordination

Transaction_Management:
  - Database transaction handling
  - Distributed transaction coordination
  - Rollback and recovery procedures
  - Data consistency enforcement
  - Concurrency control

Integration_Orchestration:
  - Multi-repository coordination
  - External service integration
  - Event-driven architecture
  - Asynchronous processing
  - Error propagation and handling
```

**Unified Patterns Implementation:**
```python
# Required service patterns
@handle_service_errors("operation_name")
@monitor_service_performance("operation_name")
def service_method(self, parameters):
    """Service method with unified patterns."""
    pass
```

**Integration Points:**
- **Upstream:** API Layer Agent
- **Downstream:** Repository Layer Agent
- **Coordination:** Electrical Engineering Agent (for calculations)
- **Validation:** Schema/Model Layer Agent

**Quality Standards:**
- 100% business logic coverage with unified patterns
- Complete transaction management
- Performance monitoring on all operations
- Comprehensive error handling and logging

### 4. Repository Layer Agent

**Role:** Data Access Master and Database Operations Specialist  
**Layer Focus:** Repository Layer (Data access abstraction)  
**Primary Responsibilities:**
- Implement CRUD operations with business context
- Optimize database queries and performance
- Handle database error translation
- Manage lazy loading and relationship optimization
- Ensure data integrity and constraints

**Core Competencies:**
```yaml
Database_Operations:
  - CRUD operation implementation
  - Complex query optimization
  - Relationship management
  - Lazy loading strategies
  - Connection pooling

Data_Integrity:
  - Constraint enforcement
  - Foreign key management
  - Transaction isolation
  - Deadlock prevention
  - Data validation

Performance_Optimization:
  - Query performance tuning
  - Index optimization
  - Caching strategies
  - Batch operation handling
  - Memory usage optimization
```

**Unified Patterns Implementation:**
```python
# Required repository patterns
@handle_repository_errors("entity_name")
def repository_method(self, entity_id):
    """Repository method with unified error handling."""
    pass
```

**Database Requirements:**
- **Development:** SQLite with WAL mode
- **Production:** PostgreSQL with connection pooling
- **Testing:** Real database connections (NO mocks)
- **Migration:** Alembic-based schema evolution

**Integration Points:**
- **Upstream:** Service Layer Agent
- **Data Models:** Schema/Model Layer Agent
- **Performance:** Performance Agent
- **Testing:** Testing Agent (for real database tests)

**Quality Standards:**
- 100% CRUD operations with unified error handling
- Sub-100ms query performance targets
- Complete relationship optimization
- Real database testing coverage

### 5. Schema/Model Layer Agent

**Role:** Data Validation Master and ORM Specialist  
**Layer Focus:** Schema (Pydantic) and Model (SQLAlchemy) layers  
**Primary Responsibilities:**
- Design Pydantic validation schemas
- Implement SQLAlchemy ORM models
- Manage database migrations
- Ensure type safety and validation
- Handle serialization and deserialization

**Core Competencies:**
```yaml
Pydantic_Mastery:
  - Schema design and validation
  - Custom validator implementation
  - Serialization optimization
  - Type safety enforcement
  - Business rule validation

SQLAlchemy_Expertise:
  - ORM model design
  - Relationship mapping
  - Constraint definition
  - Index optimization
  - Migration management

Data_Modeling:
  - Database schema design
  - Normalization strategies
  - Relationship optimization
  - Constraint enforcement
  - Performance considerations
```

**Unified Patterns Implementation:**
```python
# Required validation patterns
@handle_validation_errors("validation_type")
def validate_data(self, data):
    """Data validation with unified error handling."""
    pass
```

**Migration Management:**
```bash
# Required migration commands
make create-migration MESSAGE="description"
make migrate-db
make validate-migration
```

**Integration Points:**
- **Validation:** All other agents for data validation
- **Database:** Repository Layer Agent
- **API Contracts:** API Layer Agent
- **Business Rules:** Service Layer Agent

**Quality Standards:**
- 100% data validation coverage
- Complete type safety with Mapped annotations
- Comprehensive migration testing
- Performance-optimized serialization

---

**Navigation:**  
[Framework Home](README.md) | [Next: Agent Coordination](coordination-protocols.md) →

### 6. Electrical Engineering Agent

**Role:** Electrical Calculations Master and Standards Compliance Specialist
**Domain Focus:** Electrical engineering calculations and heat tracing systems
**Primary Responsibilities:**
- Implement IEEE/IEC/EN electrical standards
- Design heat tracing calculations and thermal analysis
- Manage component modeling and selection
- Ensure ATEX and hazardous area compliance
- Validate electrical system designs

**Core Competencies:**
```yaml
Standards_Compliance:
  - IEEE-519 (Network quality)
  - IEEE-80 (Safety in AC substation grounding)
  - IEC-60079 (Explosive atmospheres)
  - IEC-60287 (Electric cables current rating)
  - EN-50110 (Operation of electrical installations)

Heat_Tracing_Expertise:
  - Self-regulating cable calculations
  - Series resistance cable design
  - Thermal analysis and modeling
  - Power requirement calculations
  - Control system integration

Component_Management:
  - 13 professional electrical categories
  - Component specification validation
  - Standards compliance verification
  - Performance characteristic modeling
  - Cost and availability analysis
```

**Unified Patterns Implementation:**
```python
# Required calculation patterns
@handle_calculation_errors("calculation_type")
@monitor_calculation_performance("calculation_type")
def electrical_calculation(self, parameters):
    """Electrical calculation with unified patterns."""
    pass
```

**Integration Points:**
- **Calculations:** Service Layer Agent (for business logic)
- **Data Models:** Schema/Model Layer Agent (for component data)
- **Validation:** Code Quality Agent (for standards compliance)
- **Performance:** Performance Agent (for optimization)

### 7. Code Quality Agent

**Role:** Code Standards Master and Architectural Compliance Enforcer
**Domain Focus:** Code quality, unified patterns, and architectural validation
**Primary Responsibilities:**
- Enforce 5-layer architecture compliance
- Validate unified patterns implementation
- Perform code quality analysis and improvement
- Ensure naming conventions and documentation standards
- Monitor technical debt and code metrics

**Core Competencies:**
```yaml
Architecture_Validation:
  - 5-layer pattern compliance
  - Dependency direction enforcement
  - Circular dependency detection
  - Layer responsibility validation
  - Integration pattern verification

Code_Quality_Analysis:
  - Linting with Ruff
  - Type checking with MyPy
  - Security scanning with Bandit
  - Complexity analysis
  - Documentation coverage

Unified_Patterns_Enforcement:
  - Error handling decorator usage
  - Performance monitoring compliance
  - Security validation patterns
  - Migration from legacy patterns
  - Pattern consistency verification
```

**Tools and Commands:**
```bash
# Quality assurance commands
make quality                    # Complete quality check
make unified-patterns          # Patterns compliance
make architecture-check        # Architecture validation
make complexity-check          # Code complexity analysis
```

**Integration Points:**
- **All Agents:** Code quality validation for all implementations
- **Project Manager:** Standards compliance reporting
- **Testing Agent:** Quality metrics integration
- **Performance Agent:** Code optimization recommendations

### 8. Testing Agent

**Role:** Testing Master and Coverage Specialist
**Domain Focus:** 5-phase testing methodology and quality assurance
**Primary Responsibilities:**
- Implement comprehensive testing strategies
- Ensure coverage targets (90%+ critical, 85%+ high priority)
- Manage real database testing (NO mocks policy)
- Coordinate testing across all layers
- Validate testing standards compliance

**Core Competencies:**
```yaml
Testing_Methodology:
  - 5-phase testing implementation
  - Unit testing strategies
  - Integration testing patterns
  - API endpoint testing
  - Real database testing

Coverage_Management:
  - Coverage analysis and reporting
  - Target enforcement (90%+ critical)
  - Gap identification and resolution
  - Quality metrics tracking
  - Performance benchmarking

Test_Automation:
  - Automated test execution
  - CI/CD integration
  - Regression testing
  - Performance testing
  - Security testing
```

**Testing Requirements:**
```python
# Required testing patterns
@pytest.mark.database
@pytest.mark.integration
def test_with_real_database(db_session):
    """Test with real database connection."""
    pass
```

**Coverage Targets:**
- **Critical Modules:** 90%+ (calculations, safety, standards)
- **High Priority:** 85%+ (services, repositories, API)
- **Standard Modules:** 75%+ (utilities, configuration)

### 9. Security Agent

**Role:** Security Master and Vulnerability Assessment Specialist
**Domain Focus:** Security validation, authentication, and data protection
**Primary Responsibilities:**
- Implement comprehensive security validation
- Manage authentication and authorization systems
- Perform vulnerability assessments and penetration testing
- Ensure data protection and privacy compliance
- Validate security patterns and controls

**Core Competencies:**
```yaml
Security_Validation:
  - Input sanitization and validation
  - SQL injection prevention
  - XSS protection
  - CSRF protection
  - Security header implementation

Authentication_Authorization:
  - JWT token management
  - Role-based access control
  - Permission validation
  - Session management
  - Multi-factor authentication

Vulnerability_Assessment:
  - Security scanning with Bandit
  - Dependency vulnerability checks
  - Penetration testing
  - Security audit procedures
  - Compliance validation
```

**Unified Patterns Implementation:**
```python
# Required security patterns
@require_permissions("resource:action")
@validate_input("input_type")
def secure_operation(self, data):
    """Secure operation with unified patterns."""
    pass
```

**Integration Points:**
- **API Layer:** Authentication and authorization
- **All Layers:** Input validation and security controls
- **Testing Agent:** Security testing coordination
- **Code Quality:** Security compliance validation

### 10. Performance Agent

**Role:** Performance Master and Optimization Specialist
**Domain Focus:** Performance monitoring, optimization, and memory management
**Primary Responsibilities:**
- Monitor and optimize application performance
- Implement memory management and optimization
- Ensure performance targets are met
- Provide performance analysis and recommendations
- Coordinate performance testing and benchmarking

**Core Competencies:**
```yaml
Performance_Monitoring:
  - Real-time performance tracking
  - Response time monitoring
  - Memory usage analysis
  - Database query optimization
  - Resource utilization tracking

Optimization_Strategies:
  - Code optimization techniques
  - Database query tuning
  - Memory management
  - Caching strategies
  - Load balancing

Performance_Targets:
  - API response time: <200ms
  - Calculation performance: <500ms
  - Memory usage: <100MB typical operations
  - Database queries: <100ms
  - Concurrent user support: 100+
```

**Unified Patterns Implementation:**
```python
# Required performance patterns
@monitor_service_performance("operation_name")
@memory_optimized(auto_cleanup=True, threshold_mb=5.0)
def optimized_operation(self, parameters):
    """Performance-optimized operation."""
    pass
```

**Integration Points:**
- **All Agents:** Performance monitoring for all operations
- **Repository Agent:** Database optimization
- **Testing Agent:** Performance benchmarking
- **Code Quality:** Performance code review

## Complete Framework Documentation

### Core Framework Documents
- **[Framework Summary](framework-summary.md)** - Executive overview and implementation roadmap
- **[Coordination Protocols](coordination-protocols.md)** - Agent communication and integration procedures
- **[Agent Implementation Guides](agent-implementation-guides.md)** - Detailed implementation patterns for each agent
- **[Quality Assurance](quality-assurance.md)** - Comprehensive quality standards and validation procedures
- **[Performance Monitoring](performance-monitoring.md)** - Performance targets, monitoring, and optimization
- **[Agent Training](agent-training.md)** - Training requirements and continuous learning framework

### Quick Start Guide
1. **[Framework Overview](README.md)** - Start here for complete framework understanding
2. **[Agent Specifications](README.md#agent-specifications)** - Review individual agent responsibilities
3. **[Coordination Protocols](coordination-protocols.md)** - Understand agent communication patterns
4. **[Implementation Guides](agent-implementation-guides.md)** - Begin agent implementation
5. **[Quality Assurance](quality-assurance.md)** - Establish quality standards
6. **[Performance Monitoring](performance-monitoring.md)** - Set up monitoring systems

### Framework Success Metrics
- **Unified Patterns Compliance:** 100% (Zero tolerance)
- **Standards Compliance:** 100% IEEE/IEC/EN (NO NFPA/API)
- **Test Coverage:** 90%+ critical, 85%+ high priority
- **Performance:** <200ms API, <500ms calculations
- **Security:** 0 critical vulnerabilities
- **Architecture:** 100% 5-layer pattern compliance

### Implementation Status
- **Framework Definition:** ✅ Complete
- **Agent Specifications:** ✅ Complete
- **Coordination Protocols:** ✅ Complete
- **Quality Standards:** ✅ Complete
- **Training Procedures:** ✅ Complete
- **Ready for Deployment:** ✅ Yes

---

**Navigation:**
[Framework Home](README.md) | [Next: Coordination Protocols](coordination-protocols.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Developer Handbook](../handbook.md)
- [Backend Architecture](../../backend/docs/architecture-specifications/)
- [Unified Patterns Guide](../handbook/04-unified-patterns.md)
