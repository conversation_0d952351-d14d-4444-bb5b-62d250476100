# **Backend Specification*

This document provides a comprehensive and logical roadmap for implementing and enhancing key features in the backend, focusing on the "what" and "why" for each layer. It is designed to be a clear specification and tracking document for AI Agents.

## **1. Foundation: Central Universal Component Catalog**

**Description:** Establish a single, robust database model that serves as the definitive source of truth for all types of components (cables, circuit breakers, insulation, etc.) across all manufacturers. This catalog will include detailed technical specifications and pricing information.

**Why it's important:** Avoids data redundancy, ensures consistency for component data, centralizes pricing, and forms the core library from which all automated selections and manual assignments will draw. It's the bedrock for accurate BOMs and intelligent design.

### **1.1. Enhance Model Layer (src/core/models/components.py)**

- **What:** Modify the existing Component model (or create if it's a new base) to include fields for universal component data and a flexible JSONB field for type-specific properties.  
  \# src/core/models/components.py (Snippet)  
  from sqlalchemy import Column, String, Float, <PERSON>SO<PERSON>, <PERSON>olean, Text  
  from sqlalchemy.orm import Mapped, mapped_column  
  \# from src.core.models.base import Base \# Assumed Base  
    
  class Component(Base):  
  \_\_tablename\_\_ = "components"  
  id: Mapped\[str\] = mapped_column(String(36), primary_key=True) \# UUID  
  name: Mapped\[str\] = mapped_column(String(255), unique=True, index=True)  
  \# ... existing generic fields ...  
  manufacturer: Mapped\[str \| None\] = mapped_column(String(255), nullable=True)  
  model_number: Mapped\[str \| None\] = mapped_column(String(255), nullable=True)  
  component_type: Mapped\[str\] = mapped_column(String(50), index=True) \# e.g., "Cable", "Circuit Breaker"  
    
  \# Pricing  
  unit_material_price: Mapped\[float \| None\] = mapped_column(Float, nullable=True)  
  unit_installation_price: Mapped\[float \| None\] = mapped_column(Float, nullable=True)  
  price_unit_of_measure: Mapped\[str \| None\] = mapped_column(String(50), nullable=True)  
    
  \# Flexible JSONB for type-specific technical properties  
  technical_properties: Mapped\[dict \| None\] = mapped_column(JSON, nullable=True)  
    
  is_selectable: Mapped\[bool\] = mapped_column(Boolean, default=False)  
  is_active: Mapped\[bool\] = mapped_column(Boolean, default=True)

- **Why:** This schema centralizes all component data. component_type allows for easy filtering. technical_properties (JSONB) provides schema-less flexibility for widely varying data across component types (e.g., a cable has cross_sectional_area_mm2, a breaker has breaking_capacity_kA). Pricing fields are added here as they are part of the catalog definition.

### **1.2. Enhance Schema Layer (src/core/schemas/component.py)**

- **What:** Create or update Pydantic schemas for Component (ComponentCreateSchema, ComponentUpdateSchema, ComponentReadSchema) to reflect all the new fields, including technical_properties (which can be Dict\[str, Any\] in Pydantic).

- **Why:** Ensures robust data validation and clear API contracts for managing the central component catalog.

### **1.3. Enhance Repository Layer (src/core/repositories/component_repository.py)**

- **What:** Modify the existing ComponentRepository to handle CRUD operations for the enhanced Component model. Add methods for querying/filtering components by component_type, manufacturer, model_number, or by searching within technical_properties.

- **Why:** Provides the data access methods for managing the master catalog.

### **1.4. Enhance Service Layer (src/core/services/component_service.py)**

- **What:** Modify the ComponentService to encapsulate business logic for managing the central component catalog. This service will be responsible for validating component data, handling updates/creates (including the flexible technical_properties), and providing methods for other services to retrieve specific component types.

- **Why:** Centralizes business rules related to the component catalog.

### **1.5. Data Import for Components (XLSX/CSV via CLI)**

- **What:** Implement a robust import mechanism to populate the central Component catalog from XLSX or CSV files, as catalogues are typically external. This involves:

  - Adding pandas as a dependency.

  - Creating src/core/data_import/component_importer.py with logic to:

    - Read XLSX/CSV files.

    - Map spreadsheet columns to Component model fields.

    - Parse and structure technical_properties from relevant columns into the JSONB format.

    - Implement **idempotent** insertion/update logic (check for existing component by unique keys like name, manufacturer, model_number).

    - Include robust error handling and reporting for invalid rows.

  - Adding a CLI command (src/cli/main.py) to trigger the import, allowing specifying file path and type (--type xlsx/csv).

  - Adding a Makefile command for easy execution.

- **Why:** Enables efficient bulk population and updates of the extensive component catalog from common external data formats, avoiding manual data entry and ensuring data accuracy.

## **2. Customizable Cable Installation Circumstances**

**Description:** Allow project admins to define and select specific environmental and installation parameters that affect cable sizing calculations. These can be system defaults or overridden per project/location.

**Why it's important:** Provides realistic and precise inputs for cable ampacity derating calculations, crucial for accurate design.

### **2.1. New Model Layer (src/core/models/cable_installation_circumstance.py)**

- **What:** Create a new SQLAlchemy ORM model CableInstallationCircumstance to store these specific parameter sets.  
  \# src/core/models/cable_installation_circumstance.py (Snippet)  
  from sqlalchemy import Column, String, Float, Integer, Enum as SQLEnum  
  \# from src.core.models.base import Base \# Assumed Base  
  import enum  
    
  class InstallationMethodType(enum.Enum): \# Define an enum for method types  
  DIRECT_BURIED = "DirectBuried"  
  IN_AIR_SINGLE_TRAY = "InAirSingleTray"  
  \# ... more types  
    
  class CableInstallationCircumstance(Base):  
  \_\_tablename\_\_ = "cable_installation_circumstances"  
  id: Mapped\[str\] = mapped_column(String(36), primary_key=True) \# UUID  
  name: Mapped\[str\] = mapped_column(String(255), unique=True, index=True)  
  description: Mapped\[str \| None\] = mapped_column(String, nullable=True)  
  ambient_temperature: Mapped\[float\] = mapped_column(Float) \# °C  
  soil_thermal_resistivity: Mapped\[float \| None\] = mapped_column(Float, nullable=True) \# K.m/W (for buried)  
  installation_method_type: Mapped\[InstallationMethodType\] = mapped_column(SQLEnum(InstallationMethodType))  
  number_of_cables_in_group: Mapped\[int\] = mapped_column(Integer, default=1)  
  depth_of_burial_m: Mapped\[float \| None\] = mapped_column(Float, nullable=True) \# meters  
  \# ... other relevant environmental/installation factors

- **Why:** Provides a structured way to store reusable sets of installation conditions that impact cable thermal performance.

### **2.2. New Schema Layer (src/core/schemas/cable_installation_circumstance.py)**

- **What:** Create Pydantic schemas (CableInstallationCircumstanceCreateSchema, UpdateSchema, ReadSchema) corresponding to the new model.

- **Why:** Enables validation and serialization of installation circumstance data for API interactions.

### **2.3. New Repository Layer (src/core/repositories/cable_circumstance_repository.py)**

- **What:** Create a new CableCircumstanceRepository for CRUD operations on CableInstallationCircumstance records.

- **Why:** Provides dedicated data access for managing installation circumstances.

### **2.4. New Service Layer (src/core/services/cable_circumstance_service.py)**

- **What:** Create a new CableCircumstanceService to handle the business logic for managing these circumstances (creating, retrieving, updating, deleting).

- **Why:** Encapsulates business rules related to defining and selecting installation circumstances.

### **2.5. Enhance Model Layer (src/core/models/project.py)**

- **What:** Add default_cable_circumstance_id (Foreign Key to CableInstallationCircumstance) to the Project model.

- **Why:** Allows a project to have a default set of installation conditions.

### **2.6. Enhance Schema Layer (src/core/schemas/project.py)**

- **What:** Update Project schemas to include default_cable_circumstance_id.

- **Why:** Exposes this setting via the API for project management.

### **2.7. New API Layer (src/api/v1/cable_installation_circumstance_routes.py)**

- **What:** Create new FastAPI routes for CRUD operations on CableInstallationCircumstance entities.

- **Why:** Provides API endpoints for the frontend to manage these customizable circumstances.

## **3. Automated Cable Selection Service**

**Description:** Implement an intelligent service that automatically selects the optimal power cable based on load requirements, installation length, customizable circumstances, and validation against voltage drop and short-circuit current limits.

**Why it's important:** Automates a complex design task, reduces manual errors, ensures compliance, and significantly speeds up the design process.

### **3.1. New Model Layer (src/core/models/cable_selection_matrix.py)**

- **What:** Create the CableSelectionMatrix model (as described in section 1.1) to store curated lists of Component.id references from the central catalog.

- **Why:** Provides the structured data for the automated selection algorithm, allowing for system-wide defaults and project-specific overrides of *which* cables are considered for selection.

### **3.2. New Schema Layer (src/core/schemas/cable_selection_matrix.py)**

- **What:** Create Pydantic schemas for CableSelectionMatrix (Create, Update, Read).

- **Why:** Defines API contracts for managing these selection matrices.

### **3.3. New Repository Layer (src/core/repositories/cable_selection_matrix_repository.py)**

- **What:** Create a new CableSelectionMatrixRepository for CRUD operations on CableSelectionMatrix records.

- **Why:** Provides data access for managing selection matrices.

### **3.4. Enhance Model Layer (src/core/models/project.py)**

- **What:** Add default_cable_selection_matrix_id (Foreign Key to CableSelectionMatrix) to the Project model.

- **Why:** Allows a project to specify which cable selection matrix to use by default.

### **3.5. Enhance Schema Layer (src/core/schemas/project.py)**

- **What:** Update Project schemas to include default_cable_selection_matrix_id.

- **Why:** Exposes this setting via the API.

### **3.6. New Schemas Layer (src/core/schemas/cable_selection.py)**

- **What:** Create new Pydantic schemas for the selection request and response:

  - CableSelectionRequestSchema: Inputs like nominal_load_power_kW, nominal_load_voltage_V, load_power_factor, cabling_length_m, installation_circumstance_id (to link to the chosen circumstance), project_id (to determine the correct selection matrix default), and potentially target_matrix_id (for explicit override).

  - CableSelectionResponseSchema: Output for the selected cable (e.g., catalog_component_id, name, cross_sectional_area_mm2, calculated_ampacity, calculated_voltage_drop, short_circuit_check_result, and **all relevant copied properties and pricing from the Component catalog**).

- **Why:** Defines the API contract for triggering the cable selection and receiving its results.

### **3.7. Enhance Calculations Layer (src/core/calculations/cable_sizing_calcs.py)**

- **What:** Significantly enhance cable_sizing_calcs.py to contain robust functions for:

  - **Ampacity Calculation:** Takes cable properties (from Component.technical_properties) and CableInstallationCircumstance parameters. Applies derating factors.

  - **Voltage Drop Calculation:** Takes cable length, load characteristics, and cable properties (resistance, reactance).

  - **Short-Circuit Current Withstand:** Verifies cable integrity against fault current for a duration based on k_factor from Component and short_circuit_duration_limit_s.

- **Why:** These are the core engineering functions that determine if a candidate cable meets the requirements. They must be accurate and well-tested.

### **3.8. Enhance Standards Layer (src/core/standards/compliance_rules.py)**

- **What:** Add specific compliance rules and derating factors that the CableSelectionService will use (e.g., maximum allowable voltage drop percentage, specific derating curves based on installation_method_type, short-circuit breaking time requirements).

- **Why:** Ensures that automated cable selection adheres to relevant engineering standards.

### **3.9. New Service Layer (src/core/services/cable_selection_service.py)**

- **What:** Create a new CableSelectionService to encapsulate the intelligent selection logic.

  - **Orchestration:** Retrieves the correct CableSelectionMatrix (project-specific or system default). Fetches the detailed Component data from the central catalog (via component_service) for each candidate in the matrix. Retrieves the selected CableInstallationCircumstance (via cable_circumstance_service).

  - **Selection Algorithm:** Iterates through candidate cables, calls functions in cable_sizing_calcs.py (Calculations Layer), and applies rules from compliance_rules.py (Standards Layer). Filters unsuitable cables. Selects the optimal cable (e.g., smallest suitable cross-section).

  - **Data Copying:** **Crucially**, when a cable is selected, this service will create/update a *project-specific instance* of that cable (e.g., a Cable ORM object linked to the project/load). It will copy all relevant technical properties (from Component.technical_properties) and pricing (unit_material_price, unit_installation_price) from the **central Component catalog entry** to this project-specific instance. This ensures design integrity even if catalog prices change.

- **Why:** Centralizes the complex business logic for automated cable selection, ensuring a consistent and robust selection process. Handles the vital copying of catalog data to project instances.

### **3.10. Enhance Project-Specific Component Models & Schemas**

- **What:** Create/update models (e.g., src/core/models/electrical.py for Cable, Breaker, etc.) and their corresponding Pydantic schemas. These models represent *instances* of components in a project design. They will now store:

  - A foreign key (catalog_component_id) back to the original Component in the central catalog.

  - The selected_matrix_cable_entry_id (if selected via matrix).

  - **All relevant technical properties (e.g., cross_sectional_area_mm2, reference_ampacity_A, breaking_capacity_kA) are COPIED as fields or a JSONB field at the instance level.**

  - **COPIED PRICING** (unit_material_price, unit_installation_price) from the catalog.

  - Calculated results for this instance (e.g., calculated_ampacity_A, calculated_voltage_drop_V).

  - A foreign key to installation_circumstance_id if this specific instance has an override.

- **Why:** Ensures that project designs are self-contained and reflect the component's state at the time of design, independent of future catalog updates. Enables accurate BOMs.

### **3.13. Enhance Existing Service Layer (electrical_service.py / circuit_design_service.py)**

- **What:** These services will now call the new cable_selection_service (and other \*selection_services) to get component suggestions or selections during design. They will also handle the logic for *manual assignment* of components, which involves fetching a Component from the central catalog and copying its properties/pricing to the project instance.

- **Why:** Integrates the automated selection into the overall design workflow and ensures consistency for manual assignments.

### **3.12. New API Layer (src/api/v1/cable_selection_routes.py)**

- **What:** Create new FastAPI routes to:

  - Manage CableSelectionMatrix entities (CRUD, duplication from system default).

  - Expose the POST /api/v1/select-cable endpoint that takes CableSelectionRequestSchema and returns CableSelectionResponseSchema.

- **Why:** Provides the API for frontend to trigger automated cable selection and manage matrices.

## **4. Bill of Materials (BOM) & Costing**

**Description:** Generate comprehensive cost estimates by aggregating all components within a project and utilizing their unit material and installation prices.

**Why it's important:** Provides essential financial insights for project planning and budgeting.

### **4.1. New Service Layer (src/core/services/bill_of_materials_service.py)**

- **What:** Create a new BillOfMaterialsService.

  - **Logic:** This service will query all project-specific component instances (which now hold their **copied unit_material_price and unit_installation_price**). It will sum up total material costs and total installation costs based on quantity, length, etc., for each component.

  - **Output:** Formats the aggregated data into BomReportSchema and BomItemSchema.

- **Why:** Centralizes the cost aggregation logic, ensuring accuracy and consistency across reports.

### **4.2. New Schema Layer (src/core/schemas/bill_of_materials.py)**

- **What:** Define BomItemSchema and BomReportSchema for the structure of the Bill of Materials output.

- **Why:** Provides a clear API contract for BOM data.

### **4.3. Enhance Report Generation Layer (src/core/reports/)**

- **What:** Update report_service.py and associated data_preparators/document_populator to call the BillOfMaterialsService and include BOM data in generated reports.

- **Why:** Integrates costing into the application's reporting capabilities.

### **4.4. New API Layer (src/api/v1/bill_of_materials_routes.py)**

- **What:** Add a new endpoint (e.g., GET /api/v1/projects/{project_id}/bill-of-materials) that triggers the BillOfMaterialsService and returns the BomReportSchema.

- **Why:** Exposes BOM generation functionality to the frontend.

## **5. Future Extensions: Matrices for Other Components**

**Description:** Apply the established component selection matrix pattern to other critical components such as circuit breakers, isolation switches, and frequency converters.

**Why it's important:** Extends design automation and intelligence across the entire electrical system, providing comprehensive design recommendations.

### **5.1. Generalize Previous Steps:**

- **Model Layer:** New CircuitBreakerSelectionMatrix, IsolationSwitchSelectionMatrix, FrequencyConverterSelectionMatrix models, each with a JSONB matrix_data referring to Component.ids and specific selection context.

- **Schema Layer:** Corresponding Create, Update, Read, Request, Response schemas for each new matrix and selection service.

- **Repository Layer:** New repositories for each new selection matrix.

- **Service Layer:** New CircuitBreakerSelectionService, IsolationSwitchSelectionService, FrequencyConverterSelectionService, mirroring the CableSelectionService logic. These services will:

  - Fetch relevant \*SelectionMatrix and Component data.

  - Call specialized calculations from the Calculations Layer (e.g., fault current calculations for breakers, motor sizing for VFDs).

  - Apply rules from the Standards Layer.

  - **Copy properties and pricing** from the Component catalog to the project-specific component instances.

- **Calculations Layer:** Significant enhancements to include new calculation types (e.g., short_circuit_calcs.py, motor_load_calcs.py) that derive the *requirements* for these components.

- **Standards Layer:** New compliance rules specific to these component types.

- **API Layer:** New CRUD endpoints for managing these new matrices and selection endpoints (e.g., POST /api/v1/select-breaker).
