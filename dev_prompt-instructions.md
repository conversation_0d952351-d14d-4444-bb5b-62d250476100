## COMMON INSTRUCTIONS

Continue completing the remaining tasks with immaculate attention to detail, ensuring all tasks follow the most robust patterns for the Ultimate Electrical Designer project standards while maintaining flexibility for future expansions. Ensure that all implementations follow unified patterns (e.g., @handle_repository_errors decorators) and maintain engineering-grade robustness without backward compatibility. Focus on business-critical functionality for professional electrical design application requirements (IEEE/IEC/EN standards compliance, client management, project lifecycle, component specifications).

---

Continue fixing the failing tests with immaculate attention to detail, ensuring all follow the most robust patterns for the Ultimate Electrical Designer project standards while maintaining flexibility for future expansions. Ensure that all implementations follow unified patterns (e.g., @handle_repository_errors decorators) and maintain engineering-grade robustness without backward compatibility. Focus on business-critical functionality for professional electrical design application requirements (IEEE/IEC/EN standards compliance, client management, project lifecycle, component specifications).

---

## TESTING

### Increase Coverage of Layer
Complete the Ultimate Electrical Designer layer test suite to achieve 85%+ coverage per module with 100% pass rate, following the proven 5-phase methodology. Current total coverage is []%.

**Priority Order by Coverage Level**

**CRITICAL (Coverage <40% - Immediate Action Required):**
- []

**HIGH (Coverage 40-69% - Next Priority):**
- []

**MAINTENANCE (Coverage >70% - Verify Only):**
- []

**Mandatory 5-Phase Testing Methodology**

**Phase 1: Discovery & Analysis**
Use codebase-retrieval to analyze each repository module's:
- Complete method inventory and signatures
- Business logic requirements for professional electrical design
- Dependencies and integration points
- Existing test coverage gaps
- Model field structure and relationships
- Verify current coverage

**Phase 2: Task Planning & Prioritization**
- Create task management plan with ~20-minute professional work units
- Prioritize by dependency order: base → domain-specific → integration
- Focus on implementing missing business-critical functionality over updating test data

**Phase 3: Implementation Standards**

**Phase 4: Verification & Quality Assurance**

**Phase 5: Documentation & Handover**

**Assessment Priority for Test Failures**

**Before updating any test data, systematically assess:**

1. **Examine Implementation Structure**: Use codebase-retrieval to understand actual model fields, method signatures, and business logic
2. **Identify Missing Business-Critical Functionality**: Determine if missing methods/fields are essential for professional electrical design workflows (client management, project tracking, IEEE/IEC/EN standards compliance, component specifications)
3. **Implementation vs Test Data Decision**: Prioritize implementing missing business-critical functionality over simply updating test data to match incomplete implementations

**Ultimate Electrical Designer Standards:**
- Engineering-grade robustness with no backward compatibility
- Professional electrical design application requirements
- IEEE/IEC/EN standards compliance (NO NFPA/API references)
- Unified patterns throughout (@handle_repository_errors, lazy loading, proper error handling)
- Single source of truth per calculation type (no 'simple/basic/advanced/professional' variants)

**Success Criteria**
- Each repository module achieves 80%+ line coverage
- 100% pass rate across all repository tests
- Complete unified patterns compliance verification
- Real database connections with proper session management
- All business-critical functionality implemented for professional electrical design workflows
- Systematic documentation of methodology and lessons learned

### Increase Test Coverage of Files to xx %+
Increase the test coverage of [Name] to >90%, currently at [Cover]%: 
```
Name                                                              Stmts   Miss Branch BrPart   Cover   Missing
--------------------------------------------------------------------------------------------------------------


```
Phase 1: Analyze the missing lines in [Name]
Phase 2: Implement missing tests with immaculate attention to detail, ensuring all tasks follow the most robust patterns for the Ultimate Electrical Designer project standards while maintaining flexibility for future expansions. 
**For Ultimate Electrical Designer Standards:** 
- Prioritize implementing missing business-critical functionality over simply updating test data 
- Ensure all implementations follow unified patterns (e.g., @handle_repository_errors decorators) 
- Maintain engineering-grade robustness without backward compatibility 
- Use real database connections (NO mocks)
- Focus on professional electrical design application requirements (IEEE/IEC/EN standards compliance, client management, project lifecycle, component specifications) 
Phase 3: Verify 100% pass rate for the new tests
**Verification Requirements:**
- Confirm 100% pass rate for the specific test module

---

### Fix Failing Tests
Execute the comprehensive test suite using `.\make test` from the backend directory and perform a systematic analysis of the results.

Based on the test results, create a structured task list using the proven 5-phase methodology (Discovery & Analysis → Task Planning → Implementation → Verification → Documentation) with the following requirements:

1. **Task Organization**: Break down fixes into ~20-minute professional development work units, organized by dependency order and priority levels:
   - Critical: Core electrical calculations, database operations, repository layer issues
   - High: Service layer, business logic, IEEE/IEC/EN standards compliance
   - Medium: API endpoints, error handling patterns
   - Low: Utilities, performance monitoring

2. **Implementation Standards**: All fixes must comply with Ultimate Electrical Designer project standards:
   - Use unified patterns with @handle_repository_errors/@handle_database_errors decorators
   - Remove ALL legacy try-catch blocks (no backward compatibility)
   - Implement lazy loading for circular imports
   - Maintain 85%+ test coverage per module with 100% pass rate
   - Use real database connections (NO mocks)
   - Follow standardized tests/test_*/ directory structure

3. **Assessment Priority**: For each failing test, systematically assess:
   - Examine actual implementation structure first using codebase-retrieval
   - Determine if missing functionality is essential for professional electrical design workflows
   - Prioritize implementing missing business-critical functionality over simply updating test data
   - Ensure IEEE/IEC/EN standards compliance (NO NFPA or API standards)

4. **Verification Requirements**: After each fix:
   - Verify unified patterns compliance using `python scripts/inventory_analyzer.py unified-patterns`
   - Confirm 100% pass rate for the specific test module
   - Document lessons learned in backend/docs/how-to/testing/ directory

Begin implementation immediately after task planning, focusing on the highest priority failures first while maintaining engineering-grade robustness and immaculate attention to detail for the Ultimate Electrical Designer project.

---

