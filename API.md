# API Specifications Document
## Ultimate Electrical Designer Application

### Document Information
- **Version**: 1.0
- **Date**: July 2025
- **API Version**: v1
- **Base URL**: `/api/v1`
- **Authentication**: JWT <PERSON>er <PERSON>ken
- **Standards**: OpenAPI 3.0+, RESTful Design

---

## 1. API Overview

### 1.1 Base Information
- **Protocol**: HTTPS
- **Content Type**: `application/json`
- **Character Set**: UTF-8
- **Date Format**: ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)
- **API Versioning**: URI versioning (`/api/v1/`)

### 1.2 Authentication
All protected endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt_token>
```

### 1.3 Response Format
All API responses follow a consistent structure:
```json\n{\n  \"success\": true,\n  \"data\": {},\n  \"message\": \"Operation completed successfully\",\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"request_id\": \"req_123456789\"\n}\n```

Error responses:
```json\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"VALIDATION_ERROR\",\n    \"message\": \"Invalid input data\",\n    \"details\": {\n      \"field\": \"voltage_rating\",\n      \"reason\": \"Value must be positive\"\n    }\n  },\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"request_id\": \"req_123456789\"\n}\n```\n\n---\n\n## 2. Authentication Endpoints\n\n### 2.1 POST /auth/login\n**Description**: Authenticate user and obtain JWT token\n\n**Request Body**:\n```json\n{\n  \"username\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"token_type\": \"bearer\",\n    \"expires_in\": 86400,\n    \"user\": {\n      \"id\": \"user_123\",\n      \"username\": \"<EMAIL>\",\n      \"full_name\": \"John Doe\",\n      \"role\": \"engineer\",\n      \"permissions\": [\"view_components\", \"perform_calculations\"],\n      \"certifications\": [\"PE\"],\n      \"last_login\": \"2025-07-16T10:30:00.000Z\"\n    }\n  },\n  \"message\": \"Login successful\"\n}\n```\n\n**Error Responses**:\n- `401 Unauthorized`: Invalid credentials\n- `429 Too Many Requests`: Rate limit exceeded\n\n### 2.2 POST /auth/refresh\n**Description**: Refresh JWT token\n\n**Request Body**:\n```json\n{\n  \"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"expires_in\": 86400\n  }\n}\n```\n\n### 2.3 POST /auth/logout\n**Description**: Invalidate JWT token\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"message\": \"Logout successful\"\n}\n```\n\n### 2.4 GET /auth/me\n**Description**: Get current user information\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"user_123\",\n    \"username\": \"<EMAIL>\",\n    \"full_name\": \"John Doe\",\n    \"role\": \"engineer\",\n    \"permissions\": [\"view_components\", \"perform_calculations\"],\n    \"certifications\": [\"PE\"],\n    \"created_at\": \"2025-01-01T00:00:00.000Z\",\n    \"last_login\": \"2025-07-16T10:30:00.000Z\"\n  }\n}\n```\n\n---\n\n## 3. User Management Endpoints\n\n### 3.1 GET /users\n**Description**: Get list of users (Admin only)\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Query Parameters**:\n- `skip` (integer, optional): Number of records to skip (default: 0)\n- `limit` (integer, optional): Maximum number of records to return (default: 100)\n- `role` (string, optional): Filter by user role\n- `search` (string, optional): Search by username or full name\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"users\": [\n      {\n        \"id\": \"user_123\",\n        \"username\": \"<EMAIL>\",\n        \"full_name\": \"John Doe\",\n        \"role\": \"engineer\",\n        \"is_active\": true,\n        \"certifications\": [\"PE\"],\n        \"created_at\": \"2025-01-01T00:00:00.000Z\",\n        \"last_login\": \"2025-07-16T10:30:00.000Z\"\n      }\n    ],\n    \"total\": 1,\n    \"skip\": 0,\n    \"limit\": 100\n  }\n}\n```\n\n### 3.2 POST /users\n**Description**: Create new user (Admin only)\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"username\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"full_name\": \"Jane Smith\",\n  \"role\": \"engineer\",\n  \"certifications\": [\"PE\"]\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"user_456\",\n    \"username\": \"<EMAIL>\",\n    \"full_name\": \"Jane Smith\",\n    \"role\": \"engineer\",\n    \"is_active\": true,\n    \"certifications\": [\"PE\"],\n    \"created_at\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"User created successfully\"\n}\n```\n\n### 3.3 GET /users/{user_id}\n**Description**: Get specific user information\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `user_id` (string): User ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"user_123\",\n    \"username\": \"<EMAIL>\",\n    \"full_name\": \"John Doe\",\n    \"role\": \"engineer\",\n    \"is_active\": true,\n    \"certifications\": [\"PE\"],\n    \"created_at\": \"2025-01-01T00:00:00.000Z\",\n    \"last_login\": \"2025-07-16T10:30:00.000Z\"\n  }\n}\n```\n\n### 3.4 PUT /users/{user_id}\n**Description**: Update user information\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `user_id` (string): User ID\n\n**Request Body**:\n```json\n{\n  \"full_name\": \"John Updated Doe\",\n  \"role\": \"senior_engineer\",\n  \"certifications\": [\"PE\", \"PMP\"]\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"user_123\",\n    \"username\": \"<EMAIL>\",\n    \"full_name\": \"John Updated Doe\",\n    \"role\": \"senior_engineer\",\n    \"is_active\": true,\n    \"certifications\": [\"PE\", \"PMP\"],\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"User updated successfully\"\n}\n```\n\n### 3.5 DELETE /users/{user_id}\n**Description**: Deactivate user (Admin only)\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `user_id` (string): User ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"message\": \"User deactivated successfully\"\n}\n```\n\n---\n\n## 4. Component Management Endpoints\n\n### 4.1 GET /components\n**Description**: Get list of electrical components\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Query Parameters**:\n- `skip` (integer, optional): Number of records to skip (default: 0)\n- `limit` (integer, optional): Maximum number of records to return (default: 100)\n- `category_id` (string, optional): Filter by category ID\n- `type_id` (string, optional): Filter by component type ID\n- `manufacturer` (string, optional): Filter by manufacturer\n- `search` (string, optional): Search by name or description\n- `voltage_min` (number, optional): Minimum voltage rating\n- `voltage_max` (number, optional): Maximum voltage rating\n- `current_min` (number, optional): Minimum current rating\n- `current_max` (number, optional): Maximum current rating\n- `standards` (string, optional): Filter by compliance standards (comma-separated)\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"components\": [\n      {\n        \"id\": \"comp_123\",\n        \"name\": \"3-Phase Motor 10HP\",\n        \"description\": \"High-efficiency 3-phase induction motor\",\n        \"manufacturer\": \"ABB\",\n        \"model_number\": \"M3BP-160M\",\n        \"category\": {\n          \"id\": \"cat_motors\",\n          \"name\": \"Motors\",\n          \"code\": \"MOT\"\n        },\n        \"type\": {\n          \"id\": \"type_induction\",\n          \"name\": \"Induction Motor\",\n          \"code\": \"IND\"\n        },\n        \"specifications\": {\n          \"voltage_rating\": 460,\n          \"current_rating\": 14.2,\n          \"power_rating\": 7460,\n          \"power_factor\": 0.85,\n          \"efficiency\": 0.92,\n          \"frequency\": 60,\n          \"poles\": 4,\n          \"rpm\": 1750\n        },\n        \"compliance_standards\": [\"IEEE-112\", \"IEC-60034\", \"EN-60034\"],\n        \"created_at\": \"2025-07-16T10:30:00.000Z\",\n        \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n      }\n    ],\n    \"total\": 1,\n    \"skip\": 0,\n    \"limit\": 100\n  }\n}\n```\n\n### 4.2 POST /components\n**Description**: Create new electrical component\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"name\": \"3-Phase Motor 15HP\",\n  \"description\": \"High-efficiency 3-phase induction motor\",\n  \"manufacturer\": \"Siemens\",\n  \"model_number\": \"1LA7-180M\",\n  \"category_id\": \"cat_motors\",\n  \"type_id\": \"type_induction\",\n  \"specifications\": {\n    \"voltage_rating\": 460,\n    \"current_rating\": 21.3,\n    \"power_rating\": 11190,\n    \"power_factor\": 0.87,\n    \"efficiency\": 0.93,\n    \"frequency\": 60,\n    \"poles\": 4,\n    \"rpm\": 1750\n  },\n  \"compliance_standards\": [\"IEEE-112\", \"IEC-60034\", \"EN-60034\"]\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"comp_456\",\n    \"name\": \"3-Phase Motor 15HP\",\n    \"description\": \"High-efficiency 3-phase induction motor\",\n    \"manufacturer\": \"Siemens\",\n    \"model_number\": \"1LA7-180M\",\n    \"category_id\": \"cat_motors\",\n    \"type_id\": \"type_induction\",\n    \"specifications\": {\n      \"voltage_rating\": 460,\n      \"current_rating\": 21.3,\n      \"power_rating\": 11190,\n      \"power_factor\": 0.87,\n      \"efficiency\": 0.93,\n      \"frequency\": 60,\n      \"poles\": 4,\n      \"rpm\": 1750\n    },\n    \"compliance_standards\": [\"IEEE-112\", \"IEC-60034\", \"EN-60034\"],\n    \"created_at\": \"2025-07-16T10:30:00.000Z\",\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"Component created successfully\"\n}\n```\n\n### 4.3 GET /components/{component_id}\n**Description**: Get specific electrical component\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `component_id` (string): Component ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"comp_123\",\n    \"name\": \"3-Phase Motor 10HP\",\n    \"description\": \"High-efficiency 3-phase induction motor\",\n    \"manufacturer\": \"ABB\",\n    \"model_number\": \"M3BP-160M\",\n    \"category\": {\n      \"id\": \"cat_motors\",\n      \"name\": \"Motors\",\n      \"code\": \"MOT\",\n      \"description\": \"Electric motors and drives\"\n    },\n    \"type\": {\n      \"id\": \"type_induction\",\n      \"name\": \"Induction Motor\",\n      \"code\": \"IND\",\n      \"description\": \"Three-phase induction motors\"\n    },\n    \"specifications\": {\n      \"voltage_rating\": 460,\n      \"current_rating\": 14.2,\n      \"power_rating\": 7460,\n      \"power_factor\": 0.85,\n      \"efficiency\": 0.92,\n      \"frequency\": 60,\n      \"poles\": 4,\n      \"rpm\": 1750,\n      \"service_factor\": 1.15,\n      \"insulation_class\": \"F\",\n      \"protection_class\": \"IP55\"\n    },\n    \"compliance_standards\": [\"IEEE-112\", \"IEC-60034\", \"EN-60034\"],\n    \"created_at\": \"2025-07-16T10:30:00.000Z\",\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  }\n}\n```\n\n### 4.4 PUT /components/{component_id}\n**Description**: Update electrical component\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `component_id` (string): Component ID\n\n**Request Body**:\n```json\n{\n  \"name\": \"3-Phase Motor 10HP Updated\",\n  \"description\": \"High-efficiency 3-phase induction motor with updated specifications\",\n  \"specifications\": {\n    \"voltage_rating\": 460,\n    \"current_rating\": 14.2,\n    \"power_rating\": 7460,\n    \"power_factor\": 0.87,\n    \"efficiency\": 0.94,\n    \"frequency\": 60,\n    \"poles\": 4,\n    \"rpm\": 1750,\n    \"service_factor\": 1.15,\n    \"insulation_class\": \"F\",\n    \"protection_class\": \"IP55\"\n  }\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"comp_123\",\n    \"name\": \"3-Phase Motor 10HP Updated\",\n    \"description\": \"High-efficiency 3-phase induction motor with updated specifications\",\n    \"manufacturer\": \"ABB\",\n    \"model_number\": \"M3BP-160M\",\n    \"category_id\": \"cat_motors\",\n    \"type_id\": \"type_induction\",\n    \"specifications\": {\n      \"voltage_rating\": 460,\n      \"current_rating\": 14.2,\n      \"power_rating\": 7460,\n      \"power_factor\": 0.87,\n      \"efficiency\": 0.94,\n      \"frequency\": 60,\n      \"poles\": 4,\n      \"rpm\": 1750,\n      \"service_factor\": 1.15,\n      \"insulation_class\": \"F\",\n      \"protection_class\": \"IP55\"\n    },\n    \"compliance_standards\": [\"IEEE-112\", \"IEC-60034\", \"EN-60034\"],\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"Component updated successfully\"\n}\n```\n\n### 4.5 DELETE /components/{component_id}\n**Description**: Delete electrical component\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `component_id` (string): Component ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"message\": \"Component deleted successfully\"\n}\n```\n\n---\n\n## 5. Component Categories Endpoints\n\n### 5.1 GET /component-categories\n**Description**: Get list of component categories\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Query Parameters**:\n- `skip` (integer, optional): Number of records to skip (default: 0)\n- `limit` (integer, optional): Maximum number of records to return (default: 100)\n- `parent_id` (string, optional): Filter by parent category ID\n- `search` (string, optional): Search by name or description\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"categories\": [\n      {\n        \"id\": \"cat_motors\",\n        \"name\": \"Motors\",\n        \"description\": \"Electric motors and drives\",\n        \"category_code\": \"MOT\",\n        \"parent_id\": null,\n        \"specifications_template\": {\n          \"voltage_rating\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n          \"current_rating\": {\"type\": \"number\", \"unit\": \"A\", \"required\": true},\n          \"power_rating\": {\"type\": \"number\", \"unit\": \"W\", \"required\": true},\n          \"power_factor\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n          \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n          \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true}\n        },\n        \"children\": [],\n        \"created_at\": \"2025-07-16T10:30:00.000Z\",\n        \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n      }\n    ],\n    \"total\": 1,\n    \"skip\": 0,\n    \"limit\": 100\n  }\n}\n```\n\n### 5.2 POST /component-categories\n**Description**: Create new component category\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"name\": \"Transformers\",\n  \"description\": \"Power and distribution transformers\",\n  \"category_code\": \"TRF\",\n  \"parent_id\": null,\n  \"specifications_template\": {\n    \"voltage_primary\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n    \"voltage_secondary\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n    \"power_rating\": {\"type\": \"number\", \"unit\": \"VA\", \"required\": true},\n    \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true},\n    \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true}\n  }\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"cat_transformers\",\n    \"name\": \"Transformers\",\n    \"description\": \"Power and distribution transformers\",\n    \"category_code\": \"TRF\",\n    \"parent_id\": null,\n    \"specifications_template\": {\n      \"voltage_primary\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n      \"voltage_secondary\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n      \"power_rating\": {\"type\": \"number\", \"unit\": \"VA\", \"required\": true},\n      \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true},\n      \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true}\n    },\n    \"created_at\": \"2025-07-16T10:30:00.000Z\",\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"Category created successfully\"\n}\n```\n\n### 5.3 GET /component-categories/{category_id}\n**Description**: Get specific component category\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `category_id` (string): Category ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"cat_motors\",\n    \"name\": \"Motors\",\n    \"description\": \"Electric motors and drives\",\n    \"category_code\": \"MOT\",\n    \"parent_id\": null,\n    \"specifications_template\": {\n      \"voltage_rating\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n      \"current_rating\": {\"type\": \"number\", \"unit\": \"A\", \"required\": true},\n      \"power_rating\": {\"type\": \"number\", \"unit\": \"W\", \"required\": true},\n      \"power_factor\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n      \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n      \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true}\n    },\n    \"children\": [\n      {\n        \"id\": \"cat_induction_motors\",\n        \"name\": \"Induction Motors\",\n        \"description\": \"Three-phase induction motors\",\n        \"category_code\": \"IND\"\n      }\n    ],\n    \"parent\": null,\n    \"created_at\": \"2025-07-16T10:30:00.000Z\",\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  }\n}\n```\n\n### 5.4 GET /component-categories/{category_id}/tree\n**Description**: Get category tree structure\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `category_id` (string): Category ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"cat_motors\",\n    \"name\": \"Motors\",\n    \"category_code\": \"MOT\",\n    \"children\": [\n      {\n        \"id\": \"cat_induction_motors\",\n        \"name\": \"Induction Motors\",\n        \"category_code\": \"IND\",\n        \"children\": [\n          {\n            \"id\": \"cat_low_voltage_motors\",\n            \"name\": \"Low Voltage Motors\",\n            \"category_code\": \"LVM\",\n            \"children\": []\n          }\n        ]\n      },\n      {\n        \"id\": \"cat_synchronous_motors\",\n        \"name\": \"Synchronous Motors\",\n        \"category_code\": \"SYN\",\n        \"children\": []\n      }\n    ]\n  }\n}\n```\n\n---\n\n## 6. Component Types Endpoints\n\n### 6.1 GET /component-types\n**Description**: Get list of component types\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Query Parameters**:\n- `skip` (integer, optional): Number of records to skip (default: 0)\n- `limit` (integer, optional): Maximum number of records to return (default: 100)\n- `category_id` (string, optional): Filter by category ID\n- `search` (string, optional): Search by name or description\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"types\": [\n      {\n        \"id\": \"type_induction\",\n        \"name\": \"Induction Motor\",\n        \"description\": \"Three-phase induction motors\",\n        \"type_code\": \"IND\",\n        \"category_id\": \"cat_motors\",\n        \"category\": {\n          \"id\": \"cat_motors\",\n          \"name\": \"Motors\",\n          \"code\": \"MOT\"\n        },\n        \"specifications_template\": {\n          \"voltage_rating\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n          \"current_rating\": {\"type\": \"number\", \"unit\": \"A\", \"required\": true},\n          \"power_rating\": {\"type\": \"number\", \"unit\": \"W\", \"required\": true},\n          \"power_factor\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n          \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n          \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true},\n          \"poles\": {\"type\": \"integer\", \"unit\": \"\", \"required\": true},\n          \"rpm\": {\"type\": \"number\", \"unit\": \"rpm\", \"required\": true}\n        },\n        \"default_specifications\": {\n          \"frequency\": 60,\n          \"poles\": 4,\n          \"power_factor\": 0.85,\n          \"efficiency\": 0.90\n        },\n        \"created_at\": \"2025-07-16T10:30:00.000Z\",\n        \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n      }\n    ],\n    \"total\": 1,\n    \"skip\": 0,\n    \"limit\": 100\n  }\n}\n```\n\n### 6.2 POST /component-types\n**Description**: Create new component type\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"name\": \"Synchronous Motor\",\n  \"description\": \"Three-phase synchronous motors\",\n  \"type_code\": \"SYN\",\n  \"category_id\": \"cat_motors\",\n  \"specifications_template\": {\n    \"voltage_rating\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n    \"current_rating\": {\"type\": \"number\", \"unit\": \"A\", \"required\": true},\n    \"power_rating\": {\"type\": \"number\", \"unit\": \"W\", \"required\": true},\n    \"power_factor\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n    \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n    \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true},\n    \"poles\": {\"type\": \"integer\", \"unit\": \"\", \"required\": true},\n    \"rpm\": {\"type\": \"number\", \"unit\": \"rpm\", \"required\": true},\n    \"excitation_type\": {\"type\": \"string\", \"unit\": \"\", \"required\": true}\n  },\n  \"default_specifications\": {\n    \"frequency\": 60,\n    \"poles\": 4,\n    \"power_factor\": 0.95,\n    \"efficiency\": 0.95,\n    \"excitation_type\": \"brushless\"\n  }\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"type_synchronous\",\n    \"name\": \"Synchronous Motor\",\n    \"description\": \"Three-phase synchronous motors\",\n    \"type_code\": \"SYN\",\n    \"category_id\": \"cat_motors\",\n    \"specifications_template\": {\n      \"voltage_rating\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n      \"current_rating\": {\"type\": \"number\", \"unit\": \"A\", \"required\": true},\n      \"power_rating\": {\"type\": \"number\", \"unit\": \"W\", \"required\": true},\n      \"power_factor\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n      \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n      \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true},\n      \"poles\": {\"type\": \"integer\", \"unit\": \"\", \"required\": true},\n      \"rpm\": {\"type\": \"number\", \"unit\": \"rpm\", \"required\": true},\n      \"excitation_type\": {\"type\": \"string\", \"unit\": \"\", \"required\": true}\n    },\n    \"default_specifications\": {\n      \"frequency\": 60,\n      \"poles\": 4,\n      \"power_factor\": 0.95,\n      \"efficiency\": 0.95,\n      \"excitation_type\": \"brushless\"\n    },\n    \"created_at\": \"2025-07-16T10:30:00.000Z\",\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"Component type created successfully\"\n}\n```\n\n### 6.3 GET /component-types/{type_id}\n**Description**: Get specific component type\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `type_id` (string): Component type ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"type_induction\",\n    \"name\": \"Induction Motor\",\n    \"description\": \"Three-phase induction motors\",\n    \"type_code\": \"IND\",\n    \"category_id\": \"cat_motors\",\n    \"category\": {\n      \"id\": \"cat_motors\",\n      \"name\": \"Motors\",\n      \"code\": \"MOT\",\n      \"description\": \"Electric motors and drives\"\n    },\n    \"specifications_template\": {\n      \"voltage_rating\": {\"type\": \"number\", \"unit\": \"V\", \"required\": true},\n      \"current_rating\": {\"type\": \"number\", \"unit\": \"A\", \"required\": true},\n      \"power_rating\": {\"type\": \"number\", \"unit\": \"W\", \"required\": true},\n      \"power_factor\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n      \"efficiency\": {\"type\": \"number\", \"unit\": \"\", \"required\": true},\n      \"frequency\": {\"type\": \"number\", \"unit\": \"Hz\", \"required\": true},\n      \"poles\": {\"type\": \"integer\", \"unit\": \"\", \"required\": true},\n      \"rpm\": {\"type\": \"number\", \"unit\": \"rpm\", \"required\": true}\n    },\n    \"default_specifications\": {\n      \"frequency\": 60,\n      \"poles\": 4,\n      \"power_factor\": 0.85,\n      \"efficiency\": 0.90\n    },\n    \"created_at\": \"2025-07-16T10:30:00.000Z\",\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  }\n}\n```\n\n### 6.4 GET /component-types/by-category/{category_id}\n**Description**: Get component types by category\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `category_id` (string): Category ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"types\": [\n      {\n        \"id\": \"type_induction\",\n        \"name\": \"Induction Motor\",\n        \"type_code\": \"IND\",\n        \"description\": \"Three-phase induction motors\"\n      },\n      {\n        \"id\": \"type_synchronous\",\n        \"name\": \"Synchronous Motor\",\n        \"type_code\": \"SYN\",\n        \"description\": \"Three-phase synchronous motors\"\n      }\n    ],\n    \"category\": {\n      \"id\": \"cat_motors\",\n      \"name\": \"Motors\",\n      \"code\": \"MOT\"\n    }\n  }\n}\n```\n\n---\n\n## 7. Electrical Calculations Endpoints\n\n### 7.1 POST /calculations/load\n**Description**: Perform load calculation\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"calculation_type\": \"load_calculation\",\n  \"components\": [\n    {\n      \"component_id\": \"comp_123\",\n      \"quantity\": 2,\n      \"diversity_factor\": 0.8,\n      \"demand_factor\": 0.9\n    },\n    {\n      \"component_id\": \"comp_456\",\n      \"quantity\": 1,\n      \"diversity_factor\": 1.0,\n      \"demand_factor\": 1.0\n    }\n  ],\n  \"parameters\": {\n    \"voltage_level\": \"low_voltage\",\n    \"power_factor\": 0.85,\n    \"temperature\": 25,\n    \"altitude\": 0,\n    \"installation_method\": \"cable_tray\",\n    \"standards\": [\"IEEE-141\", \"IEC-60364\"]\n  }\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"calculation_id\": \"calc_789\",\n    \"calculation_type\": \"load_calculation\",\n    \"results\": {\n      \"total_load\": 26110,\n      \"connected_load\": 29800,\n      \"demand_load\": 26110,\n      \"current_load\": 56.76,\n      \"power_factor\": 0.86,\n      \"component_loads\": [\n        {\n          \"component_id\": \"comp_123\",\n          \"component_name\": \"3-Phase Motor 10HP\",\n          \"quantity\": 2,\n          \"individual_load\": 7460,\n          \"total_load\": 11936,\n          \"diversity_factor\": 0.8,\n          \"demand_factor\": 0.9\n        },\n        {\n          \"component_id\": \"comp_456\",\n          \"component_name\": \"3-Phase Motor 15HP\",\n          \"quantity\": 1,\n          \"individual_load\": 11190,\n          \"total_load\": 11190,\n          \"diversity_factor\": 1.0,\n          \"demand_factor\": 1.0\n        }\n      ]\n    },\n    \"compliance_status\": {\n      \"overall_compliance\": true,\n      \"standard_results\": [\n        {\n          \"standard\": \"IEEE-141\",\n          \"compliant\": true,\n          \"message\": \"Load calculation complies with IEEE-141 standards\",\n          \"recommendations\": []\n        },\n        {\n          \"standard\": \"IEC-60364\",\n          \"compliant\": true,\n          \"message\": \"Load calculation complies with IEC-60364 standards\",\n          \"recommendations\": []\n        }\n      ]\n    },\n    \"parameters\": {\n      \"voltage_level\": \"low_voltage\",\n      \"power_factor\": 0.85,\n      \"temperature\": 25,\n      \"altitude\": 0,\n      \"installation_method\": \"cable_tray\",\n      \"standards\": [\"IEEE-141\", \"IEC-60364\"]\n    },\n    \"calculation_time_ms\": 156,\n    \"calculated_by\": \"user_123\",\n    \"calculation_timestamp\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"Load calculation completed successfully\"\n}\n```\n\n### 7.2 POST /calculations/voltage-drop\n**Description**: Perform voltage drop calculation\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"calculation_type\": \"voltage_drop\",\n  \"cable_parameters\": {\n    \"conductor_material\": \"copper\",\n    \"conductor_size\": \"12_AWG\",\n    \"cable_length\": 100,\n    \"installation_method\": \"conduit\",\n    \"temperature\": 30,\n    \"number_of_conductors\": 3\n  },\n  \"electrical_parameters\": {\n    \"voltage\": 480,\n    \"current\": 25,\n    \"power_factor\": 0.85,\n    \"frequency\": 60\n  },\n  \"standards\": [\"IEEE-141\", \"IEC-60364\"]\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"calculation_id\": \"calc_790\",\n    \"calculation_type\": \"voltage_drop\",\n    \"results\": {\n      \"voltage_drop\": 8.7,\n      \"voltage_drop_percent\": 1.81,\n      \"final_voltage\": 471.3,\n      \"resistance_per_unit\": 0.00193,\n      \"reactance_per_unit\": 0.00041,\n      \"impedance_per_unit\": 0.00198,\n      \"power_loss\": 543.75,\n      \"efficiency\": 97.8\n    },\n    \"cable_parameters\": {\n      \"conductor_material\": \"copper\",\n      \"conductor_size\": \"12_AWG\",\n      \"cable_length\": 100,\n      \"installation_method\": \"conduit\",\n      \"temperature\": 30,\n      \"number_of_conductors\": 3,\n      \"derating_factor\": 1.0\n    },\n    \"electrical_parameters\": {\n      \"voltage\": 480,\n      \"current\": 25,\n      \"power_factor\": 0.85,\n      \"frequency\": 60\n    },\n    \"compliance_status\": {\n      \"overall_compliance\": true,\n      \"standard_results\": [\n        {\n          \"standard\": \"IEEE-141\",\n          \"compliant\": true,\n          \"message\": \"Voltage drop 1.81% is within IEEE-141 limit of 5%\",\n          \"recommendations\": []\n        },\n        {\n          \"standard\": \"IEC-60364\",\n          \"compliant\": true,\n          \"message\": \"Voltage drop complies with IEC-60364 standards\",\n          \"recommendations\": []\n        }\n      ]\n    },\n    \"calculation_time_ms\": 89,\n    \"calculated_by\": \"user_123\",\n    \"calculation_timestamp\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"Voltage drop calculation completed successfully\"\n}\n```\n\n### 7.3 POST /calculations/short-circuit\n**Description**: Perform short circuit calculation\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"calculation_type\": \"short_circuit\",\n  \"system_parameters\": {\n    \"voltage\": 480,\n    \"frequency\": 60,\n    \"system_impedance\": 0.05,\n    \"transformer_rating\": 1000,\n    \"transformer_impedance\": 0.04\n  },\n  \"fault_parameters\": {\n    \"fault_type\": \"three_phase\",\n    \"fault_location\": \"load_bus\",\n    \"cable_impedance\": 0.02,\n    \"arc_resistance\": 0.0\n  },\n  \"standards\": [\"IEEE-141\", \"IEC-60909\"]\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"calculation_id\": \"calc_791\",\n    \"calculation_type\": \"short_circuit\",\n    \"results\": {\n      \"three_phase_fault_current\": 4823,\n      \"single_phase_fault_current\": 4156,\n      \"line_to_line_fault_current\": 4177,\n      \"fault_mva\": 4.01,\n      \"x_over_r_ratio\": 12.5,\n      \"dc_component\": 0.85,\n      \"total_fault_current\": 5473,\n      \"fault_duration\": 0.083\n    },\n    \"system_parameters\": {\n      \"voltage\": 480,\n      \"frequency\": 60,\n      \"system_impedance\": 0.05,\n      \"transformer_rating\": 1000,\n      \"transformer_impedance\": 0.04\n    },\n    \"fault_parameters\": {\n      \"fault_type\": \"three_phase\",\n      \"fault_location\": \"load_bus\",\n      \"cable_impedance\": 0.02,\n      \"arc_resistance\": 0.0\n    },\n    \"protective_device_coordination\": {\n      \"upstream_device\": \"600A_breaker\",\n      \"downstream_device\": \"100A_breaker\",\n      \"selectivity\": true,\n      \"coordination_time\": 0.2\n    },\n    \"compliance_status\": {\n      \"overall_compliance\": true,\n      \"standard_results\": [\n        {\n          \"standard\": \"IEEE-141\",\n          \"compliant\": true,\n          \"message\": \"Short circuit calculation complies with IEEE-141 standards\",\n          \"recommendations\": []\n        },\n        {\n          \"standard\": \"IEC-60909\",\n          \"compliant\": true,\n          \"message\": \"Short circuit calculation complies with IEC-60909 standards\",\n          \"recommendations\": []\n        }\n      ]\n    },\n    \"calculation_time_ms\": 234,\n    \"calculated_by\": \"user_123\",\n    \"calculation_timestamp\": \"2025-07-16T10:30:00.000Z\"\n  },\n  \"message\": \"Short circuit calculation completed successfully\"\n}\n```\n\n### 7.4 GET /calculations\n**Description**: Get list of calculations\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Query Parameters**:\n- `skip` (integer, optional): Number of records to skip (default: 0)\n- `limit` (integer, optional): Maximum number of records to return (default: 100)\n- `calculation_type` (string, optional): Filter by calculation type\n- `calculated_by` (string, optional): Filter by user ID\n- `start_date` (string, optional): Filter by start date (ISO 8601)\n- `end_date` (string, optional): Filter by end date (ISO 8601)\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"calculations\": [\n      {\n        \"id\": \"calc_789\",\n        \"calculation_type\": \"load_calculation\",\n        \"summary\": {\n          \"total_load\": 26110,\n          \"components_count\": 2,\n          \"compliance_status\": \"compliant\"\n        },\n        \"calculated_by\": \"user_123\",\n        \"calculated_by_name\": \"John Doe\",\n        \"calculation_timestamp\": \"2025-07-16T10:30:00.000Z\",\n        \"calculation_time_ms\": 156\n      },\n      {\n        \"id\": \"calc_790\",\n        \"calculation_type\": \"voltage_drop\",\n        \"summary\": {\n          \"voltage_drop_percent\": 1.81,\n          \"compliance_status\": \"compliant\"\n        },\n        \"calculated_by\": \"user_123\",\n        \"calculated_by_name\": \"John Doe\",\n        \"calculation_timestamp\": \"2025-07-16T10:25:00.000Z\",\n        \"calculation_time_ms\": 89\n      }\n    ],\n    \"total\": 2,\n    \"skip\": 0,\n    \"limit\": 100\n  }\n}\n```\n\n### 7.5 GET /calculations/{calculation_id}\n**Description**: Get specific calculation result\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `calculation_id` (string): Calculation ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"calc_789\",\n    \"calculation_type\": \"load_calculation\",\n    \"results\": {\n      \"total_load\": 26110,\n      \"connected_load\": 29800,\n      \"demand_load\": 26110,\n      \"current_load\": 56.76,\n      \"power_factor\": 0.86,\n      \"component_loads\": [\n        {\n          \"component_id\": \"comp_123\",\n          \"component_name\": \"3-Phase Motor 10HP\",\n          \"quantity\": 2,\n          \"individual_load\": 7460,\n          \"total_load\": 11936,\n          \"diversity_factor\": 0.8,\n          \"demand_factor\": 0.9\n        }\n      ]\n    },\n    \"compliance_status\": {\n      \"overall_compliance\": true,\n      \"standard_results\": [\n        {\n          \"standard\": \"IEEE-141\",\n          \"compliant\": true,\n          \"message\": \"Load calculation complies with IEEE-141 standards\",\n          \"recommendations\": []\n        }\n      ]\n    },\n    \"parameters\": {\n      \"voltage_level\": \"low_voltage\",\n      \"power_factor\": 0.85,\n      \"temperature\": 25,\n      \"altitude\": 0,\n      \"installation_method\": \"cable_tray\",\n      \"standards\": [\"IEEE-141\", \"IEC-60364\"]\n    },\n    \"calculation_time_ms\": 156,\n    \"calculated_by\": \"user_123\",\n    \"calculated_by_name\": \"John Doe\",\n    \"calculation_timestamp\": \"2025-07-16T10:30:00.000Z\"\n  }\n}\n```\n\n### 7.6 DELETE /calculations/{calculation_id}\n**Description**: Delete calculation result\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `calculation_id` (string): Calculation ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"message\": \"Calculation deleted successfully\"\n}\n```\n\n---\n\n## 8. Standards Validation Endpoints\n\n### 8.1 POST /standards/validate\n**Description**: Validate calculation against standards\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Request Body**:\n```json\n{\n  \"calculation_id\": \"calc_789\",\n  \"standards\": [\"IEEE-141\", \"IEC-60364\", \"EN-50110\"],\n  \"validation_parameters\": {\n    \"strict_mode\": true,\n    \"include_recommendations\": true\n  }\n}\n```\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"validation_id\": \"val_123\",\n    \"calculation_id\": \"calc_789\",\n    \"overall_compliance\": true,\n    \"standard_results\": [\n      {\n        \"standard\": \"IEEE-141\",\n        \"compliant\": true,\n        \"message\": \"Load calculation complies with IEEE-141 standards\",\n        \"recommendations\": [\n          \"Consider using demand factors for motor loads\",\n          \"Verify diversity factors with actual load patterns\"\n        ],\n        \"violations\": []\n      },\n      {\n        \"standard\": \"IEC-60364\",\n        \"compliant\": true,\n        \"message\": \"Load calculation complies with IEC-60364 standards\",\n        \"recommendations\": [],\n        \"violations\": []\n      },\n      {\n        \"standard\": \"EN-50110\",\n        \"compliant\": false,\n        \"message\": \"Safety requirements not fully met\",\n        \"recommendations\": [\n          \"Add safety factor of 1.2 for critical loads\",\n          \"Consider redundancy for essential systems\"\n        ],\n        \"violations\": [\n          {\n            \"code\": \"EN-50110-4.2\",\n            \"description\": \"Insufficient safety margin for critical loads\",\n            \"severity\": \"warning\"\n          }\n        ]\n      }\n    ],\n    \"validation_timestamp\": \"2025-07-16T10:30:00.000Z\",\n    \"validated_by\": \"user_123\"\n  },\n  \"message\": \"Standards validation completed\"\n}\n```\n\n### 8.2 GET /standards\n**Description**: Get list of available standards\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Query Parameters**:\n- `category` (string, optional): Filter by category (electrical, safety, installation)\n- `organization` (string, optional): Filter by organization (IEEE, IEC, EN)\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"standards\": [\n      {\n        \"id\": \"ieee_141\",\n        \"code\": \"IEEE-141\",\n        \"title\": \"Recommended Practice for Electric Power Distribution\",\n        \"organization\": \"IEEE\",\n        \"version\": \"1993\",\n        \"category\": \"electrical\",\n        \"description\": \"Provides guidance for power distribution system design and analysis\",\n        \"applicable_calculations\": [\"load_calculation\", \"voltage_drop\", \"short_circuit\"],\n        \"parameters\": {\n          \"voltage_drop_limits\": {\n            \"low_voltage\": 0.05,\n            \"medium_voltage\": 0.03\n          },\n          \"diversity_factors\": {\n            \"lighting\": 0.75,\n            \"power\": 0.6,\n            \"mixed\": 0.7\n          }\n        }\n      },\n      {\n        \"id\": \"iec_60364\",\n        \"code\": \"IEC-60364\",\n        \"title\": \"Low-voltage Electrical Installations\",\n        \"organization\": \"IEC\",\n        \"version\": \"2018\",\n        \"category\": \"installation\",\n        \"description\": \"International standard for low-voltage electrical installations\",\n        \"applicable_calculations\": [\"load_calculation\", \"voltage_drop\", \"protection\"],\n        \"parameters\": {\n          \"voltage_limits\": {\n            \"normal\": 0.10,\n            \"lighting\": 0.05\n          },\n          \"protection_requirements\": {\n            \"overcurrent\": true,\n            \"earth_fault\": true,\n            \"residual_current\": true\n          }\n        }\n      }\n    ],\n    \"total\": 2\n  }\n}\n```\n\n### 8.3 GET /standards/{standard_id}\n**Description**: Get detailed standard information\n\n**Headers**: `Authorization: Bearer <token>`\n\n**Path Parameters**:\n- `standard_id` (string): Standard ID\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"ieee_141\",\n    \"code\": \"IEEE-141\",\n    \"title\": \"Recommended Practice for Electric Power Distribution\",\n    \"organization\": \"IEEE\",\n    \"version\": \"1993\",\n    \"category\": \"electrical\",\n    \"description\": \"Provides guidance for power distribution system design and analysis\",\n    \"applicable_calculations\": [\"load_calculation\", \"voltage_drop\", \"short_circuit\"],\n    \"parameters\": {\n      \"voltage_drop_limits\": {\n        \"low_voltage\": 0.05,\n        \"medium_voltage\": 0.03,\n        \"high_voltage\": 0.02\n      },\n      \"diversity_factors\": {\n        \"lighting\": 0.75,\n        \"power\": 0.6,\n        \"mixed\": 0.7,\n        \"motor\": 0.8\n      },\n      \"demand_factors\": {\n        \"continuous\": 1.0,\n        \"non_continuous\": 1.25,\n        \"intermittent\": 0.75\n      }\n    },\n    \"validation_rules\": [\n      {\n        \"rule_id\": \"voltage_drop_check\",\n        \"description\": \"Verify voltage drop is within acceptable limits\",\n        \"calculation_types\": [\"load_calculation\", \"voltage_drop\"],\n        \"parameters\": {\n          \"max_voltage_drop\": 0.05,\n          \"voltage_levels\": [\"low_voltage\"]\n        }\n      },\n      {\n        \"rule_id\": \"diversity_factor_check\",\n        \"description\": \"Verify diversity factors are appropriate for load type\",\n        \"calculation_types\": [\"load_calculation\"],\n        \"parameters\": {\n          \"recommended_factors\": {\n            \"lighting\": 0.75,\n            \"power\": 0.6,\n            \"mixed\": 0.7\n          }\n        }\n      }\n    ],\n    \"created_at\": \"2025-07-16T10:30:00.000Z\",\n    \"updated_at\": \"2025-07-16T10:30:00.000Z\"\n  }\n}\n```\n\n---\n\n## 9. Health & System Endpoints\n\n### 9.1 GET /health\n**Description**: Get system health status\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"status\": \"healthy\",\n    \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n    \"version\": \"1.0.0\",\n    \"uptime\": 3600,\n    \"checks\": {\n      \"database\": {\n        \"status\": \"healthy\",\n        \"response_time_ms\": 12\n      },\n      \"redis\": {\n        \"status\": \"healthy\",\n        \"response_time_ms\": 5\n      },\n      \"calculation_engine\": {\n        \"status\": \"healthy\",\n        \"response_time_ms\": 8\n      }\n    }\n  }\n}\n```\n\n### 9.2 GET /info\n**Description**: Get API information\n\n**Response**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"name\": \"Ultimate Electrical Designer API\",\n    \"version\": \"1.0.0\",\n    \"description\": \"Professional electrical engineering calculation and design API\",\n    \"documentation_url\": \"https://api.electrical-designer.com/docs\",\n    \"contact\": {\n      \"name\": \"API Support\",\n      \"email\": \"<EMAIL>\"\n    },\n    \"license\": {\n      \"name\": \"Commercial License\",\n      \"url\": \"https://electrical-designer.com/license\"\n    },\n    \"standards_supported\": [\n      \"IEEE-141\", \"IEEE-142\", \"IEC-60364\", \"IEC-60909\", \"EN-50110\", \"EN-60204\"\n    ],\n    \"calculation_types\": [\n      \"load_calculation\", \"voltage_drop\", \"short_circuit\", \"protection_coordination\"\n    ]\n  }\n}\n```\n\n---\n\n## 10. Error Handling\n\n### 10.1 Error Response Format\nAll error responses follow a consistent format:\n\n```json\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"ERROR_CODE\",\n    \"message\": \"Human-readable error message\",\n    \"details\": {\n      \"field\": \"specific_field\",\n      \"reason\": \"Detailed reason for the error\",\n      \"value\": \"invalid_value\"\n    }\n  },\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"request_id\": \"req_123456789\"\n}\n```\n\n### 10.2 Common Error Codes\n\n#### Authentication Errors\n- `AUTHENTICATION_REQUIRED` (401): Missing or invalid authentication token\n- `AUTHENTICATION_EXPIRED` (401): Token has expired\n- `INSUFFICIENT_PERMISSIONS` (403): User lacks required permissions\n- `ACCOUNT_DISABLED` (403): User account is disabled\n\n#### Validation Errors\n- `VALIDATION_ERROR` (400): Request validation failed\n- `INVALID_COMPONENT_ID` (400): Component ID does not exist\n- `INVALID_CALCULATION_TYPE` (400): Unsupported calculation type\n- `INVALID_STANDARDS` (400): Invalid or unsupported standards\n\n#### Resource Errors\n- `RESOURCE_NOT_FOUND` (404): Requested resource does not exist\n- `RESOURCE_ALREADY_EXISTS` (409): Resource already exists\n- `RESOURCE_LOCKED` (423): Resource is locked by another process\n\n#### Calculation Errors\n- `CALCULATION_ERROR` (422): Calculation failed due to invalid parameters\n- `STANDARDS_VIOLATION` (422): Calculation violates specified standards\n- `INSUFFICIENT_DATA` (422): Insufficient data for calculation\n\n#### System Errors\n- `INTERNAL_SERVER_ERROR` (500): Internal server error\n- `SERVICE_UNAVAILABLE` (503): Service temporarily unavailable\n- `CALCULATION_TIMEOUT` (504): Calculation timed out\n\n### 10.3 Error Response Examples\n\n#### Validation Error\n```json\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"VALIDATION_ERROR\",\n    \"message\": \"Validation failed for request data\",\n    \"details\": {\n      \"field\": \"voltage_rating\",\n      \"reason\": \"Value must be positive\",\n      \"value\": -100\n    }\n  },\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"request_id\": \"req_123456789\"\n}\n```\n\n#### Calculation Error\n```json\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"CALCULATION_ERROR\",\n    \"message\": \"Calculation failed due to invalid parameters\",\n    \"details\": {\n      \"parameter\": \"power_factor\",\n      \"reason\": \"Power factor must be between 0 and 1\",\n      \"value\": 1.5\n    }\n  },\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"request_id\": \"req_123456789\"\n}\n```\n\n#### Standards Violation\n```json\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"STANDARDS_VIOLATION\",\n    \"message\": \"Calculation violates IEEE-141 standards\",\n    \"details\": {\n      \"standard\": \"IEEE-141\",\n      \"violation\": \"voltage_drop_exceeded\",\n      \"limit\": 0.05,\n      \"actual\": 0.08,\n      \"recommendation\": \"Consider larger conductor size or shorter run length\"\n    }\n  },\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"request_id\": \"req_123456789\"\n}\n```\n\n---\n\n## 11. Rate Limiting\n\n### 11.1 Rate Limit Headers\nAll responses include rate limiting headers:\n\n```\nX-RateLimit-Limit: 1000\nX-RateLimit-Remaining: 999\nX-RateLimit-Reset: 1626441600\nX-RateLimit-Window: 3600\n```\n\n### 11.2 Rate Limit Tiers\n\n#### Standard User\n- **Requests per hour**: 1,000\n- **Calculations per hour**: 100\n- **Concurrent calculations**: 5\n\n#### Professional User\n- **Requests per hour**: 5,000\n- **Calculations per hour**: 500\n- **Concurrent calculations**: 10\n\n#### Enterprise User\n- **Requests per hour**: 10,000\n- **Calculations per hour**: 1,000\n- **Concurrent calculations**: 20\n\n### 11.3 Rate Limit Exceeded Response\n```json\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"RATE_LIMIT_EXCEEDED\",\n    \"message\": \"Rate limit exceeded. Please try again later.\",\n    \"details\": {\n      \"limit\": 1000,\n      \"window\": 3600,\n      \"reset_time\": \"2025-07-16T11:30:00.000Z\"\n    }\n  },\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"request_id\": \"req_123456789\"\n}\n```\n\n---\n\n## 12. Pagination\n\n### 12.1 Pagination Parameters\nAll list endpoints support pagination with the following parameters:\n\n- `skip` (integer, optional): Number of records to skip (default: 0)\n- `limit` (integer, optional): Maximum number of records to return (default: 100, max: 1000)\n\n### 12.2 Pagination Response\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"items\": [...],\n    \"total\": 250,\n    \"skip\": 0,\n    \"limit\": 100,\n    \"has_next\": true,\n    \"has_previous\": false\n  }\n}\n```\n\n### 12.3 Pagination Links\nResponse headers include pagination links:\n\n```\nLink: <https://api.example.com/components?skip=100&limit=100>; rel=\"next\",\n      <https://api.example.com/components?skip=200&limit=100>; rel=\"last\"\n```\n\n---\n\n## 13. Webhooks\n\n### 13.1 Webhook Events\nThe API supports webhooks for the following events:\n\n- `calculation.completed`: Calculation has finished\n- `calculation.failed`: Calculation has failed\n- `standards.updated`: New standards have been added or updated\n- `component.created`: New component has been created\n- `component.updated`: Component has been modified\n- `user.created`: New user has been registered\n\n### 13.2 Webhook Payload\n```json\n{\n  \"event\": \"calculation.completed\",\n  \"timestamp\": \"2025-07-16T10:30:00.000Z\",\n  \"data\": {\n    \"calculation_id\": \"calc_789\",\n    \"calculation_type\": \"load_calculation\",\n    \"user_id\": \"user_123\",\n    \"status\": \"completed\",\n    \"results\": {\n      \"total_load\": 26110,\n      \"compliance_status\": \"compliant\"\n    }\n  }\n}\n```\n\n### 13.3 Webhook Security\nWebhooks are secured with HMAC-SHA256 signatures:\n\n```\nX-Webhook-Signature: sha256=5d41402abc4b2a76b9719d911017c592\nX-Webhook-Timestamp: 1626441600\n```\n\n---\n\n## 14. Conclusion\n\nThis API specification provides comprehensive coverage of the Ultimate Electrical Designer application's REST API endpoints. The API is designed to support professional electrical engineering workflows with emphasis on:\n\n- **Professional Standards Compliance**: Full support for IEEE, IEC, and EN standards\n- **Comprehensive Calculation Engine**: Load, voltage drop, and short circuit calculations\n- **Type Safety**: Complete request/response validation with detailed error messages\n- **Security**: JWT authentication with role-based access control\n- **Performance**: Optimized endpoints with caching and rate limiting\n- **Documentation**: Self-documenting API with OpenAPI 3.0+ specification\n\nFor additional information, examples, or support, please refer to the interactive API documentation at `/docs` or contact the API support team.\n\n---\n\n**Document Control**\n- **Version**: 1.0\n- **Last Updated**: July 2025\n- **Next Review**: Monthly\n- **Owner**: API Development Team\n- **Approval**: Technical Lead, Product Owner, Professional Engineer