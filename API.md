# API Specifications Document
## Ultimate Electrical Designer Application

### Document Information
- **Version**: 1.0
- **Date**: July 2025
- **API Version**: v1
- **Base URL**: `/api/v1`
- **Authentication**: JWT Bearer Token
- **Standards**: OpenAPI 3.0+, RESTful Design

---

## 1. API Overview

### 1.1 Base Information
- **Protocol**: HTTPS
- **Content Type**: `application/json`
- **Character Set**: UTF-8
- **Date Format**: ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)
- **API Versioning**: URI versioning (`/api/v1/`)

### 1.2 Authentication
All protected endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt_token>
```

### 1.3 Response Format
All API responses follow a consistent structure:
```json
{
  "success": true,
    "data": {},
    "message": "Operation completed successfully",
    "timestamp": "2025-07-16T10:30:00.000Z",
    "request_id": "req_123456789"
}
```

Error responses:
```json
{
  "success": false,
    "error": {
    "code": "VALIDATION_ERROR",
      "message": "Invalid input data",
      "details": {
      "field": "voltage_rating",
        "reason": "Value must be positive"
            }
},
    "timestamp": "2025-07-16T10:30:00.000Z",
    "request_id": "req_123456789"
}
```

## 2. Authentication Endpoints

### 2.1 POST /auth/login
**Description**: Authenticate user and obtain JWT token
**Request Body**:
```json
{
  "username": "<EMAIL>",
    "password": "SecurePassword123!"
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "token_type": "bearer",
      "expires_in": 86400,
      "user": {
      "id": "user_123",
        "username": "<EMAIL>",
        "full_name": "John Doe",
        "role": "engineer",
        "permissions": ["view_components", "perform_calculations"],
        "certifications": ["PE"],
        "last_login": "2025-07-16T10:30:00.000Z"
            }
},
    "message": "Login successful"
}
```
**Error Responses**:
- `401 Unauthorized`: Invalid credentials
- `429 Too Many Requests`: Rate limit exceeded

### 2.2 POST /auth/refresh
**Description**: Refresh JWT token
**Request Body**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": 86400
}
}
```

### 2.3 POST /auth/logout
**Description**: Invalidate JWT token
**Headers**: `Authorization: Bearer <token>`
**Response**:
```json
{
  "success": true,
    "message": "Logout successful"
}
```

### 2.4 GET /auth/me
**Description**: Get current user information
**Headers**: `Authorization: Bearer <token>`
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "user_123",
      "username": "<EMAIL>",
      "full_name": "John Doe",
      "role": "engineer",
      "permissions": ["view_components", "perform_calculations"],
      "certifications": ["PE"],
      "created_at": "2025-01-01T00:00:00.000Z",
      "last_login": "2025-07-16T10:30:00.000Z"
}
}
```
---

## 3. User Management Endpoints

### 3.1 GET /users
**Description**: Get list of users (Admin only)
**Headers**: `Authorization: Bearer <token>`
**Query Parameters**:
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 100)
- `role` (string, optional): Filter by user role
- `search` (string, optional): Search by username or full name
**Response**:
```json
{
  "success": true,
    "data": {
    "users": [
      {
        "id": "user_123",
          "username": "<EMAIL>",
          "full_name": "John Doe",
          "role": "engineer",
          "is_active": true,
          "certifications": ["PE"],
          "created_at": "2025-01-01T00:00:00.000Z",
          "last_login": "2025-07-16T10:30:00.000Z"
              }
    ],
      "total": 1,
      "skip": 0,
      "limit": 100
}
}
```

### 3.2 POST /users
**Description**: Create new user (Admin only)
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "username": "<EMAIL>",
    "password": "SecurePassword123!",
    "full_name": "Jane Smith",
    "role": "engineer",
    "certifications": ["PE"]
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "user_456",
      "username": "<EMAIL>",
      "full_name": "Jane Smith",
      "role": "engineer",
      "is_active": true,
      "certifications": ["PE"],
      "created_at": "2025-07-16T10:30:00.000Z"
},
    "message": "User created successfully"
}
```

### 3.3 GET /users/{user_id}
**Description**: Get specific user information
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `user_id` (string): User ID
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "user_123",
      "username": "<EMAIL>",
      "full_name": "John Doe",
      "role": "engineer",
      "is_active": true,
      "certifications": ["PE"],
      "created_at": "2025-01-01T00:00:00.000Z",
      "last_login": "2025-07-16T10:30:00.000Z"
}
}
```

### 3.4 PUT /users/{user_id}
**Description**: Update user information
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `user_id` (string): User ID
**Request Body**:
```json
{
  "full_name": "John Updated Doe",
    "role": "senior_engineer",
    "certifications": ["PE", "PMP"]
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "user_123",
      "username": "<EMAIL>",
      "full_name": "John Updated Doe",
      "role": "senior_engineer",
      "is_active": true,
      "certifications": ["PE", "PMP"],
      "updated_at": "2025-07-16T10:30:00.000Z"
},
    "message": "User updated successfully"
}
```

### 3.5 DELETE /users/{user_id}
**Description**: Deactivate user (Admin only)
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `user_id` (string): User ID
**Response**:
```json
{
  "success": true,
    "message": "User deactivated successfully"
}
```
---

## 4. Component Management Endpoints

### 4.1 GET /components
**Description**: Get list of electrical components
**Headers**: `Authorization: Bearer <token>`
**Query Parameters**:
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 100)
- `category_id` (string, optional): Filter by category ID
- `type_id` (string, optional): Filter by component type ID
- `manufacturer` (string, optional): Filter by manufacturer
- `search` (string, optional): Search by name or description
- `voltage_min` (number, optional): Minimum voltage rating
- `voltage_max` (number, optional): Maximum voltage rating
- `current_min` (number, optional): Minimum current rating
- `current_max` (number, optional): Maximum current rating
- `standards` (string, optional): Filter by compliance standards (comma-separated)
**Response**:
```json
{
  "success": true,
    "data": {
    "components": [
      {
        "id": "comp_123",
          "name": "3-Phase Motor 10HP",
          "description": "High-efficiency 3-phase induction motor",
          "manufacturer": "ABB",
          "model_number": "M3BP-160M",
          "category": {
          "id": "cat_motors",
            "name": "Motors",
            "code": "MOT"
                },
          "type": {
          "id": "type_induction",
            "name": "Induction Motor",
            "code": "IND"
                },
          "specifications": {
          "voltage_rating": 460,
            "current_rating": 14.2,
            "power_rating": 7460,
            "power_factor": 0.85,
            "efficiency": 0.92,
            "frequency": 60,
            "poles": 4,
            "rpm": 1750
        },
          "compliance_standards": ["iEEE-112", "iEC-60034", "eN-60034"],
          "created_at": "2025-07-16T10:30:00.000Z",
          "updated_at": "2025-07-16T10:30:00.000Z"
              }
    ],
      "total": 1,
      "skip": 0,
      "limit": 100
}
}
```

### 4.2 POST /components
**Description**: Create new electrical component
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "name": "3-Phase Motor 15HP",
    "description": "High-efficiency 3-phase induction motor",
    "manufacturer": "Siemens",
    "model_number": "1LA7-180M",
    "category_id": "cat_motors",
    "type_id": "type_induction",
    "specifications": {
    "voltage_rating": 460,
      "current_rating": 21.3,
      "power_rating": 11190,
      "power_factor": 0.87,
      "efficiency": 0.93,
      "frequency": 60,
      "poles": 4,
      "rpm": 1750
},
    "compliance_standards": ["iEEE-112", "iEC-60034", "eN-60034"]
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "comp_456",
      "name": "3-Phase Motor 15HP",
      "description": "High-efficiency 3-phase induction motor",
      "manufacturer": "Siemens",
      "model_number": "1LA7-180M",
      "category_id": "cat_motors",
      "type_id": "type_induction",
      "specifications": {
      "voltage_rating": 460,
        "current_rating": 21.3,
        "power_rating": 11190,
        "power_factor": 0.87,
        "efficiency": 0.93,
        "frequency": 60,
        "poles": 4,
        "rpm": 1750
    },
      "compliance_standards": ["iEEE-112", "iEC-60034", "eN-60034"],
      "created_at": "2025-07-16T10:30:00.000Z",
      "updated_at": "2025-07-16T10:30:00.000Z"
},
    "message": "Component created successfully"
}
```

### 4.3 GET /components/{component_id}
**Description**: Get specific electrical component
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `component_id` (string): Component ID
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "comp_123",
      "name": "3-Phase Motor 10HP",
      "description": "High-efficiency 3-phase induction motor",
      "manufacturer": "ABB",
      "model_number": "M3BP-160M",
      "category": {
      "id": "cat_motors",
        "name": "Motors",
        "code": "MOT",
        "description": "Electric motors and drives"
            },
      "type": {
      "id": "type_induction",
        "name": "Induction Motor",
        "code": "IND",
        "description": "Three-phase induction motors"
            },
      "specifications": {
      "voltage_rating": 460,
        "current_rating": 14.2,
        "power_rating": 7460,
        "power_factor": 0.85,
        "efficiency": 0.92,
        "frequency": 60,
        "poles": 4,
        "rpm": 1750,
        "service_factor": 1.15,
        "insulation_class": "F",
        "protection_class": "IP55"
            },
      "compliance_standards": ["iEEE-112", "iEC-60034", "eN-60034"],
      "created_at": "2025-07-16T10:30:00.000Z",
      "updated_at": "2025-07-16T10:30:00.000Z"
}
}
```

### 4.4 PUT /components/{component_id}
**Description**: Update electrical component
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `component_id` (string): Component ID
**Request Body**:
```json
{
  "name": "3-Phase Motor 10HP Updated",
    "description": "High-efficiency 3-phase induction motor with updated specifications",
    "specifications": {
    "voltage_rating": 460,
      "current_rating": 14.2,
      "power_rating": 7460,
      "power_factor": 0.87,
      "efficiency": 0.94,
      "frequency": 60,
      "poles": 4,
      "rpm": 1750,
      "service_factor": 1.15,
      "insulation_class": "F",
      "protection_class": "IP55"
}
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "comp_123",
      "name": "3-Phase Motor 10HP Updated",
      "description": "High-efficiency 3-phase induction motor with updated specifications",
      "manufacturer": "ABB",
      "model_number": "M3BP-160M",
      "category_id": "cat_motors",
      "type_id": "type_induction",
      "specifications": {
      "voltage_rating": 460,
        "current_rating": 14.2,
        "power_rating": 7460,
        "power_factor": 0.87,
        "efficiency": 0.94,
        "frequency": 60,
        "poles": 4,
        "rpm": 1750,
        "service_factor": 1.15,
        "insulation_class": "F",
        "protection_class": "IP55"
            },
      "compliance_standards": ["iEEE-112", "iEC-60034", "eN-60034"],
      "updated_at": "2025-07-16T10:30:00.000Z"
},
    "message": "Component updated successfully"
}
```

### 4.5 DELETE /components/{component_id}
**Description**: Delete electrical component
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `component_id` (string): Component ID
**Response**:
```json
{
  "success": true,
    "message": "Component deleted successfully"
}
```
---

## 5. Component Categories Endpoints

### 5.1 GET /component-categories
**Description**: Get list of component categories
**Headers**: `Authorization: Bearer <token>`
**Query Parameters**:
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 100)
- `parent_id` (string, optional): Filter by parent category ID
- `search` (string, optional): Search by name or description
**Response**:
```json
{
  "success": true,
    "data": {
    "categories": [
      {
        "id": "cat_motors",
          "name": "Motors",
          "description": "Electric motors and drives",
          "category_code": "MOT",
          "parent_id": null,
          "specifications_template": {
          "voltage_rating": {"type": "number", "unit": "V", "required": true},
            "current_rating": {"type": "number", "unit": "A", "required": true},
            "power_rating": {"type": "number", "unit": "W", "required": true},
            "power_factor": {"type": "number", "unit": "", "required": true},
            "efficiency": {"type": "number", "unit": "", "required": true},
            "frequency": {"type": "number", "unit": "Hz", "required": true}
        },
          "children": [],
          "created_at": "2025-07-16T10:30:00.000Z",
          "updated_at": "2025-07-16T10:30:00.000Z"
              }
    ],
      "total": 1,
      "skip": 0,
      "limit": 100
}
}
```

### 5.2 POST /component-categories
**Description**: Create new component category
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "name": "Transformers",
    "description": "Power and distribution transformers",
    "category_code": "TRF",
    "parent_id": null,
    "specifications_template": {
    "voltage_primary": {"type": "number", "unit": "V", "required": true},
      "voltage_secondary": {"type": "number", "unit": "V", "required": true},
      "power_rating": {"type": "number", "unit": "VA", "required": true},
      "frequency": {"type": "number", "unit": "Hz", "required": true},
      "efficiency": {"type": "number", "unit": "", "required": true}
}
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "cat_transformers",
      "name": "Transformers",
      "description": "Power and distribution transformers",
      "category_code": "TRF",
      "parent_id": null,
      "specifications_template": {
      "voltage_primary": {"type": "number", "unit": "V", "required": true},
        "voltage_secondary": {"type": "number", "unit": "V", "required": true},
        "power_rating": {"type": "number", "unit": "VA", "required": true},
        "frequency": {"type": "number", "unit": "Hz", "required": true},
        "efficiency": {"type": "number", "unit": "", "required": true}
    },
      "created_at": "2025-07-16T10:30:00.000Z",
      "updated_at": "2025-07-16T10:30:00.000Z"
},
    "message": "Category created successfully"
}
```

### 5.3 GET /component-categories/{category_id}
**Description**: Get specific component category
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `category_id` (string): Category ID
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "cat_motors",
      "name": "Motors",
      "description": "Electric motors and drives",
      "category_code": "MOT",
      "parent_id": null,
      "specifications_template": {
      "voltage_rating": {"type": "number", "unit": "V", "required": true},
        "current_rating": {"type": "number", "unit": "A", "required": true},
        "power_rating": {"type": "number", "unit": "W", "required": true},
        "power_factor": {"type": "number", "unit": "", "required": true},
        "efficiency": {"type": "number", "unit": "", "required": true},
        "frequency": {"type": "number", "unit": "Hz", "required": true}
    },
      "children": [
      {
        "id": "cat_induction_motors",
          "name": "Induction Motors",
          "description": "Three-phase induction motors",
          "category_code": "IND"
              }
    ],
      "parent": null,
      "created_at": "2025-07-16T10:30:00.000Z",
      "updated_at": "2025-07-16T10:30:00.000Z"
}
}
```

### 5.4 GET /component-categories/{category_id}/tree
**Description**: Get category tree structure
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `category_id` (string): Category ID
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "cat_motors",
      "name": "Motors",
      "category_code": "MOT",
      "children": [
      {
        "id": "cat_induction_motors",
          "name": "Induction Motors",
          "category_code": "IND",
          "children": [
          {
            "id": "cat_low_voltage_motors",
              "name": "Low Voltage Motors",
              "category_code": "LVM",
              "children": []
          }
        ]
      },
        {
        "id": "cat_synchronous_motors",
          "name": "Synchronous Motors",
          "category_code": "SYN",
          "children": []
      }
    ]
}
}
```
---

## 6. Component Types Endpoints

### 6.1 GET /component-types
**Description**: Get list of component types
**Headers**: `Authorization: Bearer <token>`
**Query Parameters**:
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 100)
- `category_id` (string, optional): Filter by category ID
- `search` (string, optional): Search by name or description
**Response**:
```json
{
  "success": true,
    "data": {
    "types": [
      {
        "id": "type_induction",
          "name": "Induction Motor",
          "description": "Three-phase induction motors",
          "type_code": "IND",
          "category_id": "cat_motors",
          "category": {
          "id": "cat_motors",
            "name": "Motors",
            "code": "MOT"
                },
          "specifications_template": {
          "voltage_rating": {"type": "number", "unit": "V", "required": true},
            "current_rating": {"type": "number", "unit": "A", "required": true},
            "power_rating": {"type": "number", "unit": "W", "required": true},
            "power_factor": {"type": "number", "unit": "", "required": true},
            "efficiency": {"type": "number", "unit": "", "required": true},
            "frequency": {"type": "number", "unit": "Hz", "required": true},
            "poles": {"type": "integer", "unit": "", "required": true},
            "rpm": {"type": "number", "unit": "rpm", "required": true}
        },
          "default_specifications": {
          "frequency": 60,
            "poles": 4,
            "power_factor": 0.85,
            "efficiency": 0.90
        },
          "created_at": "2025-07-16T10:30:00.000Z",
          "updated_at": "2025-07-16T10:30:00.000Z"
              }
    ],
      "total": 1,
      "skip": 0,
      "limit": 100
}
}
```

### 6.2 POST /component-types
**Description**: Create new component type
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "name": "Synchronous Motor",
    "description": "Three-phase synchronous motors",
    "type_code": "SYN",
    "category_id": "cat_motors",
    "specifications_template": {
    "voltage_rating": {"type": "number", "unit": "V", "required": true},
      "current_rating": {"type": "number", "unit": "A", "required": true},
      "power_rating": {"type": "number", "unit": "W", "required": true},
      "power_factor": {"type": "number", "unit": "", "required": true},
      "efficiency": {"type": "number", "unit": "", "required": true},
      "frequency": {"type": "number", "unit": "Hz", "required": true},
      "poles": {"type": "integer", "unit": "", "required": true},
      "rpm": {"type": "number", "unit": "rpm", "required": true},
      "excitation_type": {"type": "string", "unit": "", "required": true}
},
    "default_specifications": {
    "frequency": 60,
      "poles": 4,
      "power_factor": 0.95,
      "efficiency": 0.95,
      "excitation_type": "brushless"
}
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "type_synchronous",
      "name": "Synchronous Motor",
      "description": "Three-phase synchronous motors",
      "type_code": "SYN",
      "category_id": "cat_motors",
      "specifications_template": {
      "voltage_rating": {"type": "number", "unit": "V", "required": true},
        "current_rating": {"type": "number", "unit": "A", "required": true},
        "power_rating": {"type": "number", "unit": "W", "required": true},
        "power_factor": {"type": "number", "unit": "", "required": true},
        "efficiency": {"type": "number", "unit": "", "required": true},
        "frequency": {"type": "number", "unit": "Hz", "required": true},
        "poles": {"type": "integer", "unit": "", "required": true},
        "rpm": {"type": "number", "unit": "rpm", "required": true},
        "excitation_type": {"type": "string", "unit": "", "required": true}
    },
      "default_specifications": {
      "frequency": 60,
        "poles": 4,
        "power_factor": 0.95,
        "efficiency": 0.95,
        "excitation_type": "brushless"
            },
      "created_at": "2025-07-16T10:30:00.000Z",
      "updated_at": "2025-07-16T10:30:00.000Z"
},
    "message": "Component type created successfully"
}
```

### 6.3 GET /component-types/{type_id}
**Description**: Get specific component type
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `type_id` (string): Component type ID
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "type_induction",
      "name": "Induction Motor",
      "description": "Three-phase induction motors",
      "type_code": "IND",
      "category_id": "cat_motors",
      "category": {
      "id": "cat_motors",
        "name": "Motors",
        "code": "MOT",
        "description": "Electric motors and drives"
            },
      "specifications_template": {
      "voltage_rating": {"type": "number", "unit": "V", "required": true},
        "current_rating": {"type": "number", "unit": "A", "required": true},
        "power_rating": {"type": "number", "unit": "W", "required": true},
        "power_factor": {"type": "number", "unit": "", "required": true},
        "efficiency": {"type": "number", "unit": "", "required": true},
        "frequency": {"type": "number", "unit": "Hz", "required": true},
        "poles": {"type": "integer", "unit": "", "required": true},
        "rpm": {"type": "number", "unit": "rpm", "required": true}
    },
      "default_specifications": {
      "frequency": 60,
        "poles": 4,
        "power_factor": 0.85,
        "efficiency": 0.90
    },
      "created_at": "2025-07-16T10:30:00.000Z",
      "updated_at": "2025-07-16T10:30:00.000Z"
}
}
```

### 6.4 GET /component-types/by-category/{category_id}
**Description**: Get component types by category
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `category_id` (string): Category ID
**Response**:
```json
{
  "success": true,
    "data": {
    "types": [
      {
        "id": "type_induction",
          "name": "Induction Motor",
          "type_code": "IND",
          "description": "Three-phase induction motors"
              },
        {
        "id": "type_synchronous",
          "name": "Synchronous Motor",
          "type_code": "SYN",
          "description": "Three-phase synchronous motors"
              }
    ],
      "category": {
      "id": "cat_motors",
        "name": "Motors",
        "code": "MOT"
            }
}
}
```
---


## 7. Electrical Calculations Endpoints

### 7.1 POST /calculations/load
**Description**: Perform load calculation
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "calculation_type": "load_calculation",
    "components": [
    {
      "component_id": "comp_123",
        "quantity": 2,
        "diversity_factor": 0.8,
        "demand_factor": 0.9
    },
      {
      "component_id": "comp_456",
        "quantity": 1,
        "diversity_factor": 1.0,
        "demand_factor": 1.0
    }
  ],
    "parameters": {
    "voltage_level": "low_voltage",
      "power_factor": 0.85,
      "temperature": 25,
      "altitude": 0,
      "installation_method": "cable_tray",
      "standards": ["iEEE-141", "iEC-60364"]
}
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "calculation_id": "calc_789",
      "calculation_type": "load_calculation",
      "results": {
      "total_load": 26110,
        "connected_load": 29800,
        "demand_load": 26110,
        "current_load": 56.76,
        "power_factor": 0.86,
        "component_loads": [
        {
          "component_id": "comp_123",
            "component_name": "3-Phase Motor 10HP",
            "quantity": 2,
            "individual_load": 7460,
            "total_load": 11936,
            "diversity_factor": 0.8,
            "demand_factor": 0.9
        },
          {
          "component_id": "comp_456",
            "component_name": "3-Phase Motor 15HP",
            "quantity": 1,
            "individual_load": 11190,
            "total_load": 11190,
            "diversity_factor": 1.0,
            "demand_factor": 1.0
        }
      ]
    },
      "compliance_status": {
      "overall_compliance": true,
        "standard_results": [
        {
          "standard": "IEEE-141",
            "compliant": true,
            "message": "Load calculation complies with IEEE-141 standards",
            "recommendations": []
        },
          {
          "standard": "IEC-60364",
            "compliant": true,
            "message": "Load calculation complies with IEC-60364 standards",
            "recommendations": []
        }
      ]
    },
      "parameters": {
      "voltage_level": "low_voltage",
        "power_factor": 0.85,
        "temperature": 25,
        "altitude": 0,
        "installation_method": "cable_tray",
        "standards": ["iEEE-141", "iEC-60364"]
    },
      "calculation_time_ms": 156,
      "calculated_by": "user_123",
      "calculation_timestamp": "2025-07-16T10:30:00.000Z"
},
    "message": "Load calculation completed successfully"
}
```

### 7.2 POST /calculations/voltage-drop
**Description**: Perform voltage drop calculation
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "calculation_type": "voltage_drop",
    "cable_parameters": {
    "conductor_material": "copper",
      "conductor_size": "12_AWG",
      "cable_length": 100,
      "installation_method": "conduit",
      "temperature": 30,
      "number_of_conductors": 3
},
    "electrical_parameters": {
    "voltage": 480,
      "current": 25,
      "power_factor": 0.85,
      "frequency": 60
},
    "standards": ["iEEE-141", "iEC-60364"]
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "calculation_id": "calc_790",
      "calculation_type": "voltage_drop",
      "results": {
      "voltage_drop": 8.7,
        "voltage_drop_percent": 1.81,
        "final_voltage": 471.3,
        "resistance_per_unit": 0.00193,
        "reactance_per_unit": 0.00041,
        "impedance_per_unit": 0.00198,
        "power_loss": 543.75,
        "efficiency": 97.8
    },
      "cable_parameters": {
      "conductor_material": "copper",
        "conductor_size": "12_AWG",
        "cable_length": 100,
        "installation_method": "conduit",
        "temperature": 30,
        "number_of_conductors": 3,
        "derating_factor": 1.0
    },
      "electrical_parameters": {
      "voltage": 480,
        "current": 25,
        "power_factor": 0.85,
        "frequency": 60
    },
      "compliance_status": {
      "overall_compliance": true,
        "standard_results": [
        {
          "standard": "IEEE-141",
            "compliant": true,
            "message": "Voltage drop 1.81% is within IEEE-141 limit of 5%",
            "recommendations": []
        },
          {
          "standard": "IEC-60364",
            "compliant": true,
            "message": "Voltage drop complies with IEC-60364 standards",
            "recommendations": []
        }
      ]
    },
      "calculation_time_ms": 89,
      "calculated_by": "user_123",
      "calculation_timestamp": "2025-07-16T10:30:00.000Z"
},
    "message": "Voltage drop calculation completed successfully"
}
```

### 7.3 POST /calculations/short-circuit
**Description**: Perform short circuit calculation
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "calculation_type": "short_circuit",
    "system_parameters": {
    "voltage": 480,
      "frequency": 60,
      "system_impedance": 0.05,
      "transformer_rating": 1000,
      "transformer_impedance": 0.04
},
    "fault_parameters": {
    "fault_type": "three_phase",
      "fault_location": "load_bus",
      "cable_impedance": 0.02,
      "arc_resistance": 0.0
},
    "standards": ["iEEE-141", "iEC-60909"]
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "calculation_id": "calc_791",
      "calculation_type": "short_circuit",
      "results": {
      "three_phase_fault_current": 4823,
        "single_phase_fault_current": 4156,
        "line_to_line_fault_current": 4177,
        "fault_mva": 4.01,
        "x_over_r_ratio": 12.5,
        "dc_component": 0.85,
        "total_fault_current": 5473,
        "fault_duration": 0.083
    },
      "system_parameters": {
      "voltage": 480,
        "frequency": 60,
        "system_impedance": 0.05,
        "transformer_rating": 1000,
        "transformer_impedance": 0.04
    },
      "fault_parameters": {
      "fault_type": "three_phase",
        "fault_location": "load_bus",
        "cable_impedance": 0.02,
        "arc_resistance": 0.0
    },
      "protective_device_coordination": {
      "upstream_device": "600A_breaker",
        "downstream_device": "100A_breaker",
        "selectivity": true,
        "coordination_time": 0.2
    },
      "compliance_status": {
      "overall_compliance": true,
        "standard_results": [
        {
          "standard": "IEEE-141",
            "compliant": true,
            "message": "Short circuit calculation complies with IEEE-141 standards",
            "recommendations": []
        },
          {
          "standard": "IEC-60909",
            "compliant": true,
            "message": "Short circuit calculation complies with IEC-60909 standards",
            "recommendations": []
        }
      ]
    },
      "calculation_time_ms": 234,
      "calculated_by": "user_123",
      "calculation_timestamp": "2025-07-16T10:30:00.000Z"
},
    "message": "Short circuit calculation completed successfully"
}
```

### 7.4 GET /calculations
**Description**: Get list of calculations
**Headers**: `Authorization: Bearer <token>`
**Query Parameters**:
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 100)
- `calculation_type` (string, optional): Filter by calculation type
- `calculated_by` (string, optional): Filter by user ID
- `start_date` (string, optional): Filter by start date (ISO 8601)
- `end_date` (string, optional): Filter by end date (ISO 8601)
**Response**:
```json
{
  "success": true,
    "data": {
    "calculations": [
      {
        "id": "calc_789",
          "calculation_type": "load_calculation",
          "summary": {
          "total_load": 26110,
            "components_count": 2,
            "compliance_status": "compliant"
                },
          "calculated_by": "user_123",
          "calculated_by_name": "John Doe",
          "calculation_timestamp": "2025-07-16T10:30:00.000Z",
          "calculation_time_ms": 156
      },
        {
        "id": "calc_790",
          "calculation_type": "voltage_drop",
          "summary": {
          "voltage_drop_percent": 1.81,
            "compliance_status": "compliant"
                },
          "calculated_by": "user_123",
          "calculated_by_name": "John Doe",
          "calculation_timestamp": "2025-07-16T10:25:00.000Z",
          "calculation_time_ms": 89
      }
    ],
      "total": 2,
      "skip": 0,
      "limit": 100
}
}
```

### 7.5 GET /calculations/{calculation_id}
**Description**: Get specific calculation result
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `calculation_id` (string): Calculation ID
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "calc_789",
      "calculation_type": "load_calculation",
      "results": {
      "total_load": 26110,
        "connected_load": 29800,
        "demand_load": 26110,
        "current_load": 56.76,
        "power_factor": 0.86,
        "component_loads": [
        {
          "component_id": "comp_123",
            "component_name": "3-Phase Motor 10HP",
            "quantity": 2,
            "individual_load": 7460,
            "total_load": 11936,
            "diversity_factor": 0.8,
            "demand_factor": 0.9
        }
      ]
    },
      "compliance_status": {
      "overall_compliance": true,
        "standard_results": [
        {
          "standard": "IEEE-141",
            "compliant": true,
            "message": "Load calculation complies with IEEE-141 standards",
            "recommendations": []
        }
      ]
    },
      "parameters": {
      "voltage_level": "low_voltage",
        "power_factor": 0.85,
        "temperature": 25,
        "altitude": 0,
        "installation_method": "cable_tray",
        "standards": ["iEEE-141", "iEC-60364"]
    },
      "calculation_time_ms": 156,
      "calculated_by": "user_123",
      "calculated_by_name": "John Doe",
      "calculation_timestamp": "2025-07-16T10:30:00.000Z"
}
}
```

### 7.6 DELETE /calculations/{calculation_id}
**Description**: Delete calculation result
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `calculation_id` (string): Calculation ID
**Response**:
```json
{
  "success": true,
    "message": "Calculation deleted successfully"
}
```
---


## 8. Standards Validation Endpoints

### 8.1 POST /standards/validate
**Description**: Validate calculation against standards
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "calculation_id": "calc_789",
    "standards": ["iEEE-141", "iEC-60364", "eN-50110"],
    "validation_parameters": {
    "strict_mode": true,
      "include_recommendations": true
}
}
```
**Response**:
```json
{
  "success": true,
    "data": {
    "validation_id": "val_123",
      "calculation_id": "calc_789",
      "overall_compliance": true,
      "standard_results": [
      {
        "standard": "IEEE-141",
          "compliant": true,
          "message": "Load calculation complies with IEEE-141 standards",
          "recommendations": [
          "consider using demand factors for motor loads",
            "Verify diversity factors with actual load patterns"
                ],
          "violations": []
      },
        {
        "standard": "IEC-60364",
          "compliant": true,
          "message": "Load calculation complies with IEC-60364 standards",
          "recommendations": [],
          "violations": []
      },
        {
        "standard": "EN-50110",
          "compliant": false,
          "message": "Safety requirements not fully met",
          "recommendations": [
          "Add safety factor of 1.2 for critical loads",
            "consider redundancy for essential systems"
                ],
          "violations": [
          {
            "code": "EN-50110-4.2",
              "description": "Insufficient safety margin for critical loads",
              "severity": "warning"
                  }
        ]
      }
    ],
      "validation_timestamp": "2025-07-16T10:30:00.000Z",
      "validated_by": "user_123"
},
    "message": "Standards validation completed"
}
```

### 8.2 GET /standards
**Description**: Get list of available standards
**Headers**: `Authorization: Bearer <token>`
**Query Parameters**:
- `category` (string, optional): Filter by category (electrical, safety, installation)
- `organization` (string, optional): Filter by organization (IEEE, IEC, EN)
**Response**:
```json
{
  "success": true,
    "data": {
    "standards": [
      {
        "id": "ieee_141",
          "code": "IEEE-141",
          "title": "Recommended Practice for Electric Power Distribution",
          "organization": "IEEE",
          "version": "1993",
          "category": "electrical",
          "description": "Provides guidance for power distribution system design and analysis",
          "applicable_calculations": ["load_calculation", "voltage_drop", "short_circuit"],
          "parameters": {
          "voltage_drop_limits": {
            "low_voltage": 0.05,
              "medium_voltage": 0.03
          },
            "diversity_factors": {
            "lighting": 0.75,
              "power": 0.6,
              "mixed": 0.7
          }
        }
      },
        {
        "id": "iec_60364",
          "code": "IEC-60364",
          "title": "Low-voltage Electrical Installations",
          "organization": "IEC",
          "version": "2018",
          "category": "installation",
          "description": "International standard for low-voltage electrical installations",
          "applicable_calculations": ["load_calculation", "voltage_drop", "protection"],
          "parameters": {
          "voltage_limits": {
            "normal": 0.10,
              "lighting": 0.05
          },
            "protection_requirements": {
            "overcurrent": true,
              "earth_fault": true,
              "residual_current": true
          }
        }
      }
    ],
      "total": 2
}
}
```

### 8.3 GET /standards/{standard_id}
**Description**: Get detailed standard information
**Headers**: `Authorization: Bearer <token>`
**Path Parameters**:
- `standard_id` (string): Standard ID
**Response**:
```json
{
  "success": true,
    "data": {
    "id": "ieee_141",
      "code": "IEEE-141",
      "title": "Recommended Practice for Electric Power Distribution",
      "organization": "IEEE",
      "version": "1993",
      "category": "electrical",
      "description": "Provides guidance for power distribution system design and analysis",
      "applicable_calculations": ["load_calculation", "voltage_drop", "short_circuit"],
      "parameters": {
      "voltage_drop_limits": {
        "low_voltage": 0.05,
          "medium_voltage": 0.03,
          "high_voltage": 0.02
      },
        "diversity_factors": {
        "lighting": 0.75,
          "power": 0.6,
          "mixed": 0.7,
          "motor": 0.8
      },
        "demand_factors": {
        "continuous": 1.0,
          "non_continuous": 1.25,
          "intermittent": 0.75
      }
    },
      "validation_rules": [
      {
        "rule_id": "voltage_drop_check",
          "description": "Verify voltage drop is within acceptable limits",
          "calculation_types": ["load_calculation", "voltage_drop"],
          "parameters": {
          "max_voltage_drop": 0.05,
            "voltage_levels": ["low_voltage"]
        }
      },
        {
        "rule_id": "diversity_factor_check",
          "description": "Verify diversity factors are appropriate for load type",
          "calculation_types": ["load_calculation"],
          "parameters": {
          "recommended_factors": {
            "lighting": 0.75,
              "power": 0.6,
              "mixed": 0.7
          }
        }
      }
    ],
      "created_at": "2025-07-16T10:30:00.000Z",
      "updated_at": "2025-07-16T10:30:00.000Z"
}
}
```
---


## 9. Health & System Endpoints

### 9.1 GET /health
**Description**: Get system health status
**Response**:
```json
{
  "success": true,
    "data": {
    "status": "healthy",
      "timestamp": "2025-07-16T10:30:00.000Z",
      "version": "1.0.0",
      "uptime": 3600,
      "checks": {
      "database": {
        "status": "healthy",
          "response_time_ms": 12
      },
        "redis": {
        "status": "healthy",
          "response_time_ms": 5
      },
        "calculation_engine": {
        "status": "healthy",
          "response_time_ms": 8
      }
    }
}
}
```

### 9.2 GET /info
**Description**: Get API information
**Response**:
```json
{
  "success": true,
    "data": {
    "name": "Ultimate Electrical Designer API",
      "version": "1.0.0",
      "description": "Professional electrical engineering calculation and design API",
      "documentation_url": "https://api.electrical-designer.com/docs",
      "contact": {
      "name": "API Support",
        "email": "<EMAIL>"
            },
      "license": {
      "name": "Commercial License",
        "url": "https://electrical-designer.com/license"
            },
      "standards_supported": [
      "iEEE-141", "iEEE-142", "iEC-60364", "iEC-60909", "eN-50110", "eN-60204"
            ],
      "calculation_types": [
      "load_calculation", "voltage_drop", "short_circuit", "protection_coordination"
            ]
}
}
```
---


## 10. Error Handling

### 10.1 Error Response Format
All error responses follow a consistent format:
```json
{
  "success": false,
    "error": {
    "code": "ERROR_CODE",
      "message": "Human-readable error message",
      "details": {
      "field": "specific_field",
        "reason": "Detailed reason for the error",
        "value": "invalid_value"
            }
},
    "timestamp": "2025-07-16T10:30:00.000Z",
    "request_id": "req_123456789"
}
```

### 10.2 Common Error Codes

#### Authentication Errors
- `AUTHENTICATION_REQUIRED` (401): Missing or invalid authentication token
- `AUTHENTICATION_EXPIRED` (401): Token has expired
- `INSUFFICIENT_PERMISSIONS` (403): User lacks required permissions
- `ACCOUNT_DISABLED` (403): User account is disabled

#### Validation Errors
- `VALIDATION_ERROR` (400): Request validation failed
- `INVALID_COMPONENT_ID` (400): Component ID does not exist
- `INVALID_CALCULATION_TYPE` (400): Unsupported calculation type
- `INVALID_STANDARDS` (400): Invalid or unsupported standards

#### Resource Errors
- `RESOURCE_NOT_FOUND` (404): Requested resource does not exist
- `RESOURCE_ALREADY_EXISTS` (409): Resource already exists
- `RESOURCE_LOCKED` (423): Resource is locked by another process

#### Calculation Errors
- `CALCULATION_ERROR` (422): Calculation failed due to invalid parameters
- `STANDARDS_VIOLATION` (422): Calculation violates specified standards
- `INSUFFICIENT_DATA` (422): Insufficient data for calculation

#### System Errors
- `INTERNAL_SERVER_ERROR` (500): Internal server error
- `SERVICE_UNAVAILABLE` (503): Service temporarily unavailable
- `CALCULATION_TIMEOUT` (504): Calculation timed out

### 10.3 Error Response Examples

#### Validation Error
```json
{
  "success": false,
    "error": {
    "code": "VALIDATION_ERROR",
      "message": "Validation failed for request data",
      "details": {
      "field": "voltage_rating",
        "reason": "Value must be positive",
        "value": -100
    }
},
    "timestamp": "2025-07-16T10:30:00.000Z",
    "request_id": "req_123456789"
}
```

#### Calculation Error
```json
{
  "success": false,
    "error": {
    "code": "CALCULATION_ERROR",
      "message": "Calculation failed due to invalid parameters",
      "details": {
      "parameter": "power_factor",
        "reason": "Power factor must be between 0 and 1",
        "value": 1.5
    }
},
    "timestamp": "2025-07-16T10:30:00.000Z",
    "request_id": "req_123456789"
}
```

#### Standards Violation
```json
{
  "success": false,
    "error": {
    "code": "STANDARDS_VIOLATION",
      "message": "Calculation violates IEEE-141 standards",
      "details": {
      "standard": "IEEE-141",
        "violation": "voltage_drop_exceeded",
        "limit": 0.05,
        "actual": 0.08,
        "recommendation": "Consider larger conductor size or shorter run length"
            }
},
    "timestamp": "2025-07-16T10:30:00.000Z",
    "request_id": "req_123456789"
}
```
---

## 11. Rate Limiting

### 11.1 Rate Limit Headers
All responses include rate limiting headers:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1626441600
X-RateLimit-Window: 3600
```

### 11.2 Rate Limit Tiers

#### Standard User
- **Requests per hour**: 1,000
- **Calculations per hour**: 100
- **Concurrent calculations**: 5

#### Professional User
- **Requests per hour**: 5,000
- **Calculations per hour**: 500
- **Concurrent calculations**: 10

#### Enterprise User
- **Requests per hour**: 10,000
- **Calculations per hour**: 1,000
- **Concurrent calculations**: 20

### 11.3 Rate Limit Exceeded Response
```json
{
  "success": false,
    "error": {
    "code": "RATE_LIMIT_EXCEEDED",
      "message": "Rate limit exceeded. Please try again later.",
      "details": {
      "limit": 1000,
        "window": 3600,
        "reset_time": "2025-07-16T11:30:00.000Z"
            }
},
    "timestamp": "2025-07-16T10:30:00.000Z",
    "request_id": "req_123456789"
}
```
---

## 12. Pagination

### 12.1 Pagination Parameters
All list endpoints support pagination with the following parameters:
- `skip` (integer, optional): Number of records to skip (default: 0)
- `limit` (integer, optional): Maximum number of records to return (default: 100, max: 1000)

### 12.2 Pagination Response
```json
{
  "success": true,
    "data": {
    "items": [...],
      "total": 250,
      "skip": 0,
      "limit": 100,
      "has_next": true,
      "has_previous": false
}
}
```

### 12.3 Pagination Links
Response headers include pagination links:
```
Link: <https://api.example.com/components?skip=100&limit=100>; rel="next",
        <https://api.example.com/components?skip=200&limit=100>; rel="last"
```
---

## 13. Webhooks

### 13.1 Webhook Events
The API supports webhooks for the following events:
- `calculation.completed`: Calculation has finished
- `calculation.failed`: Calculation has failed
- `standards.updated`: New standards have been added or updated
- `component.created`: New component has been created
- `component.updated`: Component has been modified
- `user.created`: New user has been registered

### 13.2 Webhook Payload
```json
{
  "event": "calculation.completed",
    "timestamp": "2025-07-16T10:30:00.000Z",
    "data": {
    "calculation_id": "calc_789",
      "calculation_type": "load_calculation",
      "user_id": "user_123",
      "status": "completed",
      "results": {
      "total_load": 26110,
        "compliance_status": "compliant"
            }
}
}
```

### 13.3 Webhook Security
Webhooks are secured with HMAC-SHA256 signatures:
```
X-Webhook-Signature: sha256=5d41402abc4b2a76b9719d911017c592
X-Webhook-Timestamp: 1626441600
```
---


## 14. Conclusion
This API specification provides comprehensive coverage of the Ultimate Electrical Designer application's REST API endpoints. The API is designed to support professional electrical engineering workflows with emphasis on:
- **Professional Standards Compliance**: Full support for IEEE, IEC, and EN standards
- **Comprehensive Calculation Engine**: Load, voltage drop, and short circuit calculations
- **Type Safety**: Complete request/response validation with detailed error messages
- **Security**: JWT authentication with role-based access control
- **Performance**: Optimized endpoints with caching and rate limiting
- **Documentation**: Self-documenting API with OpenAPI 3.0+ specification

For additional information, examples, or support, please refer to the interactive API documentation at `/docs` or contact the API support team.

---
**Document Control**
- **Version**: 1.0
- **Last Updated**: July 2025
- **Next Review**: Monthly
- **Owner**: API Development Team
- **Approval**: Technical Lead, Product Owner, Professional Engineer