{"openapi": "3.1.0", "info": {"title": "Ultimate Electrical Designer", "description": "An engineering application for industrial electrical design, calculations, and management.", "version": "1.0.0"}, "paths": {"/api/v1/health/": {"get": {"tags": ["API v1", "Health Check", "Health Check"], "summary": "Simple Health Check", "description": "Basic health check endpoint for load balancers and monitoring systems", "operationId": "simpleHealthCheck", "responses": {"200": {"description": "System is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleHealthResponseSchema"}}}}, "500": {"description": "Internal server error"}, "503": {"description": "System is unhealthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleHealthResponseSchema"}}}}}}}, "/api/v1/health/detailed": {"get": {"tags": ["API v1", "Health Check", "Health Check"], "summary": "Comprehensive Health Check", "description": "Detailed health check with system metrics, database status, and service information", "operationId": "comprehensiveHealthCheck", "responses": {"200": {"description": "Detailed system health information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthCheckResponseSchema"}}}}, "500": {"description": "Internal server error"}, "503": {"description": "System is unhealthy with detailed diagnostics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthCheckResponseSchema"}}}}}}}, "/api/v1/health/ping": {"get": {"tags": ["API v1", "Health Check", "Health Check"], "summary": "Ping Endpoint", "description": "Minimal ping endpoint for basic connectivity testing", "operationId": "pingCheck", "responses": {"200": {"description": "Service is responding", "content": {"application/json": {"schema": {"properties": {"status": {"type": "string", "example": "pong"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-01-08T10:30:00Z"}}, "additionalProperties": true, "type": "object", "required": ["status", "timestamp"], "title": "Response Pingcheck"}, "example": {"status": "pong", "timestamp": "2025-01-08T10:30:00Z"}}}}, "500": {"description": "Internal server error"}, "503": {"description": "Service unavailable - system is unhealthy"}}}}, "/api/v1/health/ready": {"get": {"tags": ["API v1", "Health Check", "Health Check"], "summary": "Readiness Check", "description": "Kubernetes-style readiness probe endpoint", "operationId": "readinessCheck", "responses": {"200": {"description": "Service is ready to accept traffic", "content": {"application/json": {"schema": {"properties": {"ready": {"type": "boolean", "example": true}, "database_status": {"type": "string", "example": "healthy"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-01-08T10:30:00Z"}}, "type": "object", "required": ["ready", "database_status", "timestamp"]}, "example": {"ready": true, "database_status": "healthy", "timestamp": "2025-01-08T10:30:00Z"}}}}, "500": {"description": "Internal server error"}, "503": {"description": "Service is not ready", "content": {"application/json": {"schema": {"properties": {"ready": {"type": "boolean", "example": false}, "error": {"type": "string", "example": "Readiness check failed"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-01-08T10:30:00Z"}}, "type": "object", "required": ["ready", "error", "timestamp"]}, "example": {"ready": false, "error": "Readiness check failed", "timestamp": "2025-01-08T10:30:00Z"}}}}}}}, "/api/v1/health/live": {"get": {"tags": ["API v1", "Health Check", "Health Check"], "summary": "Liveness Check", "description": "Kubernetes-style liveness probe endpoint", "operationId": "livenessCheck", "responses": {"200": {"description": "Service is alive", "content": {"application/json": {"schema": {"properties": {"alive": {"type": "boolean", "example": true}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-01-08T10:30:00Z"}}, "additionalProperties": true, "type": "object", "required": ["alive", "timestamp"], "title": "Response Livenesscheck"}, "example": {"alive": true, "timestamp": "2025-01-08T10:30:00Z"}}}}, "500": {"description": "Internal server error"}, "503": {"description": "Service unavailable - system is unhealthy"}}}}, "/api/v1/auth/login": {"post": {"tags": ["API v1", "Authentication", "Authentication"], "summary": "User Login", "description": "Authenticate user with email/username and password", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestSchema"}}}, "required": true}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/token": {"post": {"tags": ["API v1", "Authentication", "Authentication"], "summary": "OAuth2 Token Endpoint", "description": "OAuth2-compatible token endpoint for authentication", "operationId": "getAccessToken", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_getAccessToken"}}}, "required": true}, "responses": {"200": {"description": "Token issued successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["API v1", "Authentication", "Authentication"], "summary": "User <PERSON>", "description": "Logout current user and invalidate session", "operationId": "logout", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoutResponseSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/auth/refresh": {"post": {"tags": ["API v1", "Authentication", "Authentication"], "summary": "Refresh Access Token", "description": "Refresh access token using current authentication", "operationId": "refreshToken", "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/auth/change-password": {"post": {"tags": ["API v1", "Authentication", "Authentication"], "summary": "Change Password", "description": "Change password for current authenticated user", "operationId": "changePassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChangeRequestSchema"}}}, "required": true}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChangeResponseSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Access forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid password data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/test-simple": {"get": {"tags": ["API v1", "User Management", "User Management"], "summary": "Test Simple Endpoint", "description": "Completely simple test endpoint with no dependencies.", "operationId": "test_simple_endpoint_api_v1_users_test_simple_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}}}, "/api/v1/users/test-with-auth": {"get": {"tags": ["API v1", "User Management", "User Management"], "summary": "Test With Auth Endpoint", "description": "Test endpoint with same dependencies as /me to isolate the issue.", "operationId": "test_with_auth_endpoint_api_v1_users_test_with_auth_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/me": {"get": {"tags": ["API v1", "User Management", "User Management"], "summary": "Get Current User Profile", "description": "Get the profile of the currently authenticated user", "operationId": "getCurrentUserProfile", "responses": {"200": {"description": "Current user profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "put": {"tags": ["API v1", "User Management", "User Management"], "summary": "Update Current User Profile", "description": "Update the profile of the currently authenticated user", "operationId": "updateCurrentUserProfile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateSchema"}}}, "required": true}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid update data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/summary": {"get": {"tags": ["API v1", "User Management", "User Management"], "summary": "Get Users Summary", "description": "Get a summary list of all users (admin only)", "operationId": "getUsersSummary", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Maximum number of users to return", "default": 100, "title": "Limit"}, "description": "Maximum number of users to return"}], "responses": {"200": {"description": "List of user summaries", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserSummarySchema"}, "title": "Response 200 Getuserssummary"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Admin privileges required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}/profile": {"get": {"tags": ["API v1", "User Management", "User Management"], "summary": "Get User Profile by ID", "description": "Get detailed profile of a specific user (admin only)", "operationId": "getUserProfileById", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "User profile data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Admin privileges required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}/activate": {"post": {"tags": ["API v1", "User Management", "User Management"], "summary": "Activate User Account", "description": "Activate a user account (admin only)", "operationId": "activateUserAccount", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "User account activated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Admin privileges required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}/deactivate": {"post": {"tags": ["API v1", "User Management", "User Management"], "summary": "Deactivate User Account", "description": "Deactivate a user account (admin only)", "operationId": "deactivateUserAccount", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "User account deactivated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Admin privileges required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/": {"post": {"tags": ["API v1", "User Management - Admin", "User Management"], "summary": "Create a new user", "description": "Create a new user with unified error handling.", "operationId": "create_entity_api_v1_users__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Entity Data Dict"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "User already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["API v1", "User Management - Admin", "User Management"], "summary": "List users with pagination", "description": "List users with pagination and optional search using unified error handling.", "operationId": "list_entities_api_v1_users__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number (1-based)", "default": 1, "title": "Page"}, "description": "Page number (1-based)"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Number of users per page", "default": 10, "title": "Per Page"}, "description": "Number of users per page"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Sort field", "title": "Sort By"}, "description": "Sort field"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Sort order (asc/desc)", "default": "asc", "title": "Sort Order"}, "description": "Sort order (asc/desc)"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search term", "title": "Search"}, "description": "Search term"}, {"name": "include_deleted", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Include soft-deleted records", "default": false, "title": "Include Deleted"}, "description": "Include soft-deleted records"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPaginatedResponseSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{id}": {"get": {"tags": ["API v1", "User Management - Admin", "User Management"], "summary": "Retrieve user details by id", "description": "Retrieve user details by ID with unified error handling.", "operationId": "get_entity_api_v1_users__id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "integer"}], "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["API v1", "User Management - Admin", "User Management"], "summary": "Update user details", "description": "Update user details with unified error handling.", "operationId": "update_entity_api_v1_users__id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "integer"}], "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Entity Data Dict"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["API v1", "User Management - Admin", "User Management"], "summary": "Delete a user", "description": "Delete user (soft delete) with unified error handling.", "operationId": "delete_entity_api_v1_users__id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "integer"}], "title": "Id"}}], "responses": {"204": {"description": "Successful Response"}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/": {"post": {"tags": ["API v1", "Component Management"], "summary": "Create Component", "description": "Create a new electrical component with comprehensive validation", "operationId": "createComponent", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCreateSchema"}}}}, "responses": {"201": {"description": "Component created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentReadSchema"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid component data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "Component already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["API v1", "Component Management"], "summary": "List Components", "description": "List components with pagination, search, and filtering", "operationId": "listComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}, {"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search term", "title": "Search Term"}, "description": "Search term"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ComponentCategoryType"}, {"type": "null"}], "description": "Filter by category", "title": "Category"}, "description": "Filter by category"}, {"name": "component_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ComponentType"}, {"type": "null"}], "description": "Filter by type", "title": "Component Type"}, "description": "Filter by type"}, {"name": "manufacturer", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by manufacturer", "title": "Manufacturer"}, "description": "Filter by manufacturer"}, {"name": "is_preferred", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by preferred status", "title": "Is Preferred"}, "description": "Filter by preferred status"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "default": true, "title": "Is Active"}, "description": "Filter by active status"}], "responses": {"200": {"description": "Components retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentPaginatedResponseSchema"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid search parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/categories": {"get": {"tags": ["API v1", "Component Management"], "summary": "List Component Categories", "description": "Get list of available component categories", "operationId": "listComponentCategories", "responses": {"200": {"description": "Component categories retrieved successfully", "content": {"application/json": {"schema": {"items": {"additionalProperties": {"type": "string"}, "type": "object"}, "type": "array", "title": "Response Listcomponentcategories"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/components/types": {"get": {"tags": ["API v1", "Component Management"], "summary": "List Component Types", "description": "Get list of available component types, optionally filtered by category", "operationId": "listComponentTypes", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ComponentCategoryType"}, {"type": "null"}], "description": "Filter by category", "title": "Category"}, "description": "Filter by category"}], "responses": {"200": {"description": "Component types retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}, "title": "Response Listcomponenttypes"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/preferred": {"get": {"tags": ["API v1", "Component Management"], "summary": "Get Preferred Components", "description": "Get components marked as preferred", "operationId": "getPreferredComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Maximum number of records", "default": 100, "title": "Limit"}, "description": "Maximum number of records"}], "responses": {"200": {"description": "Preferred components retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentSummarySchema"}, "title": "Response 200 Getpreferredcomponents"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/stats": {"get": {"tags": ["API v1", "Component Management"], "summary": "Get Component Statistics", "description": "Get component catalog statistics and analytics", "operationId": "getComponentStats", "responses": {"200": {"description": "Component statistics retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentStatsSchema"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/components/{component_id}": {"get": {"tags": ["API v1", "Component Management"], "summary": "Get Component by ID", "description": "Retrieve a specific component by its unique identifier", "operationId": "getComponentById", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "component_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Component Id"}}], "responses": {"200": {"description": "Component data retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentReadSchema"}}}}, "500": {"description": "Internal server error"}, "404": {"description": "Component not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["API v1", "Component Management"], "summary": "Update Component", "description": "Update an existing component with validation", "operationId": "updateComponent", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "component_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Component Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentUpdateSchema"}}}}, "responses": {"200": {"description": "Component updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentReadSchema"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid update data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Component not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "Update conflicts with existing data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["API v1", "Component Management"], "summary": "Delete Component", "description": "Soft delete a component (marks as deleted but preserves data)", "operationId": "deleteComponent", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "component_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Component Id"}}], "responses": {"204": {"description": "Component deleted successfully"}, "500": {"description": "Internal server error"}, "404": {"description": "Component not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "Component has dependencies and cannot be deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/search": {"post": {"tags": ["API v1", "Component Management"], "summary": "Advanced Component Search", "description": "Advanced search with specification-based filtering", "operationId": "searchComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentSearchSchema"}}}}, "responses": {"200": {"description": "Search results retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentPaginatedResponseSchema"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid search parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/search/specifications": {"post": {"tags": ["API v1", "Component Management"], "summary": "Search Components by Specifications", "description": "Enhanced specification-based search with technical filtering", "operationId": "searchComponentsBySpecifications", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Specifications"}}}}, "responses": {"200": {"description": "Specification search results retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentPaginatedResponseSchema"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid specification parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/search/suggestions": {"get": {"tags": ["API v1", "Component Management"], "summary": "Get Search Suggestions", "description": "Get search suggestions for autocomplete functionality", "operationId": "getSearchSuggestions", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string", "minLength": 2, "description": "Search query for suggestions", "title": "Query"}, "description": "Search query for suggestions"}, {"name": "field", "in": "query", "required": false, "schema": {"type": "string", "description": "Field to search for suggestions (name, manufacturer, part_number)", "default": "name", "title": "Field"}, "description": "Field to search for suggestions (name, manufacturer, part_number)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "Maximum number of suggestions", "default": 10, "title": "Limit"}, "description": "Maximum number of suggestions"}], "responses": {"200": {"description": "Search suggestions retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Getsearchsuggestions"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid search parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/search/advanced": {"post": {"tags": ["API v1", "Component Management"], "summary": "Advanced Component Search", "description": "Perform advanced component search with complex filtering and relevance scoring", "operationId": "advancedComponentSearch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentAdvancedSearchSchema"}}}}, "responses": {"200": {"description": "Advanced search completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentAdvancedSearchResponseSchema"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid search parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/search/relevance": {"get": {"tags": ["API v1", "Component Management"], "summary": "Relevance-Based Component Search", "description": "Search components with relevance scoring and ranking", "operationId": "relevanceComponentSearch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "search_term", "in": "query", "required": true, "schema": {"type": "string", "minLength": 2, "description": "Search term", "title": "Search Term"}, "description": "Search term"}, {"name": "search_fields", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Comma-separated list of fields to search", "title": "Search Fields"}, "description": "Comma-separated list of fields to search"}, {"name": "fuzzy", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Enable fuzzy matching", "default": false, "title": "Fuzzy"}, "description": "Enable fuzzy matching"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}], "responses": {"200": {"description": "Relevance search completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentAdvancedSearchResponseSchema"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid search parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/manufacturers": {"get": {"tags": ["API v1", "Component Management"], "summary": "List Component Manufacturers", "description": "Get list of manufacturers from existing components", "operationId": "listComponentManufacturers", "responses": {"200": {"description": "Manufacturers retrieved successfully", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response Listcomponentmanufacturers"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/components/by-category/{category}": {"get": {"tags": ["API v1", "Component Management"], "summary": "Get Components by Category", "description": "Get components filtered by category", "operationId": "getComponentsByCategory", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/ComponentCategoryType"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Maximum number of records", "default": 100, "title": "Limit"}, "description": "Maximum number of records"}], "responses": {"200": {"description": "Components retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentSummarySchema"}, "title": "Response 200 Getcomponentsbycategory"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/by-type/{component_type}": {"get": {"tags": ["API v1", "Component Management"], "summary": "Get Components by Type", "description": "Get components filtered by type", "operationId": "getComponentsByType", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "component_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/ComponentType"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Maximum number of records", "default": 100, "title": "Limit"}, "description": "Maximum number of records"}], "responses": {"200": {"description": "Components retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentSummarySchema"}, "title": "Response 200 Getcomponentsbytype"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/bulk/create": {"post": {"tags": ["API v1", "Component Management"], "summary": "Bulk Create Components", "description": "Create multiple components in a single transaction with validation", "operationId": "bulkCreateComponents", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentBulkCreateSchema"}}}, "required": true}, "responses": {"201": {"description": "Components created successfully", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ComponentValidationResultSchema"}, "type": "array", "title": "Response 201 Bulkcreatecomponents"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid bulk create data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/components/bulk/update": {"post": {"tags": ["API v1", "Component Management"], "summary": "Bulk Update Components", "description": "Update multiple components in a single transaction with validation", "operationId": "bulkUpdateComponents", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentBulkUpdateSchema"}}}, "required": true}, "responses": {"200": {"description": "Components updated successfully", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ComponentValidationResultSchema"}, "type": "array", "title": "Response 200 Bulkupdatecomponents"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid bulk update data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/components/export": {"post": {"tags": ["API v1", "Component Management"], "summary": "Export Components", "description": "Export components to various formats (JSON, CSV, XLSX)", "operationId": "exportComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "format", "in": "query", "required": false, "schema": {"type": "string", "description": "Export format (json, csv, xlsx)", "default": "json", "title": "Format"}, "description": "Export format (json, csv, xlsx)"}, {"name": "component_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "items": {"type": "integer"}}, {"type": "null"}], "description": "Specific component IDs to export", "title": "Component Ids"}, "description": "Specific component IDs to export"}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "object", "additionalProperties": true}, {"type": "null"}], "title": "Search Filters"}}}}, "responses": {"200": {"description": "Components exported successfully", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Exportcomponents"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid export parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/import": {"post": {"tags": ["API v1", "Component Management"], "summary": "Import Components", "description": "Import components from various formats (JSON, CSV, XLSX)", "operationId": "importComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "format", "in": "query", "required": false, "schema": {"type": "string", "description": "Import format (json, csv, xlsx)", "default": "json", "title": "Format"}, "description": "Import format (json, csv, xlsx)"}, {"name": "validate_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Only validate without importing", "default": false, "title": "Validate Only"}, "description": "Only validate without importing"}, {"name": "update_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Update existing components", "default": false, "title": "Update Existing"}, "description": "Update existing components"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Import Data"}}}}, "responses": {"201": {"description": "Components imported successfully", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Importcomponents"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid import data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/bulk/create-validated": {"post": {"tags": ["API v1", "Component Management"], "summary": "Enhanced Bulk Create Components", "description": "Create multiple components with comprehensive validation and duplicate checking", "operationId": "enhancedBulkCreateComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "validate_duplicates", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Check for duplicate components", "default": true, "title": "Validate Duplicates"}, "description": "Check for duplicate components"}, {"name": "batch_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "description": "Batch size for processing", "default": 100, "title": "<PERSON><PERSON> Si<PERSON>"}, "description": "Batch size for processing"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": true}, "title": "Components Data"}}}}, "responses": {"201": {"description": "Bulk creation completed with results", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Enhancedbulkcreatecomponents"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid component data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/bulk/update-selective": {"put": {"tags": ["API v1", "Component Management"], "summary": "Selective Bulk Update Components", "description": "Update multiple components with different data for each component", "operationId": "selectiveBulkUpdateComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "batch_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "description": "Batch size for processing", "default": 100, "title": "<PERSON><PERSON> Si<PERSON>"}, "description": "Batch size for processing"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": true}, "title": "Updates"}}}}, "responses": {"200": {"description": "Bulk update completed with results", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Selectivebulkupdatecomponents"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid update data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/bulk/delete": {"delete": {"tags": ["API v1", "Component Management"], "summary": "Bulk Delete Components", "description": "Delete multiple components with soft or hard delete options", "operationId": "bulkDeleteComponents", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "soft_delete", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Perform soft delete (default) or hard delete", "default": true, "title": "Soft Delete"}, "description": "Perform soft delete (default) or hard delete"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer"}, "title": "Component Ids"}}}}, "responses": {"200": {"description": "Bulk deletion completed with results", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Bulkdeletecomponents"}}}}, "500": {"description": "Internal server error"}, "400": {"description": "Invalid deletion parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/components/performance/metrics": {"get": {"tags": ["API v1", "Component Management"], "summary": "Get Performance Metrics", "description": "Get comprehensive performance metrics for the component system", "operationId": "getPerformanceMetrics", "responses": {"200": {"description": "Performance metrics retrieved successfully", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Getperformancemetrics"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/components/performance/optimize": {"post": {"tags": ["API v1", "Component Management"], "summary": "Optimize System Performance", "description": "Perform system performance optimization tasks", "operationId": "optimizeSystemPerformance", "responses": {"200": {"description": "Performance optimization completed", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Optimizesystemperformance"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/components/cache/invalidate": {"delete": {"tags": ["API v1", "Component Management"], "summary": "Invalidate Component Cache", "description": "Invalidate component cache entries for better performance", "operationId": "invalidateComponentCache", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "component_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Specific component ID to invalidate", "title": "Component Id"}, "description": "Specific component ID to invalidate"}], "responses": {"200": {"description": "Cache invalidation completed", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Invalidatecomponentcache"}}}}, "500": {"description": "Internal server error"}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-categories/": {"post": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Create Component Category", "description": "Create a new component category with hierarchical support", "operationId": "create_component_category_api_v1_component_categories__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryCreateSchema"}}}}, "responses": {"201": {"description": "Category created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid category data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "Category already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "List Component Categories", "description": "List component categories with filtering and pagination", "operationId": "list_component_categories_api_v1_component_categories__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}, {"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search term", "title": "Search Term"}, "description": "Search term"}, {"name": "parent_category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by parent category", "title": "Parent Category Id"}, "description": "Filter by parent category"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Is Active"}, "description": "Filter by active status"}], "responses": {"200": {"description": "Categories retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryListResponseSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-categories/tree": {"get": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Get Category Tree", "description": "Get hierarchical category tree structure", "operationId": "get_category_tree_api_v1_component_categories_tree_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "root_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Root category ID", "title": "Root Id"}, "description": "Root category ID"}], "responses": {"200": {"description": "Category tree retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryTreeResponseSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-categories/{category_id}/move": {"put": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Move Category", "description": "Move category to a new parent location in the tree", "operationId": "move_component_category_api_v1_component_categories__category_id__move_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "New parent category ID (null for root level)", "title": "New Parent Id"}}}}, "responses": {"200": {"description": "Category moved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Category not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Invalid move operation (would create circular reference)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}}}, "/api/v1/component-categories/{category_id}/copy": {"post": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Copy Category", "description": "Copy category and optionally its children to a new location", "operationId": "copy_component_category_api_v1_component_categories__category_id__copy_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_copy_component_category_api_v1_component_categories__category_id__copy_post"}}}}, "responses": {"201": {"description": "Category copied successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Category not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-categories/restructure": {"put": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Bulk Restructure Categories", "description": "Perform bulk restructure operations on multiple categories", "operationId": "restructure_component_categories_api_v1_component_categories_restructure_put", "requestBody": {"content": {"application/json": {"schema": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Operations", "description": "List of restructure operations"}}}, "required": true}, "responses": {"200": {"description": "Categories restructured successfully", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ComponentCategoryReadSchema"}, "type": "array", "title": "Response 200 Restructure Component Categories Api V1 Component Categories Restructure Put"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Invalid restructure operations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/component-categories/{category_id}": {"get": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Get Component Category", "description": "Get component category by ID with full details", "operationId": "get_component_category_api_v1_component_categories__category_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "responses": {"200": {"description": "Category retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Category not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Update Component Category", "description": "Update component category with validation", "operationId": "update_component_category_api_v1_component_categories__category_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryUpdateSchema"}}}}, "responses": {"200": {"description": "Category updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentCategoryReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Category not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid update data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["API v1", "Component Categories", "Component Categories"], "summary": "Delete Component Category", "description": "Soft delete component category with dependency checking", "operationId": "delete_component_category_api_v1_component_categories__category_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "responses": {"204": {"description": "Category deleted successfully"}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Category not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "Category has dependencies", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-types/": {"post": {"tags": ["API v1", "Component Types", "Component Types"], "summary": "Create Component Type", "description": "Create a new component type with category relationship", "operationId": "create_component_type_api_v1_component_types__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentTypeCreateSchema"}}}}, "responses": {"201": {"description": "Component type created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentTypeReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid component type data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "Component type already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["API v1", "Component Types", "Component Types"], "summary": "List Component Types", "description": "List component types with filtering and pagination", "operationId": "list_component_types_api_v1_component_types__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}, {"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search term", "title": "Search Term"}, "description": "Search term"}, {"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by category", "title": "Category Id"}, "description": "Filter by category"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Is Active"}, "description": "Filter by active status"}, {"name": "has_specifications_template", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by specifications template", "title": "Has Specifications Template"}, "description": "Filter by specifications template"}], "responses": {"200": {"description": "Component types retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentTypeListResponseSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-types/{type_id}": {"get": {"tags": ["API v1", "Component Types", "Component Types"], "summary": "Get Component Type", "description": "Get component type by ID with full details", "operationId": "get_component_type_api_v1_component_types__type_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Type Id"}}], "responses": {"200": {"description": "Component type retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentTypeReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Component type not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["API v1", "Component Types", "Component Types"], "summary": "Update Component Type", "description": "Update component type with validation", "operationId": "update_component_type_api_v1_component_types__type_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentTypeUpdateSchema"}}}}, "responses": {"200": {"description": "Component type updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentTypeReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Component type not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid update data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["API v1", "Component Types", "Component Types"], "summary": "Delete Component Type", "description": "Soft delete component type with dependency checking", "operationId": "delete_component_type_api_v1_component_types__type_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Type Id"}}], "responses": {"204": {"description": "Component type deleted successfully"}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Component type not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "409": {"description": "Component type has dependencies", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-types/by-category/{category_id}": {"get": {"tags": ["API v1", "Component Types", "Component Types"], "summary": "Get Component Types by Category", "description": "Get component types filtered by category", "operationId": "get_component_types_by_category_api_v1_component_types_by_category__category_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}, {"name": "include_inactive", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Include inactive types", "default": false, "title": "Include Inactive"}, "description": "Include inactive types"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Maximum number of records", "default": 100, "title": "Limit"}, "description": "Maximum number of records"}], "responses": {"200": {"description": "Component types retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentTypeSummarySchema"}, "title": "Response 200 Get Component Types By Category Api V1 Component Types By Category  Category Id  Get"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/component-types/{type_id}/specifications-template": {"put": {"tags": ["API v1", "Component Types", "Component Types"], "summary": "Update Specifications Template", "description": "Update specifications template for component type", "operationId": "update_specifications_template_api_v1_component_types__type_id__specifications_template_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Template"}}}}, "responses": {"200": {"description": "Specifications template updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentTypeReadSchema"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "403": {"description": "Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "404": {"description": "Component type not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "400": {"description": "Invalid template data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/": {"get": {"tags": ["API v1"], "summary": "API v1 Information", "description": "Get information about the v1 API endpoints", "operationId": "get_v1_info_api_v1__get", "responses": {"200": {"description": "API v1 information", "content": {"application/json": {"schema": {}, "example": {"version": "1.0", "title": "Ultimate Electrical Designer API v1", "description": "Professional electrical design and calculation API", "endpoints": {"health": "/api/v1/health", "auth": "/api/v1/auth", "users": "/api/v1/users", "components": "/api/v1/components", "component-categories": "/api/v1/component-categories", "component-types": "/api/v1/component-types"}}}}}, "500": {"description": "Internal server error"}}}}, "/": {"get": {"summary": "<PERSON> Root", "operationId": "read_root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Read Root  Get"}}}}}}}}, "components": {"schemas": {"AdvancedFilterSchema": {"properties": {"field": {"type": "string", "title": "Field", "description": "Field name to filter on"}, "operator": {"$ref": "#/components/schemas/FilterOperatorEnum", "description": "Filter operator"}, "value": {"title": "Value", "description": "Filter value"}, "logical_operator": {"$ref": "#/components/schemas/LogicalOperatorEnum", "description": "How to combine with other filters", "default": "and"}}, "type": "object", "required": ["field", "operator", "value"], "title": "AdvancedFilterSchema", "description": "Schema for individual advanced search filter."}, "Body_copy_component_category_api_v1_component_categories__category_id__copy_post": {"properties": {"target_parent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Target Parent Id", "description": "Target parent category ID"}, "copy_children": {"type": "boolean", "title": "Copy Children", "description": "Whether to copy child categories", "default": false}, "name_suffix": {"type": "string", "title": "Name Suffix", "description": "Suffix to add to copied category name", "default": " (Copy)"}}, "type": "object", "title": "Body_copy_component_category_api_v1_component_categories__category_id__copy_post"}, "Body_getAccessToken": {"properties": {"grant_type": {"anyOf": [{"type": "string", "pattern": "^password$"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_getAccessToken"}, "ComponentAdvancedSearchResponseSchema": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ComponentSearchResultSchema"}, "type": "array", "title": "Items", "description": "Search results"}, "pagination": {"$ref": "#/components/schemas/PaginationSchema", "description": "Pagination information"}, "search_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Search Metadata", "description": "Search metadata (query time, filters applied, etc.)"}, "suggestions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Suggestions", "description": "Search suggestions for refinement"}}, "type": "object", "required": ["items", "pagination"], "title": "ComponentAdvancedSearchResponseSchema", "description": "Response schema for advanced component search."}, "ComponentAdvancedSearchSchema": {"properties": {"search_term": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Search Term", "description": "Text search across multiple fields"}, "search_fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Search Fields", "description": "Specific fields to search in (defaults to name, description, manufacturer, part_number)"}, "fuzzy_search": {"type": "boolean", "title": "Fuzzy Search", "description": "Enable fuzzy text matching", "default": false}, "basic_filters": {"anyOf": [{"items": {"$ref": "#/components/schemas/AdvancedFilterSchema"}, "type": "array"}, {"type": "null"}], "title": "Basic Filters", "description": "List of basic field filters"}, "specification_filters": {"anyOf": [{"items": {"$ref": "#/components/schemas/SpecificationFilterSchema"}, "type": "array"}, {"type": "null"}], "title": "Specification Filters", "description": "List of specification-based filters"}, "range_filters": {"anyOf": [{"items": {"$ref": "#/components/schemas/RangeFilterSchema"}, "type": "array"}, {"type": "null"}], "title": "Range Filters", "description": "List of range filters"}, "price_range": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Price Range", "description": "Price range filter with currency support"}, "sort_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By", "description": "Field to sort by", "default": "name"}, "sort_order": {"type": "string", "title": "Sort Order", "description": "Sort order (asc/desc)", "default": "asc"}, "include_inactive": {"type": "boolean", "title": "Include Inactive", "description": "Include inactive components", "default": false}, "include_deleted": {"type": "boolean", "title": "Include Deleted", "description": "Include soft-deleted components", "default": false}}, "type": "object", "title": "ComponentAdvancedSearchSchema", "description": "Schema for advanced component search with complex filtering."}, "ComponentBulkCreateSchema": {"properties": {"components": {"items": {"$ref": "#/components/schemas/ComponentCreateSchema"}, "type": "array", "maxItems": 100, "minItems": 1, "title": "Components", "description": "List of components to create"}, "validate_duplicates": {"type": "boolean", "title": "Validate Duplicates", "description": "Whether to validate for duplicate components", "default": true}, "skip_invalid": {"type": "boolean", "title": "<PERSON><PERSON>", "description": "Whether to skip invalid components and continue", "default": false}}, "type": "object", "required": ["components"], "title": "ComponentBulkCreateSchema", "description": "Schema for bulk component creation."}, "ComponentBulkUpdateSchema": {"properties": {"component_ids": {"items": {"type": "integer"}, "type": "array", "maxItems": 100, "minItems": 1, "title": "Component Ids", "description": "List of component IDs to update"}, "update_data": {"$ref": "#/components/schemas/ComponentUpdateSchema", "description": "Update data to apply to all components"}, "skip_missing": {"type": "boolean", "title": "<PERSON><PERSON>", "description": "Whether to skip missing components and continue", "default": true}}, "type": "object", "required": ["component_ids", "update_data"], "title": "ComponentBulkUpdateSchema", "description": "Schema for bulk component updates."}, "ComponentCategoryCreateSchema": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Category name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed category description"}, "parent_category_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Parent Category Id", "description": "Parent category ID for hierarchical organization"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether category is active in the system", "default": true}}, "additionalProperties": false, "type": "object", "required": ["name"], "title": "ComponentCategoryCreateSchema", "description": "Schema for creating component categories."}, "ComponentCategoryListResponseSchema": {"properties": {"categories": {"items": {"$ref": "#/components/schemas/ComponentCategorySummarySchema"}, "type": "array", "title": "Categories", "description": "List of component categories"}, "total_count": {"type": "integer", "title": "Total Count", "description": "Total number of categories"}, "page": {"type": "integer", "title": "Page", "description": "Current page number"}, "page_size": {"type": "integer", "title": "<PERSON>", "description": "Number of items per page"}, "total_pages": {"type": "integer", "title": "Total Pages", "description": "Total number of pages"}, "has_next": {"type": "boolean", "title": "Has Next", "description": "Whether there are more pages"}, "has_previous": {"type": "boolean", "title": "Has Previous", "description": "Whether there are previous pages"}}, "type": "object", "required": ["categories", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "ComponentCategoryListResponseSchema", "description": "Schema for paginated component category list responses."}, "ComponentCategoryReadSchema": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Category name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed category description"}, "parent_category_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Parent Category Id", "description": "Parent category ID for hierarchical organization"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether category is active in the system", "default": true}, "id": {"type": "integer", "title": "Id", "description": "Category ID"}, "full_path": {"type": "string", "title": "Full Path", "description": "Full hierarchical path"}, "level": {"type": "integer", "title": "Level", "description": "Hierarchical level"}, "is_root_category": {"type": "boolean", "title": "Is Root Category", "description": "Whether this is a root category"}, "has_children": {"type": "boolean", "title": "Has Children", "description": "Whether category has child categories"}, "component_count": {"type": "integer", "title": "Component Count", "description": "Number of component types in category"}}, "additionalProperties": false, "type": "object", "required": ["created_at", "name", "id", "full_path", "level", "is_root_category", "has_children", "component_count"], "title": "ComponentCategoryReadSchema", "description": "Schema for reading component categories."}, "ComponentCategorySummarySchema": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Category ID"}, "name": {"type": "string", "title": "Name", "description": "Category name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Category description"}, "parent_category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Category Id", "description": "Parent category ID"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether category is active"}, "component_count": {"type": "integer", "title": "Component Count", "description": "Number of component types"}, "child_count": {"type": "integer", "title": "Child Count", "description": "Number of child categories"}}, "type": "object", "required": ["id", "name", "is_active", "component_count", "child_count"], "title": "ComponentCategorySummarySchema", "description": "Schema for component category summary information."}, "ComponentCategoryTreeNodeSchema": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Category ID"}, "name": {"type": "string", "title": "Name", "description": "Category name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Category description"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether category is active"}, "level": {"type": "integer", "title": "Level", "description": "Hierarchical level"}, "component_count": {"type": "integer", "title": "Component Count", "description": "Number of component types"}, "children": {"items": {"$ref": "#/components/schemas/ComponentCategoryTreeNodeSchema"}, "type": "array", "title": "Children", "description": "Child categories"}}, "type": "object", "required": ["id", "name", "is_active", "level", "component_count"], "title": "ComponentCategoryTreeNodeSchema", "description": "Schema for hierarchical category tree representation."}, "ComponentCategoryTreeResponseSchema": {"properties": {"tree": {"items": {"$ref": "#/components/schemas/ComponentCategoryTreeNodeSchema"}, "type": "array", "title": "Tree", "description": "Hierarchical category tree"}, "total_categories": {"type": "integer", "title": "Total Categories", "description": "Total number of categories"}, "max_depth": {"type": "integer", "title": "<PERSON>", "description": "Maximum tree depth"}}, "type": "object", "required": ["tree", "total_categories", "max_depth"], "title": "ComponentCategoryTreeResponseSchema", "description": "Schema for hierarchical category tree responses."}, "ComponentCategoryType": {"type": "string", "enum": ["Power Distribution", "Cables & Wiring", "Protection Devices", "Switching & Control", "Measurement & Monitoring", "Enclosures & Mounting", "Grounding & Bonding", "Power Sources", "Loads", "Communication", "Safety & Emergency", "Heat Tracing System", "Cable Management", "Other Electrical"], "title": "ComponentCategoryType", "description": "Broad categories for electrical components and equipment.\nThis helps in high-level classification and filtering."}, "ComponentCategoryUpdateSchema": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Category name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed category description"}, "parent_category_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Parent Category Id", "description": "Parent category ID for hierarchical organization"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Whether category is active in the system"}}, "type": "object", "title": "ComponentCategoryUpdateSchema", "description": "Schema for updating component categories with partial validation."}, "ComponentCreateSchema": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Component name"}, "manufacturer": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Manufacturer", "description": "Component manufacturer"}, "model_number": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Model Number", "description": "Manufacturer model/part number"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed component description"}, "component_type": {"anyOf": [{"$ref": "#/components/schemas/ComponentType"}, {"type": "null"}], "description": "Type of electrical component (legacy enum, use component_type_id instead)", "deprecated": true}, "category": {"anyOf": [{"$ref": "#/components/schemas/ComponentCategoryType"}, {"type": "null"}], "description": "Component category (legacy enum, use component_category_id instead)", "deprecated": true}, "component_type_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Component Type Id", "description": "Component type ID (new relational approach)"}, "component_category_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Component Category Id", "description": "Component category ID (new relational approach)"}, "specifications": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"$ref": "#/components/schemas/ComponentSpecificationSchema"}, {"type": "null"}], "title": "Specifications", "description": "Component technical specifications"}, "unit_price": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Unit Price", "description": "Component unit price", "example": 125.99}, "currency": {"type": "string", "maxLength": 3, "minLength": 3, "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Price currency (ISO 4217)", "default": "USD"}, "supplier": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Supplier", "description": "Primary supplier name"}, "part_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Part Number", "description": "Supplier part number"}, "weight_kg": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Weight Kg", "description": "Component weight in kilograms"}, "dimensions": {"anyOf": [{"$ref": "#/components/schemas/ComponentDimensionsSchema"}, {"type": "null"}], "description": "Physical dimensions"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether component is active", "default": true}, "is_preferred": {"type": "boolean", "title": "Is Preferred", "description": "Whether component is preferred", "default": false}, "stock_status": {"type": "string", "title": "Stock Status", "description": "Stock availability status", "default": "available"}, "version": {"type": "string", "title": "Version", "description": "Component data version", "default": "1.0"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata", "example": {"internal_ref": "REF-456", "supplier_code": "XYZ-123"}}}, "additionalProperties": false, "type": "object", "required": ["name", "manufacturer", "model_number"], "title": "ComponentCreateSchema", "description": "Schema for creating components with comprehensive validation."}, "ComponentDimensionsSchema": {"properties": {"length": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Length", "description": "Length in mm"}, "width": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Width in mm"}, "height": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Height", "description": "Height in mm"}, "diameter": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Diameter", "description": "Diameter in mm (for cylindrical components)"}, "unit": {"type": "string", "title": "Unit", "description": "Unit of measurement", "default": "mm"}}, "type": "object", "title": "ComponentDimensionsSchema", "description": "Schema for component physical dimensions."}, "ComponentPaginatedResponseSchema": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ComponentReadSchema"}, "type": "array", "title": "Items", "description": "List of components"}, "pagination": {"$ref": "#/components/schemas/PaginationSchema", "description": "Pagination information"}}, "type": "object", "required": ["items", "pagination"], "title": "ComponentPaginatedResponseSchema", "description": "Paginated response schema for components."}, "ComponentReadSchema": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}, "name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Component name"}, "manufacturer": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Manufacturer", "description": "Component manufacturer"}, "model_number": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Model Number", "description": "Manufacturer model/part number"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed component description"}, "component_type": {"anyOf": [{"$ref": "#/components/schemas/ComponentType"}, {"type": "null"}], "description": "Type of electrical component (legacy enum, use component_type_id instead)", "deprecated": true}, "category": {"anyOf": [{"$ref": "#/components/schemas/ComponentCategoryType"}, {"type": "null"}], "description": "Component category (legacy enum, use component_category_id instead)", "deprecated": true}, "component_type_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Component Type Id", "description": "Component type ID (new relational approach)"}, "component_category_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Component Category Id", "description": "Component category ID (new relational approach)"}, "id": {"type": "integer", "title": "Id", "description": "Unique component identifier"}, "specifications": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Specifications", "description": "Component technical specifications"}, "unit_price": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit Price", "description": "Component unit price"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Price currency", "default": "USD"}, "supplier": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Supplier", "description": "Primary supplier name"}, "part_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Part Number", "description": "Supplier part number"}, "weight_kg": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Weight Kg", "description": "Component weight in kilograms"}, "dimensions": {"anyOf": [{"$ref": "#/components/schemas/ComponentDimensionsSchema"}, {"type": "null"}], "description": "Physical dimensions"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether component is active", "default": true}, "is_preferred": {"type": "boolean", "title": "Is Preferred", "description": "Whether component is preferred", "default": false}, "stock_status": {"type": "string", "title": "Stock Status", "description": "Stock availability status", "default": "available"}, "version": {"type": "string", "title": "Version", "description": "Component data version", "default": "1.0"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name", "description": "Full component name (manufacturer + model)"}, "display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Name", "description": "Display name for UI"}}, "additionalProperties": false, "type": "object", "required": ["created_at", "name", "manufacturer", "model_number", "id"], "title": "ComponentReadSchema", "description": "Schema for reading components with full data."}, "ComponentSearchResultSchema": {"properties": {"component": {"$ref": "#/components/schemas/ComponentReadSchema", "description": "Component data"}, "relevance_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Relevance Score", "description": "Search relevance score (0.0 to 1.0)"}, "matched_fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Matched Fields", "description": "Fields that matched the search criteria"}}, "type": "object", "required": ["component"], "title": "ComponentSearchResultSchema", "description": "Schema for individual search result with relevance scoring."}, "ComponentSearchSchema": {"properties": {"search_term": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Search Term", "description": "Search term for name, description, manufacturer, or part number"}, "category": {"anyOf": [{"$ref": "#/components/schemas/ComponentCategoryType"}, {"type": "null"}], "description": "Filter by component category"}, "component_type": {"anyOf": [{"$ref": "#/components/schemas/ComponentType"}, {"type": "null"}], "description": "Filter by component type"}, "manufacturer": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Manufacturer", "description": "Filter by manufacturer"}, "is_preferred": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Preferred", "description": "Filter by preferred status"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Filter by active status", "default": true}, "min_price": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "<PERSON>", "description": "Minimum price filter"}, "max_price": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Max Price", "description": "Maximum price filter"}, "currency": {"anyOf": [{"type": "string", "maxLength": 3, "minLength": 3}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Currency for price filters"}, "stock_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stock Status", "description": "Filter by stock status"}, "specifications": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Specifications", "description": "Filter by technical specifications"}}, "type": "object", "title": "ComponentSearchSchema", "description": "Schema for component search parameters."}, "ComponentSpecificationSchema": {"properties": {"electrical": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Electrical", "description": "Electrical specifications (voltage, current, power, etc.)"}, "thermal": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Thermal", "description": "Thermal specifications (operating temperature, etc.)"}, "mechanical": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Mechanical", "description": "Mechanical specifications (mounting, enclosure, etc.)"}, "standards_compliance": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Standards Compliance", "description": "List of applicable standards (IEC, IEEE, EN, etc.)"}, "environmental": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Environmental", "description": "Environmental specifications (IP rating, humidity, etc.)"}}, "type": "object", "title": "ComponentSpecificationSchema", "description": "Schema for component electrical specifications."}, "ComponentStatsSchema": {"properties": {"total_components": {"type": "integer", "title": "Total Components", "description": "Total number of components"}, "active_components": {"type": "integer", "title": "Active Components", "description": "Number of active components"}, "preferred_components": {"type": "integer", "title": "Preferred Components", "description": "Number of preferred components"}, "components_by_category": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Components By Category", "description": "Component count by category"}, "components_by_manufacturer": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Components By Manufacturer", "description": "Component count by manufacturer"}, "average_price": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Average Price", "description": "Average component price"}, "price_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "title": "Price Range", "description": "Price range (min/max)"}}, "type": "object", "required": ["total_components", "active_components", "preferred_components", "components_by_category", "components_by_manufacturer"], "title": "ComponentStatsSchema", "description": "Schema for component statistics."}, "ComponentSummarySchema": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Unique component identifier"}, "name": {"type": "string", "title": "Name", "description": "Component name"}, "manufacturer": {"type": "string", "title": "Manufacturer", "description": "Component manufacturer"}, "model_number": {"type": "string", "title": "Model Number", "description": "Manufacturer model/part number"}, "component_type": {"$ref": "#/components/schemas/ComponentType", "description": "Type of electrical component"}, "category": {"$ref": "#/components/schemas/ComponentCategoryType", "description": "Component category"}, "unit_price": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit Price", "description": "Component unit price"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Price currency", "default": "USD"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether component is active", "default": true}, "is_preferred": {"type": "boolean", "title": "Is Preferred", "description": "Whether component is preferred", "default": false}, "stock_status": {"type": "string", "title": "Stock Status", "description": "Stock availability status", "default": "available"}}, "additionalProperties": false, "type": "object", "required": ["id", "name", "manufacturer", "model_number", "component_type", "category"], "title": "ComponentSummarySchema", "description": "Schema for component summaries in lists."}, "ComponentType": {"type": "string", "enum": ["Switchboard", "Motor Control Center (MCC)", "Distribution Board (DB)", "Panelboard", "Main Switchboard (MSB)", "Sub Switchboard (SSB)", "Control Panel", "Transformer", "Generator", "Uninterruptible Power Supply (UPS)", "Battery Bank", "DC Power Supply", "Sur<PERSON> Protective Device (SPD)", "Capacitor Bank", "Automatic Transfer Switch (ATS)", "Manual Transfer Switch (MTS)", "Variable Frequency Drive (VFD)", "Soft Starter", "Motor Starter", "Circuit Breaker", "<PERSON><PERSON>", "Residual Current Device (RCD)", "Overload Relay", "Protective Relay", "Power Cable", "Control Cable", "Instrumentation Cable", "Communication Cable", "Fiber Optic Cable", "Coaxial Cable", "Busbar", "Grounding Conductor", "Bonding Conductor", "Cable Tray", "Conduit", "Cable Duct", "Cable Ladder", "Disconnect Switch", "Load Break Switch", "Isolation Switch", "<PERSON><PERSON>", "Control Relay", "<PERSON><PERSON>", "Selector Switch", "Pilot Light", "<PERSON><PERSON>", "Proximity Switch", "Timer", "Counter", "Thermostat", "Pressure Switch", "Flow Switch", "Level Switch", "Ammeter", "Voltmeter", "Power Meter", "Energy Meter", "Current Transformer (CT)", "Voltage Transformer (VT/PT)", "Temperature Sensor", "Pressure Sensor", "Flow Sensor", "Level Sensor", "<PERSON><PERSON><PERSON><PERSON>", "Vibration Sensor", "Gas Detector", "Smoke Detector", "Motion Sensor", "Junction Box", "Terminal Box", "Enclosure", "Terminal Block", "Connector", "Cable Gland", "Cable Lug", "Electric Motor", "Heater", "Lighting Fixture", "Fan", "Pump", "Compressor", "HVAC Unit", "Oven", "Furnace", "Welding Machine", "Rectifier", "Valve Actuator", "Heat Tracing Cable", "Heat Tracing Controller", "Heat Tracing Power Distribution Enclosure", "Heat Tracing Junction Box", "Emergency Stop Button", "Fire Alarm Control Panel", "Fire Alarm Detector", "Strobe Light", "Horn", "Emergency Lighting Fixture", "Ethernet Switch", "Wireless Access Point", "Fiber Optic Transceiver", "Industrial Router", "Junction Terminal", "Miscellaneous Electrical Component"], "title": "ComponentType", "description": "Detailed types of electrical components.\nThis replaces both previous `ComponentType` and `EquipmentType` for a single source of truth.\nEach physical item, whether large equipment or a small device, is a ComponentType."}, "ComponentTypeCreateSchema": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Component type name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed component type description"}, "category_id": {"type": "integer", "minimum": 1.0, "title": "Category Id", "description": "Component category ID"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether component type is active in the system", "default": true}, "specifications_template": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Specifications Template", "description": "JSON template for component specifications"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata for component type"}}, "additionalProperties": false, "type": "object", "required": ["name", "category_id"], "title": "ComponentTypeCreateSchema", "description": "Schema for creating component types."}, "ComponentTypeListResponseSchema": {"properties": {"component_types": {"items": {"$ref": "#/components/schemas/ComponentTypeSummarySchema"}, "type": "array", "title": "Component Types", "description": "List of component types"}, "total_count": {"type": "integer", "title": "Total Count", "description": "Total number of component types"}, "page": {"type": "integer", "title": "Page", "description": "Current page number"}, "page_size": {"type": "integer", "title": "<PERSON>", "description": "Number of items per page"}, "total_pages": {"type": "integer", "title": "Total Pages", "description": "Total number of pages"}, "has_next": {"type": "boolean", "title": "Has Next", "description": "Whether there are more pages"}, "has_previous": {"type": "boolean", "title": "Has Previous", "description": "Whether there are previous pages"}}, "type": "object", "required": ["component_types", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "ComponentTypeListResponseSchema", "description": "Schema for paginated component type list responses."}, "ComponentTypeReadSchema": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Component type name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed component type description"}, "category_id": {"type": "integer", "minimum": 1.0, "title": "Category Id", "description": "Component category ID"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether component type is active in the system", "default": true}, "specifications_template": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Specifications Template", "description": "JSON template for component specifications"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata for component type"}, "id": {"type": "integer", "title": "Id", "description": "Component type ID"}, "full_name": {"type": "string", "title": "Full Name", "description": "Full name including category"}, "category_path": {"type": "string", "title": "Category Path", "description": "Full category path"}, "component_count": {"type": "integer", "title": "Component Count", "description": "Number of components of this type"}, "has_specifications_template": {"type": "boolean", "title": "Has Specifications Template", "description": "Whether type has specifications template"}, "category_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category Name", "description": "Category name"}}, "additionalProperties": false, "type": "object", "required": ["created_at", "name", "category_id", "id", "full_name", "category_path", "component_count", "has_specifications_template"], "title": "ComponentTypeReadSchema", "description": "Schema for reading component types."}, "ComponentTypeSummarySchema": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Component type ID"}, "name": {"type": "string", "title": "Name", "description": "Component type name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Component type description"}, "category_id": {"type": "integer", "title": "Category Id", "description": "Category ID"}, "category_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category Name", "description": "Category name"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether component type is active"}, "component_count": {"type": "integer", "title": "Component Count", "description": "Number of components"}}, "type": "object", "required": ["id", "name", "category_id", "is_active", "component_count"], "title": "ComponentTypeSummarySchema", "description": "Schema for component type summary information."}, "ComponentTypeUpdateSchema": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Component type name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed component type description"}, "category_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Category Id", "description": "Component category ID"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Whether component type is active in the system"}, "specifications_template": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Specifications Template", "description": "JSON template for component specifications"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata for component type"}}, "type": "object", "title": "ComponentTypeUpdateSchema", "description": "Schema for updating component types with partial validation."}, "ComponentUpdateSchema": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Component name"}, "manufacturer": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Manufacturer", "description": "Component manufacturer"}, "model_number": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Model Number", "description": "Manufacturer model/part number"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed component description"}, "component_type": {"anyOf": [{"$ref": "#/components/schemas/ComponentType"}, {"type": "null"}], "description": "Type of electrical component (legacy enum, use component_type_id instead)", "deprecated": true}, "category": {"anyOf": [{"$ref": "#/components/schemas/ComponentCategoryType"}, {"type": "null"}], "description": "Component category (legacy enum, use component_category_id instead)", "deprecated": true}, "component_type_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Component Type Id", "description": "Component type ID (new relational approach)"}, "component_category_id": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Component Category Id", "description": "Component category ID (new relational approach)"}, "specifications": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"$ref": "#/components/schemas/ComponentSpecificationSchema"}, {"type": "null"}], "title": "Specifications", "description": "Component technical specifications"}, "unit_price": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Unit Price", "description": "Component unit price"}, "currency": {"anyOf": [{"type": "string", "maxLength": 3, "minLength": 3}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Price currency (ISO 4217)"}, "supplier": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Supplier", "description": "Primary supplier name"}, "part_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Part Number", "description": "Supplier part number"}, "weight_kg": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Weight Kg", "description": "Component weight in kilograms"}, "dimensions": {"anyOf": [{"$ref": "#/components/schemas/ComponentDimensionsSchema"}, {"type": "null"}], "description": "Physical dimensions"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Whether component is active"}, "is_preferred": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Preferred", "description": "Whether component is preferred"}, "stock_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stock Status", "description": "Stock availability status"}, "version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version", "description": "Component data version"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Additional metadata"}}, "type": "object", "title": "ComponentUpdateSchema", "description": "Schema for updating components with partial validation."}, "ComponentValidationResultSchema": {"properties": {"is_valid": {"type": "boolean", "title": "Is <PERSON>", "description": "Whether component is valid"}, "errors": {"items": {"type": "string"}, "type": "array", "title": "Errors", "description": "List of validation errors"}, "warnings": {"items": {"type": "string"}, "type": "array", "title": "Warnings", "description": "List of validation warnings"}, "component_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Component Id", "description": "Component ID if applicable"}}, "type": "object", "required": ["is_valid"], "title": "ComponentValidationResultSchema", "description": "Schema for component validation results."}, "DatabaseHealthSchema": {"properties": {"status": {"type": "string", "title": "Status", "description": "Database connection status"}, "connection_responsive": {"type": "boolean", "title": "Connection Responsive", "description": "Whether database responds to queries"}, "connection_latency_ms": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Connection Latency Ms", "description": "Database query latency in milliseconds"}, "pool_utilization": {"type": "number", "title": "Pool Utilization", "description": "Connection pool utilization percentage"}, "pool_metrics": {"additionalProperties": true, "type": "object", "title": "Pool Metrics", "description": "Detailed connection pool metrics"}, "health_score": {"type": "integer", "title": "Health Score", "description": "Overall database health score (0-10)"}, "recommendations": {"items": {"type": "string"}, "type": "array", "title": "Recommendations", "description": "Health improvement recommendations"}, "database_type": {"type": "string", "title": "Database Type", "description": "Type of database (postgresql, sqlite, etc.)"}, "connection_error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Connection Error", "description": "Connection error message if any"}}, "type": "object", "required": ["status", "connection_responsive", "pool_utilization", "pool_metrics", "health_score", "database_type"], "title": "DatabaseHealthSchema", "description": "Schema for database health status."}, "ErrorResponseSchema": {"properties": {"code": {"type": "string", "title": "Code", "description": "Unique application-specific error code."}, "detail": {"type": "string", "title": "Detail", "description": "A human-readable explanation of the error."}, "category": {"type": "string", "title": "Category", "description": "Category of the error (e.g., <PERSON><PERSON><PERSON><PERSON>r, Server<PERSON>rror, <PERSON><PERSON><PERSON>)."}, "status_code": {"type": "integer", "title": "Status Code", "description": "HTTP status code equivalent (for UI/API compatibility)."}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Additional context or debugging information."}}, "type": "object", "required": ["code", "detail", "category", "status_code"], "title": "ErrorResponseSchema", "description": "Schema for standardized error responses.\n\nProvides a consistent structure for all API error responses\nincluding error codes, messages, and contextual information.", "example": {"category": "ClientError", "code": "404_001", "detail": "Project with ID 'XYZ-123' not found.", "metadata": {"project_id": "XYZ-123", "requested_by": "<EMAIL>"}, "status_code": 404}}, "FilterOperatorEnum": {"type": "string", "enum": ["eq", "ne", "gt", "gte", "lt", "lte", "contains", "starts_with", "ends_with", "in", "not_in", "between", "fuzzy", "regex", "is_null", "is_not_null"], "title": "FilterOperatorEnum", "description": "Enumeration of supported filter operators for advanced search."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthCheckResponseSchema": {"properties": {"status": {"type": "string", "title": "Status", "description": "Overall system status (healthy, degraded, unhealthy)"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "Health check timestamp"}, "version": {"type": "string", "title": "Version", "description": "Application version"}, "environment": {"type": "string", "title": "Environment", "description": "Current environment (development, production, etc.)"}, "uptime_seconds": {"type": "number", "title": "Uptime Seconds", "description": "Application uptime in seconds"}, "database": {"$ref": "#/components/schemas/DatabaseHealthSchema", "description": "Database health status"}, "services": {"items": {"$ref": "#/components/schemas/ServiceHealthSchema"}, "type": "array", "title": "Services", "description": "Individual service health status"}, "system_metrics": {"anyOf": [{"$ref": "#/components/schemas/SystemMetricsSchema"}, {"type": "null"}], "description": "System performance metrics"}, "health_score": {"type": "integer", "title": "Health Score", "description": "Overall system health score (0-10)"}, "critical_issues": {"items": {"type": "string"}, "type": "array", "title": "Critical Issues", "description": "Critical issues requiring attention"}, "warnings": {"items": {"type": "string"}, "type": "array", "title": "Warnings", "description": "Non-critical warnings"}, "checks_performed": {"items": {"type": "string"}, "type": "array", "title": "Checks Performed", "description": "List of health checks performed"}, "response_time_ms": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Response Time Ms", "description": "Health check response time in milliseconds"}}, "additionalProperties": false, "type": "object", "required": ["status", "timestamp", "version", "environment", "uptime_seconds", "database", "health_score"], "title": "HealthCheckResponseSchema", "description": "Comprehensive health check response schema."}, "LogicalOperatorEnum": {"type": "string", "enum": ["and", "or", "not"], "title": "LogicalOperatorEnum", "description": "Enumeration of logical operators for combining filters."}, "LoginRequestSchema": {"properties": {"username": {"type": "string", "title": "Username", "description": "Username or email"}, "password": {"type": "string", "title": "Password", "description": "Password"}}, "type": "object", "required": ["username", "password"], "title": "LoginRequestSchema", "description": "Schema for login requests."}, "LoginResponseSchema": {"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "JWT access token"}, "token_type": {"type": "string", "title": "Token Type", "description": "Token type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In", "description": "Token expiration time in seconds"}, "user": {"$ref": "#/components/schemas/UserReadSchema", "description": "User information"}}, "type": "object", "required": ["access_token", "expires_in", "user"], "title": "LoginResponseSchema", "description": "Schema for login responses."}, "LogoutResponseSchema": {"properties": {"message": {"type": "string", "title": "Message", "description": "Logout confirmation message", "default": "Successfully logged out"}, "logged_out_at": {"type": "string", "format": "date-time", "title": "Logged Out At", "description": "Logout timestamp"}}, "type": "object", "required": ["logged_out_at"], "title": "LogoutResponseSchema", "description": "Schema for logout responses."}, "PaginationSchema": {"properties": {"page": {"type": "integer", "minimum": 1.0, "title": "Page", "description": "Page number (1-based)", "default": 1}, "size": {"type": "integer", "maximum": 100.0, "minimum": 1.0, "title": "Size", "description": "Items per page", "default": 20}, "total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total", "description": "Total number of items"}, "pages": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Pages", "description": "Total number of pages"}}, "type": "object", "title": "PaginationSchema", "description": "Schema for pagination parameters."}, "PasswordChangeRequestSchema": {"properties": {"current_password": {"type": "string", "title": "Current Password", "description": "Current password"}, "new_password": {"type": "string", "minLength": 8, "title": "New Password", "description": "New password"}, "confirm_password": {"type": "string", "title": "Confirm Password", "description": "Confirm new password"}}, "type": "object", "required": ["current_password", "new_password", "confirm_password"], "title": "PasswordChangeRequestSchema", "description": "Schema for password change requests."}, "PasswordChangeResponseSchema": {"properties": {"message": {"type": "string", "title": "Message", "description": "Success message", "default": "Password changed successfully"}, "changed_at": {"type": "string", "format": "date-time", "title": "Changed At", "description": "Password change timestamp"}}, "type": "object", "required": ["changed_at"], "title": "PasswordChangeResponseSchema", "description": "Schema for password change responses."}, "RangeFilterSchema": {"properties": {"field": {"type": "string", "title": "Field", "description": "Field name to filter on"}, "min_value": {"anyOf": [{"type": "integer"}, {"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Min Value", "description": "Minimum value (inclusive by default)"}, "max_value": {"anyOf": [{"type": "integer"}, {"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Max Value", "description": "Maximum value (inclusive by default)"}, "include_min": {"type": "boolean", "title": "Include Min", "description": "Whether to include minimum value", "default": true}, "include_max": {"type": "boolean", "title": "Include Max", "description": "Whether to include maximum value", "default": true}}, "type": "object", "required": ["field"], "title": "RangeFilterSchema", "description": "Schema for range filter."}, "ServiceHealthSchema": {"properties": {"name": {"type": "string", "title": "Name", "description": "Service name"}, "status": {"type": "string", "title": "Status", "description": "Service status (healthy, degraded, unhealthy)"}, "version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version", "description": "Service version"}, "uptime_seconds": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Uptime Seconds", "description": "Service uptime in seconds"}, "last_check": {"type": "string", "format": "date-time", "title": "Last Check", "description": "Last health check timestamp"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "Additional service details"}}, "type": "object", "required": ["name", "status", "last_check"], "title": "ServiceHealthSchema", "description": "Schema for individual service health status."}, "SimpleHealthResponseSchema": {"properties": {"status": {"type": "string", "title": "Status", "description": "System status (healthy, unhealthy)"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "Health check timestamp"}, "version": {"type": "string", "title": "Version", "description": "Application version"}, "database_status": {"type": "string", "title": "Database Status", "description": "Database connection status"}, "uptime_seconds": {"type": "number", "title": "Uptime Seconds", "description": "Application uptime in seconds"}}, "additionalProperties": false, "type": "object", "required": ["status", "timestamp", "version", "database_status", "uptime_seconds"], "title": "SimpleHealthResponseSchema", "description": "Simplified health check response for basic monitoring."}, "SpecificationFilterSchema": {"properties": {"path": {"type": "string", "title": "Path", "description": "Dot notation path in specifications JSON"}, "operator": {"$ref": "#/components/schemas/FilterOperatorEnum", "description": "Filter operator"}, "value": {"title": "Value", "description": "Filter value"}, "data_type": {"type": "string", "title": "Data Type", "description": "Data type for proper casting", "default": "string"}, "unit": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit", "description": "Unit for conversion (if applicable)"}, "logical_operator": {"$ref": "#/components/schemas/LogicalOperatorEnum", "description": "How to combine with other filters", "default": "and"}}, "type": "object", "required": ["path", "operator", "value"], "title": "SpecificationFilterSchema", "description": "Schema for specification-based filter."}, "SystemMetricsSchema": {"properties": {"memory_usage_mb": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Memory Usage Mb", "description": "Current memory usage in MB"}, "cpu_usage_percent": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Cpu Usage Percent", "description": "Current CPU usage percentage"}, "disk_usage_percent": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Disk Usage Percent", "description": "Current disk usage percentage"}, "active_connections": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Active Connections", "description": "Number of active connections"}, "request_count_last_minute": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Request Count <PERSON>", "description": "Requests processed in last minute"}}, "type": "object", "title": "SystemMetricsSchema", "description": "Schema for system performance metrics."}, "UserPaginatedResponseSchema": {"properties": {"items": {"items": {"$ref": "#/components/schemas/UserReadSchema"}, "type": "array", "title": "Items", "description": "List of users"}, "pagination": {"$ref": "#/components/schemas/PaginationSchema", "description": "Pagination information"}}, "type": "object", "required": ["items", "pagination"], "title": "UserPaginatedResponseSchema", "description": "Paginated response schema for users."}, "UserReadSchema": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "Last update timestamp"}, "name": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Name", "description": "User name"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "Email address"}, "role": {"$ref": "#/components/schemas/UserRole", "description": "User role", "default": "Viewer"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether user is active", "default": true}, "id": {"type": "integer", "title": "Id", "description": "Unique identifier"}, "is_admin": {"type": "boolean", "title": "Is Admin", "description": "Whether user is an admin", "default": false}, "last_login": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login", "description": "Last login timestamp"}}, "additionalProperties": false, "type": "object", "required": ["created_at", "name", "email", "id"], "title": "UserReadSchema", "description": "Schema for reading users."}, "UserRole": {"type": "string", "enum": ["Administrator", "Project Manager", "Lead Electrical Engineer", "Electrical Designer", "Mechanical Engineer", "Instrumentation Engineer", "CAD Operator", "Viewer", "Guest", "Client", "Supplier"], "title": "UserRole", "description": "Roles for users within the application."}, "UserSummarySchema": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Unique identifier"}, "name": {"type": "string", "title": "Name", "description": "User name"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "Email address"}, "role": {"$ref": "#/components/schemas/UserRole", "description": "User role"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether user is active"}, "last_login": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login", "description": "Last login timestamp"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}}, "additionalProperties": false, "type": "object", "required": ["id", "name", "email", "role", "is_active", "created_at"], "title": "UserSummarySchema", "description": "Schema for user summaries."}, "UserUpdateSchema": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 3}, {"type": "null"}], "title": "Name", "description": "User name"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address"}, "role": {"anyOf": [{"$ref": "#/components/schemas/UserRole"}, {"type": "null"}], "description": "User role"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Whether user is active"}, "password": {"anyOf": [{"type": "string", "minLength": 8}, {"type": "null"}], "title": "Password", "description": "New password"}}, "type": "object", "title": "UserUpdateSchema", "description": "Schema for updating users."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/api/v1/auth/token"}}}}}}