# backend/core/services/user_service.py
"""User Service.

This module provides business logic for user operations, including
authentication, user management, and security operations.
"""

from passlib.context import CryptContext
from sqlalchemy.orm import Session

from src.config.logging_config import logger
from src.config.settings import settings
from src.core.errors.exceptions import (
    BaseApplicationException,
    InvalidInputError,
    NotFoundError,
)

# Unified systems imports
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.user_preference_repository import (
    UserPreferenceRepository,
)
from src.core.repositories.general.user_repository import UserRepository
from src.core.schemas.base import PaginationSchema
from src.core.schemas.general.user_schemas import (
    LoginRequestSchema,
    LoginResponseSchema,
    LogoutResponseSchema,
    PasswordChangeRequestSchema,
    UserCreateSchema,
    UserPaginatedResponseSchema,
    UserPreferenceReadSchema,
    UserPreferenceUpdateSchema,
    UserReadSchema,
    UserSummarySchema,
    UserUpdateSchema,
)
from src.core.utils.datetime_utils import utcnow_aware
from src.core.utils.pagination_utils import PaginationParams, SortParams, paginate_query
from src.core.utils.query_utils import QueryBuilder

# Import utilities for enhanced functionality
from src.core.utils.string_utils import sanitize_text

# Initialize password context for secure hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserService:
    """Service for user operations and authentication management.

    This service provides high-level business logic for user management workflows,
    including authentication, password management, and user preferences.
    """

    def __init__(self, db_session: Session):
        """Initialize the user service.

        Args:
            db_session: SQLAlchemy database session

        """
        self.db_session = db_session
        self.user_repo = UserRepository(db_session)
        self.preference_repo = UserPreferenceRepository(db_session)
        logger.debug("UserService initialized")

    # ============================================================================
    # USER MANAGEMENT OPERATIONS
    # ============================================================================

    @handle_service_errors("create_user")
    @monitor_service_performance("create_user")
    def create_user(self, user_data: UserCreateSchema) -> UserReadSchema:
        """Create a new user with secure password hashing and data sanitization.

        Args:
            user_data: User creation data

        Returns:
            UserReadSchema: Created user

        Raises:
            BusinessLogicError: If validation fails
            DatabaseError: If database operation fails

        """
        logger.info(f"Creating user: {user_data.name}")

        # Check if user with email already exists
        if user_data.email:
            existing_user = self.user_repo.get_by_email(user_data.email)
            if existing_user:
                raise InvalidInputError(
                    f"User with email {user_data.email} already exists"
                )

        # Hash the password
        password_hash = pwd_context.hash(user_data.password)

        # Create user data without plain password and sanitize text fields
        user_dict = user_data.model_dump(exclude={"password"})
        user_dict["password_hash"] = password_hash

        # Sanitize text fields for security
        if user_dict.get("name"):
            user_dict["name"] = sanitize_text(user_dict["name"])
        if user_dict.get("bio"):
            user_dict["bio"] = sanitize_text(user_dict["bio"])
        if user_dict.get("company"):
            user_dict["company"] = sanitize_text(user_dict["company"])
        if user_dict.get("department"):
            user_dict["department"] = sanitize_text(user_dict["department"])

        # Create user
        user = self.user_repo.create(user_dict)
        self.db_session.commit()
        self.db_session.refresh(user)

        logger.info(f"User created successfully: {user.id}")
        return UserReadSchema.model_validate(user)

    @handle_service_errors("create_superuser")
    @monitor_service_performance("create_superuser")
    def create_superuser(
        self, username: str, password: str, email: str
    ) -> UserReadSchema:
        """Create a superuser with administrative privileges.

        Args:
            username: Username for the superuser
            password: Password for the superuser
            email: Email address for the superuser

        Returns:
            UserReadSchema: Created superuser

        Raises:
            BusinessLogicError: If validation fails or user already exists
            DatabaseError: If database operation fails

        """
        logger.info(f"Creating superuser: {username}")

        # Check if user with email already exists
        existing_user = self.user_repo.get_by_email(email)
        if existing_user:
            raise InvalidInputError(f"User with email {email} already exists")

        # Check if user with name already exists
        existing_user_by_name = self.user_repo.get_by_name(username)
        if existing_user_by_name:
            raise InvalidInputError(f"User with name {username} already exists")

        # Import UserRole enum
        from src.core.enums import UserRole

        # Create user data for superuser with admin privileges
        user_data = UserCreateSchema(
            name=username,  # Use the name field from CommonColumns
            email=email,
            password=password,
            is_active=True,
            role=UserRole.ADMIN,
        )

        # Create the superuser using the standard create_user method
        superuser = self.create_user(user_data)

        logger.info(f"Superuser created successfully: {superuser.id}")
        return superuser

    @handle_service_errors("get_user")
    @monitor_service_performance("get_user")
    def get_user(self, user_id: int) -> UserReadSchema:
        """Get user by ID.

        Args:
            user_id: User ID

        Returns:
            UserReadSchema: User data

        Raises:
            NotFoundError: If user not found

        """
        logger.debug(f"Retrieving user: {user_id}")

        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        return UserReadSchema.model_validate(user)

    @handle_service_errors("get_user_by_email")
    @monitor_service_performance("get_user_by_email")
    def get_user_by_email(self, email: str) -> UserReadSchema:
        """Get user by email address.

        Args:
            email: User email address

        Returns:
            UserReadSchema: User data

        Raises:
            NotFoundError: If user not found

        """
        logger.debug(f"Retrieving user by email: {email}")

        user = self.user_repo.get_by_email(email)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User with email {email} not found or inactive",
            )

        return UserReadSchema.model_validate(user)

    @handle_service_errors("update_user")
    @monitor_service_performance("update_user")
    def update_user(
        self, user_id: int, update_data: UserUpdateSchema
    ) -> UserReadSchema:
        """Update user information.

        Args:
            user_id: User ID
            update_data: Update data

        Returns:
            UserReadSchema: Updated user

        Raises:
            NotFoundError: If user not found
            BusinessLogicError: If validation fails

        """
        logger.info(f"Updating user: {user_id}")

        # Validate user exists
        existing_user = self.user_repo.get_by_id(user_id)
        if not existing_user or not existing_user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        # Check email uniqueness if email is being updated
        if update_data.email and update_data.email != existing_user.email:
            email_user = self.user_repo.get_by_email(update_data.email)
            if email_user and email_user.id != user_id:
                raise InvalidInputError(f"Email {update_data.email} is already in use")

        # Update user with sanitized data
        update_dict = update_data.model_dump(exclude_unset=True)

        # Sanitize text fields for security
        if update_dict.get("name"):
            update_dict["name"] = sanitize_text(update_dict["name"])
        if update_dict.get("bio"):
            update_dict["bio"] = sanitize_text(update_dict["bio"])
        if update_dict.get("company"):
            update_dict["company"] = sanitize_text(update_dict["company"])
        if update_dict.get("department"):
            update_dict["department"] = sanitize_text(update_dict["department"])

        updated_user = self.user_repo.update(user_id, update_dict)
        self.db_session.commit()

        logger.info(f"User updated successfully: {user_id}")
        return UserReadSchema.model_validate(updated_user)

    @handle_service_errors("deactivate_user")
    @monitor_service_performance("deactivate_user")
    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user account.

        Args:
            user_id: User ID

        Returns:
            bool: True if deactivated successfully

        Raises:
            NotFoundError: If user not found

        """
        logger.info(f"Deactivating user: {user_id}")

        success = self.user_repo.deactivate_user(user_id)
        if success:
            self.db_session.commit()
            logger.info(f"User deactivated successfully: {user_id}")
        else:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found",
            )
        return success

    @handle_service_errors("activate_user")
    @monitor_service_performance("activate_user")
    def activate_user(self, user_id: int) -> bool:
        """Activate a user account.

        Args:
            user_id: User ID

        Returns:
            bool: True if activated successfully

        Raises:
            NotFoundError: If user not found

        """
        logger.info(f"Activating user: {user_id}")

        # Check if user exists (including inactive users)
        existing_user = self.user_repo.get_by_id(user_id)
        if not existing_user:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found",
            )

        # Use repository to directly update is_active status
        success = self.user_repo.activate_user(user_id)
        if success:
            self.db_session.commit()
            logger.info(f"User activated successfully: {user_id}")
        else:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found",
            )
        return success

    @handle_service_errors("get_users")
    @monitor_service_performance("get_users")
    def get_users(self, skip: int = 0, limit: int = 100) -> list[UserReadSchema]:
        """Get list of active users.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[UserReadSchema]: List of users

        """
        logger.debug(f"Retrieving users: skip={skip}, limit={limit}")

        users = self.user_repo.get_active_users(skip, limit)
        return [UserReadSchema.model_validate(user) for user in users]

    @handle_service_errors("get_users_paginated")
    @monitor_service_performance("get_users_paginated")
    def get_users_paginated(
        self,
        pagination_params: PaginationParams,
        sort_params: SortParams | None = None,
        filters: dict | None = None,
    ) -> UserPaginatedResponseSchema:
        """Get paginated list of users with enhanced search and sorting.

        Args:
            pagination_params: Pagination parameters (page, per_page)
            sort_params: Optional sorting parameters
            filters: Optional filters (search, include_inactive, etc.)

        Returns:
            dict: Paginated response with users and metadata

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(
            f"Retrieving paginated users: page={pagination_params.page}, "
            f"per_page={pagination_params.per_page}, sort={sort_params}, filters={filters}"
        )

        from src.core.models.general.user import User

        # Build base query using QueryBuilder
        builder = QueryBuilder(self.db_session, User)

        # Apply filters
        if filters:
            # Handle active/inactive filter
            include_inactive = filters.get("include_inactive", False)
            if not include_inactive:
                builder.filter_by_field("is_active", True)

            # Handle search filter
            search_term = filters.get("search")
            if search_term:
                searchable_fields = ["name", "email", "company", "department"]
                builder.filter_by_text_search(search_term, searchable_fields)

            # Handle role filter
            role = filters.get("role")
            if role:
                builder.filter_by_field("role", role)
        else:
            # Default: only active users
            builder.filter_by_field("is_active", True)

        # Build the query
        query = builder.build()

        # Apply pagination and sorting using utilities
        allowed_sort_fields = [
            "name",
            "email",
            "created_at",
            "updated_at",
            "company",
            "role",
        ]
        result = paginate_query(
            self.db_session,
            query,
            User,
            pagination_params,
            sort_params,
            allowed_sort_fields,
        )

        # Convert to schemas (use UserReadSchema for pagination to match expected type)
        user_schemas = [UserReadSchema.model_validate(u) for u in result.items]

        # Create pagination information
        pagination = PaginationSchema(
            page=result.page,
            size=result.per_page,
            total=result.total,
            pages=result.total_pages,
        )

        # Create response compatible with UserPaginatedResponseSchema
        response = UserPaginatedResponseSchema(
            items=user_schemas, pagination=pagination
        )

        logger.debug(f"Retrieved {len(user_schemas)} users (total: {result.total})")

        return response

    # ============================================================================
    # AUTHENTICATION OPERATIONS
    # ============================================================================

    @handle_service_errors("authenticate_user")
    @monitor_service_performance("authenticate_user")
    def authenticate_user(self, login_data: LoginRequestSchema) -> UserReadSchema:
        """Authenticate user with email and password.

        Args:
            login_data: Login credentials

        Returns:
            UserReadSchema: Authenticated user

        Raises:
            BusinessLogicError: If authentication fails

        """
        logger.debug(f"Authenticating user: {login_data.username}")

        # Get user by email (username is expected to be email)
        user = self.user_repo.get_by_email(login_data.username)
        if not user or not user.is_active:
            raise InvalidInputError("Invalid email or password")

        # Verify password
        if not user.password_hash or not pwd_context.verify(
            login_data.password, user.password_hash
        ):
            raise InvalidInputError("Invalid email or password")

        logger.info(f"User authenticated successfully: {user.id}")
        return UserReadSchema.model_validate(user)

    @handle_service_errors("generate_access_token")
    @monitor_service_performance("generate_access_token")
    def generate_access_token(self, user: UserReadSchema) -> str:
        """Generate JWT access token for user.

        Args:
            user: User data

        Returns:
            str: JWT access token

        """
        logger.debug(f"Generating access token for user: {user.id}")

        from datetime import datetime, timedelta, timezone

        from jose import jwt

        from src.config.settings import settings

        # Create JWT payload
        now = datetime.now(timezone.utc)
        expire = now + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)

        payload = {
            "sub": user.email,  # Subject (user ID)
            "email": user.email,
            "name": user.name,
            "iat": now,  # Issued at
            "exp": expire,  # Expiration time
            "type": "access_token",
        }

        # Sign the token with SECRET_KEY
        token = jwt.encode(
            payload, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
        )

        logger.debug(f"JWT access token generated for user: {user.id}")
        return token

    @handle_service_errors("login")
    @monitor_service_performance("login")
    def login(self, login_data: LoginRequestSchema) -> LoginResponseSchema:
        """Perform user login and return authentication response.

        Args:
            login_data: Login credentials

        Returns:
            LoginResponseSchema: Login response with token and user data

        Raises:
            BusinessLogicError: If authentication fails

        """
        logger.info(f"Login attempt for: {login_data.username}")

        # Authenticate user
        user = self.authenticate_user(login_data)

        # Generate access token
        access_token = self.generate_access_token(user)

        # Create login response
        response = LoginResponseSchema(
            access_token=access_token,
            token_type="bearer",  # nosec B106 - OAuth2 token type, not a password
            expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
            * 60,  # Convert to seconds
            user=user,
        )

        logger.info(f"Login successful for user: {user.id}")
        return response

    @handle_service_errors("logout")
    @monitor_service_performance("logout")
    def logout(self, user_id: int) -> LogoutResponseSchema:
        """Perform user logout.

        Args:
            user_id: User ID

        Returns:
            LogoutResponseSchema: Logout confirmation

        Note:
            In a real implementation, this would invalidate the user's tokens
            and clean up any session data.

        """
        logger.info(f"Logout for user: {user_id}")

        # In a real implementation, you would:
        # 1. Invalidate the user's access tokens
        # 2. Clean up session data
        # 3. Log the logout event

        from datetime import datetime, timezone

        logger.info(f"Logout successful for user: {user_id}")
        return LogoutResponseSchema(
            message="Successfully logged out", logged_out_at=datetime.now(timezone.utc)
        )

    # ============================================================================
    # PASSWORD MANAGEMENT OPERATIONS
    # ============================================================================

    @handle_service_errors("change_password")
    @monitor_service_performance("change_password")
    def change_password(
        self, user_id: int, password_data: PasswordChangeRequestSchema
    ) -> bool:
        """Change user password.

        Args:
            user_id: User ID
            password_data: Password change data

        Returns:
            bool: True if password changed successfully

        Raises:
            NotFoundError: If user not found
            BusinessLogicError: If current password is invalid

        """
        logger.info(f"Password change request for user: {user_id}")

        # Get user
        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        # Verify current password
        if not user.password_hash or not pwd_context.verify(
            password_data.current_password, user.password_hash
        ):
            raise InvalidInputError("Current password is incorrect")

        # Hash new password
        new_password_hash = pwd_context.hash(password_data.new_password)

        # Update password
        success = self.user_repo.update_password(user_id, new_password_hash)
        if success:
            self.db_session.commit()
            logger.info(f"Password changed successfully for user: {user_id}")
        else:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found",
            )

        return success

    # ============================================================================
    # USER PREFERENCES OPERATIONS
    # ============================================================================

    @handle_service_errors("get_user_preferences")
    @monitor_service_performance("get_user_preferences")
    def get_user_preferences(self, user_id: int) -> UserPreferenceReadSchema | None:
        """Get user preferences.

        Args:
            user_id: User ID

        Returns:
            Optional[UserPreferenceReadSchema]: User preferences or None if not found

        Raises:
            NotFoundError: If user not found

        """
        logger.debug(f"Retrieving preferences for user: {user_id}")

        # Validate user exists
        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        preferences = self.preference_repo.get_by_user_id(user_id)
        if preferences:
            return UserPreferenceReadSchema.model_validate(preferences)

        # Return default preferences structure instead of None
        logger.debug(
            f"No preferences found for user {user_id}, returning default structure"
        )

        # Create default preferences structure
        default_preferences = {
            "user_id": user_id,
            "theme": "light",
            "language": "en",
            "timezone": "UTC",
            "units": "metric",
            "notifications_enabled": True,
            "email_notifications": True,
            "dashboard_layout": "default",
            "calculation_precision": 2,
            "auto_save_enabled": True,
            "preferences_status": "default_applied",
            "message": "No custom preferences found, using system defaults",
        }

        return UserPreferenceReadSchema.model_validate(default_preferences)

    @handle_service_errors("create_or_update_user_preferences")
    @monitor_service_performance("create_or_update_user_preferences")
    def create_or_update_user_preferences(
        self, user_id: int, preferences_data: UserPreferenceUpdateSchema
    ) -> UserPreferenceReadSchema:
        """Create or update user preferences.

        Args:
            user_id: User ID
            preferences_data: Preferences data

        Returns:
            UserPreferenceReadSchema: Created or updated preferences

        Raises:
            NotFoundError: If user not found

        """
        logger.info(f"Creating/updating preferences for user: {user_id}")

        # Validate user exists
        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        # Create or update preferences
        preferences_dict = preferences_data.model_dump(exclude_unset=True)
        preferences = self.preference_repo.create_or_update_preferences(
            user_id, preferences_dict
        )
        self.db_session.commit()
        self.db_session.refresh(preferences)

        logger.info(f"Preferences updated successfully for user: {user_id}")
        return UserPreferenceReadSchema.model_validate(preferences)

    @handle_service_errors("delete_user_preferences")
    @monitor_service_performance("delete_user_preferences")
    def delete_user_preferences(
        self, user_id: int, deleted_by_user_id: int | None = None
    ) -> bool:
        """Soft delete user preferences.

        Args:
            user_id: User ID
            deleted_by_user_id: ID of user performing deletion

        Returns:
            bool: True if deleted successfully

        Raises:
            NotFoundError: If user not found

        """
        logger.info(f"Deleting preferences for user: {user_id}")

        # Validate user exists
        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        success = self.preference_repo.soft_delete_preferences(
            user_id, deleted_by_user_id
        )
        if success:
            self.db_session.commit()
            logger.info(f"Preferences deleted successfully for user: {user_id}")
        else:
            logger.debug(f"No preferences found for user: {user_id}")

        return success

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    @handle_service_errors("search_users")
    @monitor_service_performance("search_users")
    def search_users(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> list[UserReadSchema]:
        """Search users by name or email.

        Args:
            search_term: Search term
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[UserReadSchema]: List of matching users

        """
        logger.debug(f"Searching users with term: {search_term}")

        users = self.user_repo.search_users(search_term, skip, limit)
        return [UserReadSchema.model_validate(user) for user in users]

    @handle_service_errors("count_active_users")
    @monitor_service_performance("count_active_users")
    def count_active_users(self) -> int:
        """Count total number of active users.

        Returns:
            int: Number of active users

        """
        logger.debug("Counting active users")
        return self.user_repo.count_active_users()

    # ============================================================================
    # PASSWORD UTILITY METHODS
    # ============================================================================

    def hash_password(self, password: str) -> str:
        """Hash a plain text password using bcrypt.

        Args:
            password: Plain text password

        Returns:
            str: Hashed password

        """
        return pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a plain text password against a hashed password.

        Args:
            plain_password: Plain text password
            hashed_password: Hashed password

        Returns:
            bool: True if password matches, False otherwise

        """
        return pwd_context.verify(plain_password, hashed_password)

    @handle_service_errors("update_user_last_login")
    @monitor_service_performance("update_user_last_login")
    def update_user_last_login(self, user_id: int) -> bool:
        """Update user's last login timestamp with timezone-aware datetime.

        Args:
            user_id: User ID

        Returns:
            bool: True if updated successfully

        Raises:
            NotFoundError: If user not found

        """
        logger.debug(f"Updating last login for user: {user_id}")

        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        # Update with timezone-aware timestamp
        user.last_login_at = utcnow_aware()
        self.db_session.commit()

        logger.info(f"Last login updated for user: {user_id}")
        return True
