import json
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

from fastapi import status
from fastapi.testclient import TestClient
import pytest

from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.schemas.general.component_schemas import (
    ComponentCreateSchema,
    ComponentReadSchema,
    ComponentSearchSchema,
    ComponentUpdateSchema,
)

@pytest.fixture
def mock_component_service():
    """Mock component service for testing."""
    service = MagicMock()
    service.create_component = AsyncMock()
    service.get_component = AsyncMock()
    service.update_component = AsyncMock()
    service.delete_component = AsyncMock()
    service.search_components = AsyncMock()
    service.get_components_by_category = AsyncMock()
    service.get_components_by_type = AsyncMock()
    service.get_preferred_components = AsyncMock()
    service.get_component_stats = AsyncMock()
    return service
