# src/core/schemas/health.py
"""Health Check Schemas for Ultimate Electrical Designer.

This module provides Pydantic schemas for health check endpoints,
system monitoring, and service status reporting.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from src.core.schemas.base import BaseSchema


class DatabaseHealthSchema(BaseModel):
    """Schema for database health status."""

    status: str = Field(..., description="Database connection status")
    connection_responsive: bool = Field(
        ..., description="Whether database responds to queries"
    )
    connection_latency_ms: Optional[float] = Field(
        None, description="Database query latency in milliseconds"
    )
    pool_utilization: float = Field(
        ..., description="Connection pool utilization percentage"
    )
    pool_metrics: Dict[str, Any] = Field(
        ..., description="Detailed connection pool metrics"
    )
    health_score: int = Field(..., description="Overall database health score (0-10)")
    recommendations: List[str] = Field(
        default_factory=list, description="Health improvement recommendations"
    )
    database_type: str = Field(
        ..., description="Type of database (postgresql, sqlite, etc.)"
    )
    connection_error: Optional[str] = Field(
        None, description="Connection error message if any"
    )


class ServiceHealthSchema(BaseModel):
    """Schema for individual service health status."""

    name: str = Field(..., description="Service name")
    status: str = Field(
        ..., description="Service status (healthy, degraded, unhealthy)"
    )
    version: Optional[str] = Field(None, description="Service version")
    uptime_seconds: Optional[float] = Field(
        None, description="Service uptime in seconds"
    )
    last_check: datetime = Field(..., description="Last health check timestamp")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional service details"
    )


class SystemMetricsSchema(BaseModel):
    """Schema for system performance metrics."""

    memory_usage_mb: Optional[float] = Field(
        None, description="Current memory usage in MB"
    )
    cpu_usage_percent: Optional[float] = Field(
        None, description="Current CPU usage percentage"
    )
    disk_usage_percent: Optional[float] = Field(
        None, description="Current disk usage percentage"
    )
    active_connections: Optional[int] = Field(
        None, description="Number of active connections"
    )
    request_count_last_minute: Optional[int] = Field(
        None, description="Requests processed in last minute"
    )


class HealthCheckResponseSchema(BaseSchema):
    """Comprehensive health check response schema."""

    status: str = Field(
        ..., description="Overall system status (healthy, degraded, unhealthy)"
    )
    timestamp: datetime = Field(..., description="Health check timestamp")
    version: str = Field(..., description="Application version")
    environment: str = Field(
        ..., description="Current environment (development, production, etc.)"
    )
    uptime_seconds: float = Field(..., description="Application uptime in seconds")

    # Detailed health information
    database: DatabaseHealthSchema = Field(..., description="Database health status")
    services: List[ServiceHealthSchema] = Field(
        default_factory=list, description="Individual service health status"
    )
    system_metrics: Optional[SystemMetricsSchema] = Field(
        None, description="System performance metrics"
    )

    # Overall health indicators
    health_score: int = Field(..., description="Overall system health score (0-10)")
    critical_issues: List[str] = Field(
        default_factory=list, description="Critical issues requiring attention"
    )
    warnings: List[str] = Field(
        default_factory=list, description="Non-critical warnings"
    )

    # Additional context
    checks_performed: List[str] = Field(
        default_factory=list, description="List of health checks performed"
    )
    response_time_ms: Optional[float] = Field(
        None, description="Health check response time in milliseconds"
    )


class SimpleHealthResponseSchema(BaseSchema):
    """Simplified health check response for basic monitoring."""

    status: str = Field(..., description="System status (healthy, unhealthy)")
    timestamp: datetime = Field(..., description="Health check timestamp")
    version: str = Field(..., description="Application version")
    database_status: str = Field(..., description="Database connection status")
    uptime_seconds: float = Field(..., description="Application uptime in seconds")


__all__ = [
    "DatabaseHealthSchema",
    "ServiceHealthSchema",
    "SystemMetricsSchema",
    "HealthCheckResponseSchema",
    "SimpleHealthResponseSchema",
]
