# Workflows

- [Workflows](#workflows)
  - [Backend Workflow Definitions](#backend-workflow-definitions)
    - [1. Workflow: Implementing a New Module](#1-workflow-implementing-a-new-module)
    - [2. Workflow: Adding a New Element to an Existing Module](#2-workflow-adding-a-new-element-to-an-existing-module)
    - [3. Workflow: Updating an Existing Module](#3-workflow-updating-an-existing-module)
    - [4. Workflow: Updating an Existing Element](#4-workflow-updating-an-existing-element)
  - [General Workflows](#general-workflows)
    - [5. Workflow: Analyze the Codebase](#5-workflow-analyze-the-codebase)
    - [6. Workflow: Refactoring Code](#6-workflow-refactoring-code)
    - [7. Workflow: Increasing Test Coverage for a Specific Module](#7-workflow-increasing-test-coverage-for-a-specific-module)
    - [8. Workflow: Implementing a New Feature](#8-workflow-implementing-a-new-feature)
    - [9. Workflow: Enhancing an Existing Feature](#9-workflow-enhancing-an-existing-feature)
    - [10. Workflow: Fixing All Failing Tests](#10-workflow-fixing-all-failing-tests)
    - [11. Workflow: Fixing Failing Tests for a Specific Module](#11-workflow-fixing-failing-tests-for-a-specific-module)
    - [12. Workflow: Update Development Documentation](#12-workflow-update-development-documentation)
    - [13. Workflow: Update Main README.md](#13-workflow-update-main-readmemd)
  - [Frontend Workflow Definitions](#frontend-workflow-definitions)
    - [14. Workflow: Implementing a New Client Module (e.g., a new UI section or core feature component)](#14-workflow-implementing-a-new-client-module-eg-a-new-ui-section-or-core-feature-component)
    - [15. Workflow: Adding a New Client Element to an Existing Module (e.g., a new UI component or form field)](#15-workflow-adding-a-new-client-element-to-an-existing-module-eg-a-new-ui-component-or-form-field)
    - [16. Workflow: Updating an Existing Client Module (e.g., refactoring a major UI section)](#16-workflow-updating-an-existing-client-module-eg-refactoring-a-major-ui-section)
    - [17. Workflow: Updating an Existing Client Element (e.g., changing styling or behavior of a button)](#17-workflow-updating-an-existing-client-element-eg-changing-styling-or-behavior-of-a-button)

## Backend Workflow Definitions

The "Backend Workflow Definitions" are designed to ensure the AI Agents apply the correct context for backend development, focusing on **Python**, **FastAPI**, **SQLAlchemy**, **Ruff**, **MyPy**, **Pytest**, and the "5-Layer Architecture" for backend.

### 1. Workflow: Implementing a New Module

This workflow guides the AI Agent through the process of creating and integrating a completely new module into the system, aligning with the "Core API Implementation & Foundation Setup" phase.

* **1.1. Discovery & Analysis:**
    * **Task:** Identify the new module's purpose, define its clear scope, and analyze its required interactions and dependencies within the existing 5-layer architecture.
    * **Guidance for AI:** Review existing architectural diagrams and identify the appropriate layer for the new module (e.g., `src/api/`, `src/core/`, `src/repositories/`). Verify no existing functionality overlaps.
* **1.2. Task Planning:**
    * **Task:** Break down the module implementation into smaller, manageable sub-tasks using the "Task Planning Template". This includes defining data models (SQLAlchemy ORM models), API endpoints (FastAPI routes), core business logic, and repository interfaces.
    * **Guidance for AI:** Generate a detailed task list, estimating complexity and dependencies. Ensure adherence to "Unified Patterns" for proposed new components.
* **1.3. Implementation:**
    * **Task:** Develop the module's components according to the planned tasks, strictly adhering to "SOLID" principles, "Unified Patterns", and achieving full type safety (MyPy validation for Python).
    * **Guidance for AI:** Generate code for models, schemas, API routes, core logic, and repositories. Apply decorators for error handling, performance monitoring, and memory optimization as per "Unified Patterns". Ensure "Zero Tolerance Policies" for warnings/errors are met.
* **1.4. Verification:**
    * **Task:** Conduct comprehensive testing, including unit, integration, and performance tests, with a target of 100% code coverage for the newly implemented module. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate test cases (using Pytest for backend). Execute tests and verify "100% test pass rates". Analyze coverage reports and suggest additional tests if coverage is below 100% for the new code.
* **1.5. Documentation & Handover:**
    * **Task:** Update the "Developer Handbook" with details of the new module and prepare a comprehensive handover package for future development and AI agent transfer.
    * **Guidance for AI:** Generate or update relevant sections in the developer handbook, including API specifications. Ensure all public APIs are documented.

### 2. Workflow: Adding a New Element to an Existing Module

This workflow details the process of extending an existing module with new functionality or components.

* **2.1. Discovery & Analysis:**
    * **Task:** Understand the purpose and specific requirements of the new element. Analyze its impact on the existing module's structure, dependencies, and overall functionality.
    * **Guidance for AI:** Identify the precise location within the existing module for the new element. Ensure consistency with the module's current design patterns.
* **2.2. Task Planning:**
    * **Task:** Plan the element's addition, breaking it down into manageable units. Ensure the plan minimizes disruption to existing functionalities and adheres to the module's established patterns.
    * **Guidance for AI:** Update the relevant "Task Planning Template" to incorporate the new element's implementation, testing, and documentation.
* **2.3. Implementation:**
    * **Task:** Implement the new element, extending existing unified patterns and maintaining complete type safety (MyPy validation).
    * **Guidance for AI:** Generate code for the new element. Ensure "Zero Tolerance Policies" for warnings/errors are strictly met.
* **2.4. Verification:**
    * **Task:** Update existing tests and add new tests specifically for the new element, maintaining 100% code coverage for the changes. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate new test cases and execute the module's test suite. Verify that all tests pass and that code coverage for the modified areas is 100%.
* **2.5. Documentation & Handover:**
    * **Task:** Update module-specific documentation and prepare for AI agent transfer.
    * **Guidance for AI:** Generate or update the relevant sections within the module's documentation, describing the new element's functionality and usage.

### 3. Workflow: Updating an Existing Module

This workflow focuses on modifying or enhancing an already implemented module.

* **3.1. Discovery & Analysis:**
    * **Task:** Analyze the current functionality of the module and identify specific areas that require updates or refinement. Consider any internal or external dependencies affected by the update.
    * **Guidance for AI:** Review existing module documentation and code to understand the scope of the update. Identify potential impacts on other system components.
* **3.2. Task Planning:**
    * **Task:** Break down the update tasks into smaller units, prioritizing non-breaking changes, performance improvements, or bug fixes. Utilize the "Task Planning Template".
    * **Guidance for AI:** Create a detailed plan for modifying the module, ensuring adherence to the "5-Phase Methodology".
* **3.3. Implementation:**
    * **Task:** Apply the necessary updates to the module, ensuring continued adherence to the 5-layer architecture and "unified patterns". Address any identified technical debt.
    * **Guidance for AI:** Generate code modifications. Ensure all "Zero Tolerance Policies" for warnings, errors, and technical debt are adhered to.
* **3.4. Verification:**
    * **Task:** Rerun all relevant tests for the module and add new tests for updated functionalities. Ensure zero warnings/errors are introduced. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Execute unit and integration tests for the module. Verify "100% test pass rates" and analyze code quality metrics to ensure no regressions.
* **3.5. Documentation & Handover:**
    * **Task:** Update the module documentation and any relevant calculation reports.
    * **Guidance for AI:** Generate or update documentation reflecting the changes and improvements made to the module.

### 4. Workflow: Updating an Existing Element

This workflow addresses the process of modifying a specific component or feature within an existing module.

* **4.1. Discovery & Analysis:**
    * **Task:** Pinpoint the specific element within the module that needs updating. Clearly understand its current role and the precise scope of the required modification.
    * **Guidance for AI:** Review the element's existing code and documentation to identify dependencies and potential side effects of the update.
* **4.2. Task Planning:**
    * **Task:** Plan the update focusing on isolated changes to minimize side effects and potential regressions.
    * **Guidance for AI:** Create a mini-plan for the element, detailing modifications, required tests, and documentation updates.
* **4.3. Implementation:**
    * **Task:** Modify the element's code, maintaining existing design principles, "unified patterns", and full type safety.
    * **Guidance for AI:** Generate code changes for the specific element. Ensure no new warnings or errors are introduced.
* **4.4. Verification:**
    * **Task:** Conduct targeted testing on the updated element and relevant components within its module. Ensure no regressions are introduced.
    * **Guidance for AI:** Execute specific unit tests related to the element. Confirm "100% test pass rates" and no degradation in "Code Quality".
* **4.5. Documentation & Handover:**
    * **Task:** Update specific element documentation within the module to reflect the changes.
    * **Guidance for AI:** Generate or update documentation detailing the modifications to the element.

## General Workflows

The "General Workflows" incorporates both backend and frontend tools and considerations. The workflows are designed to ensure the AI Agents apply the correct context regardless of whether the task is backend or frontend focused.

Here are the suggested modifications to make them fully comprehensive for both backend and frontend development:

### 5. Workflow: Analyze the Codebase

This workflow outlines a continuous analysis process for maintaining code quality, security, and adherence to standards across both backend and frontend. This is primarily an AI-driven task within the "Verification" phase or as a standalone quality gate.

* **5.1. Discovery & Analysis:**
    * **Task:** Define the specific scope of the analysis (e.g., a specific module, recent changes, entire codebase).
    * **Guidance for AI:** Access the relevant code base sections for both backend (Python) and frontend (TypeScript/JavaScript).
* **5.2. Task Planning:**
    * **Task:** Outline the specific checks and metrics to be applied for the analysis.
    * **Guidance for AI:** Prepare the necessary tools and scripts for linting, type checking, security scanning, and test coverage analysis for both backend and frontend environments.
* **5.3. Implementation (Analysis Execution):**
    * **Task:** Perform automated analysis of the codebase for:
        * **Code duplication:** Identify repetitive code for "DRY" principle violations across Python and TypeScript.
        * **Adherence to coding standards:** Check against "Development Standards", including "SOLID" principles, "Unified Patterns", and engineering-grade quality for both backend and frontend. This includes **ESLint/Prettier** for frontend.
        * **Security vulnerabilities:** Scan for potential issues like insecure API endpoints, database injection risks, or JWT vulnerabilities (backend), as well as XSS, CSRF, and secure token storage issues (frontend).
        * **Code quality:** Assess against "Technical Debt & Quality Metrics" such as "Linting Status (Zero warnings/errors)" and "Type Coverage (100% type annotations)" for both Python (MyPy) and TypeScript.
        * **Maintainability:** Evaluate code readability, complexity, and modularity in alignment with the "5-layer architecture" for backend and **Component-Based Architecture (Atomic Design Methodology)** for frontend.
        * **Performance:** Identify potential bottlenecks, inefficient queries, or unoptimized algorithms (backend), and **Core Web Vitals** issues, bundle size, or rendering performance (frontend).
        * **Documentation:** Verify all public APIs are documented and internal code comments are sufficient, aiming for "All public APIs documented" using tools like **JSDoc/TypeDoc** for frontend.
    * **Guidance for AI:** Execute static analysis tools (e.g., **Ruff, MyPy** for Python; **ESLint, TypeScript checking** for frontend), security scanners, and custom scripts. Analyze bundle sizes and performance metrics. Generate comprehensive reports for all layers.
* **5.4. Verification (Reporting & Flagging):**
    * **Task:** Generate detailed reports of findings, highlighting violations, vulnerabilities, or areas needing improvement. Flag critical issues for immediate attention.
    * **Guidance for AI:** Summarize findings, categorize issues by severity (e.g., error, warning, suggestion), and link directly to relevant code sections and project standards for both backend and frontend. Ensure "Zero Tolerance Policies" are met, or issues are clearly flagged.
* **5.5. Documentation & Handover:**
    * **Task:** Document the analysis results and propose actionable recommendations for remediation.
    * **Guidance for AI:** Create an analysis report and, if applicable, generate tasks for remediation, linking back to the relevant development workflow (e.g., "Refactoring Code", "Fixing Failing Tests").

### 6. Workflow: Refactoring Code

This workflow defines the process for improving the internal structure of code without changing its external behavior, directly supporting the "Zero Tolerance Policies" for technical debt, applicable to both backend and frontend.

* **6.1. Discovery & Analysis:**
    * **Task:** Identify specific areas of the codebase that require refactoring (e.g., complex functions, low cohesion, high coupling, code smells) in either backend Python or frontend TypeScript/React code. This can be triggered by "Analyze the Codebase" workflow.
    * **Guidance for AI:** Review code complexity metrics, linting reports, and design patterns to pinpoint refactoring candidates across the entire codebase.
* **6.2. Task Planning:**
    * **Task:** Plan the refactoring process into small, isolated, and verifiable steps to minimize risk and ensure stability for both backend and frontend components.
    * **Guidance for AI:** Create a granular plan, prioritizing changes that yield the most significant improvements in maintainability or performance without introducing regressions, considering impact on both sides.
* **6.3. Implementation:**
    * **Task:** Apply refactoring techniques (e.g., extract method, rename variable, introduce parameter object, re-structure components, extract hooks), ensuring existing functionality is maintained and adhering to project coding standards like "SOLID" principles, "Unified Patterns", and **Component-Based Architecture**.
    * **Guidance for AI:** Generate modified code for either backend (Python) or frontend (TypeScript/React). Ensure type safety is preserved and no new warnings/errors are introduced by linting tools (**Ruff, ESLint**).
* **6.4. Verification:**
    * **Task:** Conduct comprehensive testing (unit, integration) for the refactored parts, specific to their domain (backend or frontend), to ensure no regressions are introduced and that quality metrics (e.g., linting, type coverage, code coverage) are improved or maintained.
    * **Guidance for AI:** Execute all relevant tests (**pytest** for backend, **Vitest/React Testing Library/Playwright** for frontend) before and after refactoring to confirm behavior remains unchanged. Verify adherence to "100% test pass rates".
* **6.5. Documentation & Handover:**
    * **Task:** Document the changes made during refactoring and provide the rationale behind them.
    * **Guidance for AI:** Update relevant sections of the "Developer Handbook", "Frontend Specification", or inline code comments, explaining the refactored design for both backend and frontend components.

### 7. Workflow: Increasing Test Coverage for a Specific Module

This workflow aims to improve the test coverage of a designated module, whether backend or frontend, directly supporting the project's "Target 100% for new implementations" and "extensive test coverage (≥85%)" metrics.

* **7.1. Discovery & Analysis:**
    * **Task:** Identify the specific module (backend Python or frontend TypeScript) and pinpoint areas within it with low test coverage using test coverage reports.
    * **Guidance for AI:** Analyze `pytest --cov=src --cov-report=html` output for Python backend or `npm run test -- --coverage` for frontend to highlight uncovered lines or branches.
* **7.2. Task Planning:**
    * **Task:** Plan the creation of new tests (unit, integration, E2E) to cover the identified uncovered code paths and functionalities, specific to the module's technology.
    * **Guidance for AI:** Create a detailed test plan, specifying the types of tests needed and the functionalities to be covered for the given module (e.g., backend API tests, frontend component tests, Playwright E2E scenarios).
* **7.3. Implementation:**
    * **Task:** Write new tests using **Pytest** for the backend or **Vitest/React Testing Library** for frontend unit/integration tests, and **Playwright** for frontend E2E tests. Ensure these tests adhere to best practices for testability and maintainability.
    * **Guidance for AI:** Generate test code that effectively exercises the uncovered parts of the module. For frontend, ensure tests account for state management (**React Query/Zustand**) and component interactions. Use **MSW** for frontend API mocking.
* **7.4. Verification:**
    * **Task:** Run the newly added tests along with the existing test suite for the module. Verify that the test coverage has increased as planned and that all tests pass.
    * **Guidance for AI:** Execute `poetry run pytest --cov=src` for backend or `npm run test` / `npx playwright test` for frontend and review the coverage report to confirm improvement towards the target.
* **7.5. Documentation & Handover:**
    * **Task:** Document the newly added tests, including their purpose and the specific functionalities they cover.
    * **Guidance for AI:** Update test documentation, "Developer Handbook", or inline code comments to reflect the expanded test suite.

### 8. Workflow: Implementing a New Feature

This workflow guides the AI Agent through the end-to-end process of developing a completely new feature, involving both backend and frontend components.

* **8.1. Discovery & Analysis:**
    * **Task:** Gain a comprehensive understanding of the new feature's requirements, user stories, and its overall impact on the system. Define clear objectives and scope for both backend and frontend parts.
    * **Guidance for AI:** Analyze user stories and functional specifications. Map the feature's components to the appropriate layers of the **5-layer architecture** (backend) and **Component-Based Architecture** (frontend). Identify necessary data models, API endpoints, core logic, UI components, and state management.
* **8.2. Task Planning:**
    * **Task:** Break down the feature into epics and smaller, manageable stories, following the "5-Phase Methodology" for each sub-task. Utilize the "Task Planning Template".
    * **Guidance for AI:** Generate a detailed project plan for the feature, including dependencies between backend and frontend sub-tasks and estimated timelines. Ensure all sub-tasks align with project standards (e.g., TDD, Unified Patterns, Atomic Design).
* **8.3. Implementation:**
    * **Task:** Develop the feature's components across all relevant layers (API, core logic, repositories, frontend, CAD integration), ensuring strict adherence to "SOLID" principles, "Unified Patterns", "complete type safety" (MyPy validation for Python, TypeScript for frontend), and "engineering-grade quality".
    * **Guidance for AI:** Generate code for backend services (**FastAPI, SQLAlchemy, NumPy, SciPy, Pandas**), frontend components (**Next.js, TypeScript, React Query, Zustand, Tailwind CSS, shadcn/ui**), and any necessary CAD integration (AutoCAD .NET API). Apply decorators for error handling, performance, and memory optimization. Ensure "Zero Tolerance Policies" for warnings/errors across the entire stack.
* **8.4. Verification:**
    * **Task:** Conduct comprehensive testing for the new feature, including unit, integration, performance, and end-to-end tests across both backend and frontend. Aim for 100% code coverage for all new implementations. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate and execute test cases (**pytest** for backend, **Vitest/React Testing Library/Playwright** for frontend). Verify "100% test pass rates" and analyze code coverage reports to ensure targets are met. Conduct compliance verification against relevant IEEE/IEC/EN standards. Perform **Core Web Vitals** performance audits for the frontend.
* **8.5. Documentation & Handover:**
    * **Task:** Create comprehensive feature documentation, including API specifications, user guides, frontend component documentation, and any relevant calculation reports. Prepare a complete handover package for future development and AI agent transfer.
    * **Guidance for AI:** Generate or update the "Developer Handbook", "Frontend Specification", and "Professional Documentation" sections. Ensure "engineering-grade precision" and clarity across all documentation.

### 9. Workflow: Enhancing an Existing Feature

This workflow outlines the process for improving or extending an already implemented feature, involving both backend and frontend components.

* **9.1. Discovery & Analysis:**
    * **Task:** Evaluate the current feature's backend logic and frontend UI/UX. Understand its existing functionality and identify the precise scope of the enhancement. Analyze potential dependencies and impacts on other system parts across both layers.
    * **Guidance for AI:** Review existing feature documentation, backend code, frontend code, user feedback, and performance metrics to define the enhancement requirements clearly.
* **9.2. Task Planning:**
    * **Task:** Plan the enhancements, breaking them into manageable steps for both backend and frontend. Prioritize changes that ensure backward compatibility and improve performance.
    * **Guidance for AI:** Update the "Task Planning Template" to detail the enhancement implementation, testing, and documentation for both backend services and frontend components.
* **9.3. Implementation:**
    * **Task:** Implement the enhancements across relevant backend services and frontend components, strictly adhering to established coding standards, "unified patterns", "complete type safety", and "SOLID" principles.
    * **Guidance for AI:** Modify existing code or add new components as required in both backend (Python) and frontend (TypeScript/React). Ensure "Zero Tolerance Policies" for warnings/errors and technical debt are maintained across the full stack.
* **9.4. Verification:**
    * **Task:** Conduct extensive regression testing to ensure the existing functionality remains intact across backend APIs and frontend UI. Add new tests for the enhanced functionalities, contributing to 100% coverage for changes. Ensure zero security vulnerabilities are introduced.
    * **Guidance for AI:** Execute the feature's existing test suite and newly generated tests for both backend (**pytest**) and frontend (**Vitest/React Testing Library/Playwright**). Verify "100% test pass rates" and perform security scans if applicable. Use the "Quality Assurance Checklist". Conduct frontend performance audits.
* **9.5. Documentation & Handover:**
    * **Task:** Update the feature's documentation (both backend and frontend parts) and any relevant user guides to reflect the enhancements.
    * **Guidance for AI:** Generate or update documentation, ensuring clarity on new functionalities and changes to existing ones across the entire feature.

### 10. Workflow: Fixing All Failing Tests

This workflow defines the procedure for addressing and resolving all identified test failures across the entire project, ensuring "100% test pass rates" for both backend and frontend tests.

* **10.1. Discovery & Analysis:**
    * **Task:** Isolate all failing tests and identify the root cause of each failure across backend (Python) and frontend (TypeScript) tests. This may involve reviewing recent code changes, build logs, and test reports.
    * **Guidance for AI:** Access and analyze the full test report from all test runners (e.g., **pytest**, **Vitest**, **Playwright**). Correlate failing tests with recent commits or known issues across the entire codebase.
* **10.2. Task Planning:**
    * **Task:** Prioritize the fixes, starting with critical or foundational failures. Plan the necessary code changes to resolve each identified test failure, specifying whether it's a backend or frontend fix.
    * **Guidance for AI:** Create a prioritized list of failing tests and propose a remediation plan, ideally grouping related fixes by domain (backend/frontend).
* **10.3. Implementation:**
    * **Task:** Implement the necessary code changes to resolve the test failures in the respective backend or frontend codebases. Ensure fixes adhere to "Development Standards" and do not introduce new issues.
    * **Guidance for AI:** Generate targeted code modifications for the specific failing tests. Apply "Zero Tolerance Policies" for any new warnings, errors, or technical debt introduced by the fix in either environment.
* **10.4. Verification:**
    * **Task:** Rerun *all* project tests (backend and frontend) to ensure comprehensive test pass rates are achieved and no new regressions have been introduced.
    * **Guidance for AI:** Execute the entire test suite (**`poetry run pytest`** for backend, **`npm run test`** and **`npx playwright test`** for frontend). Confirm that all tests now pass and the project achieves "100% test pass rates". Use the "Quality Assurance Checklist" to verify overall quality.
* **10.5. Documentation & Handover:**
    * **Task:** Document the root cause of the failures, the implemented fix, and its impact.
    * **Guidance for AI:** Update relevant issue trackers (GitHub Issues) and internal documentation to detail the resolution, specifying whether the fix was backend or frontend related.

### 11. Workflow: Fixing Failing Tests for a Specific Module

This workflow provides a more granular approach to resolving test failures, focusing on a particular backend or frontend module.

* **11.1. Discovery & Analysis:**
    * **Task:** Identify the failing tests specifically within the designated module (backend Python or frontend TypeScript/React) and determine their root causes.
    * **Guidance for AI:** Access the test report filtered by module (e.g., `pytest tests/path/to/backend_module/` or `npm run test src/modules/frontend_module/`). Analyze logs and code related to the module's failures.
* **11.2. Task Planning:**
    * **Task:** Plan the fix within the module's context, focusing on isolated changes to resolve the specific test failures.
    * **Guidance for AI:** Create a targeted plan for code modifications and testing within the module, considering its specific technology stack (Python/FastAPI or TypeScript/React).
* **11.3. Implementation:**
    * **Task:** Implement code changes specific to the module to resolve the test failures. Ensure adherence to the module's existing design and "unified patterns" (backend) or **Component-Based Architecture** (frontend).
    * **Guidance for AI:** Generate precise code modifications for the module. Ensure no new warnings or errors are introduced by linting/type-checking tools specific to the module's language.
* **11.4. Verification:**
    * **Task:** Rerun the module-specific tests and relevant integration tests to ensure the fixes are effective and no regressions are introduced within the module or its immediate dependencies.
    * **Guidance for AI:** Execute the specific module's test suite (e.g., `pytest` for backend, `vitest` or `playwright` for frontend). Confirm "100% test pass rates" for the module.
* **11.5. Documentation & Handover:**
    * **Task:** Document the fix for the module, including the specific tests that were failing and how they were resolved.
    * **Guidance for AI:** Update the module's documentation or related issue entries with details of the fix.

### 12. Workflow: Update Development Documentation

This workflow outlines the process for maintaining and updating the project's development documentation, ensuring "Comprehensive documentation" and contributing to "Documentation Coverage" across both backend and frontend. This includes resources like the Developer Handbook, Backend Development, Frontend Specification, etc.

* **12.1. Discovery & Analysis:**
    * **Task:** Identify outdated, incomplete, or missing sections within the development documentation. This can be triggered by new features, bug fixes, refactoring, or a review of the "Documentation Coverage" metric for both backend and frontend.
    * **Guidance for AI:** Review recent code changes, new feature implementations, and project discussions to pinpoint areas where documentation updates are necessary. Refer to the "Developer Handbook", "Backend Development", and "Frontend Specification" to understand existing structure.
* **12.2. Task Planning:**
    * **Task:** Plan the scope of the documentation update, outlining specific sections or documents that require modification or creation for either backend or frontend.
    * **Guidance for AI:** Create a detailed plan for the documentation update, specifying the content to be added, modified, or removed, and cross-referencing with relevant code changes in both Python and TypeScript.
* **12.3. Implementation:**
    * **Task:** Write or update the documentation, ensuring it is accurate, clear, and adheres to the project's standard for "engineering-grade precision". This includes updating API documentation, component guides, architectural explanations, and any domain-specific documentation.
    * **Guidance for AI:** Generate or modify markdown files (`.md`) within the `docs/` directory. Ensure all technical details are correct, consistent with the code, and easy to understand for other developers and future AI agents. Maintain "100% type coverage" for code examples within documentation. Use **JSDoc/TypeDoc** for inline frontend documentation.
* **12.4. Verification:**
    * **Task:** Review the updated documentation for accuracy, clarity, completeness, and adherence to documentation standards. This may involve a peer review or automated checks for broken links.
    * **Guidance for AI:** Conduct a self-review of the generated documentation. Cross-reference against the actual codebase (backend and frontend) and functional specifications to ensure consistency.
* **12.5. Documentation & Handover:**
    * **Task:** Publish the updated documentation and ensure it is accessible to the development team and other AI agents.
    * **Guidance for AI:** Commit the changes to the documentation repository. Ensure the updated documentation is part of the handover package for relevant features.

### 13. Workflow: Update Main README.md

This workflow defines the process for keeping the project's primary overview document, `README.md`, current with the latest project status, features, and key information, ensuring it reflects the "Current Project Phase" and "Revision History" for both backend and frontend.

* **13.1. Discovery & Analysis:**
    * **Task:** Review the `README.md` file for any outdated information, including project status, feature lists, technology stack versions (backend and frontend), or instructions that no longer apply based on recent sprints or major changes.
    * **Guidance for AI:** Compare the content of the `README.md` with the "Current Project Phase" (implemented/verified, in-progress, planned, not started sections), "Key Features", "Technology Stack", and "Technical Debt & Quality Metrics" sections to identify discrepancies across the full stack.
* **13.2. Task Planning:**
    * **Task:** Plan the specific updates required for the `README.md`, outlining which sections need modification.
    * **Guidance for AI:** Create a list of `README.md` sections to update (e.g., Development Status Overview, Active Development Areas, Key Features, Technology Stack, Technical Debt & Quality Metrics, Revision History). Ensure the technology stack lists all relevant versions for both backend and frontend (e.g., **FastAPI-0.115+**, **Next.js-15.3+**, **.NET 8.0+**, **Python 3.13+**).
* **13.3. Implementation:**
    * **Task:** Update the relevant sections of the `README.md` to accurately reflect the current project state, recent achievements, upcoming plans, and any changes to key information for both backend and frontend.
    * **Guidance for AI:** Modify the markdown content of `README.md`. Ensure clarity, conciseness, and accuracy. Update version numbers for all listed technologies.
* **13.4. Verification:**
    * **Task:** Verify that all updates align with the actual project state, documented changes, and project vision. Ensure no new errors or inconsistencies are introduced.
    * **Guidance for AI:** Cross-reference the updated `README.md` against other primary project documentation (e.g., sprint reports, architectural diagrams, frontend specification) to confirm consistency across the entire project.
* **13.5. Documentation & Handover:**
    * **Task:** Commit the updated `README.md` to the main branch.
    * **Guidance for AI:** Ensure the changes are committed with a conventional commit message that clearly indicates the nature of the update (e.g., `docs: update README with latest project status and frontend spec details`).

## Frontend Workflow Definitions

The "Frontend Workflow Definitions" are designed to ensure the AI Agents apply the correct context for frontend development, focusing on **Next.js**, **React**, **TypeScript**, **Tailwind CSS**, **Zustand**, **React Query**, and **Atomic Design Methodology**.

### 14. Workflow: Implementing a New Client Module (e.g., a new UI section or core feature component)

This workflow guides the AI Agent through creating and integrating a new, significant client-side module or feature section, such as a new dashboard view or a complex interactive form, adhering to **Domain-Driven Design (DDD)** principles for `src/modules`.

* **14.1. Discovery & Analysis:**
    * **Task:** Comprehensively understand the new module's purpose, define its clear scope (e.g., UI components, state management, data fetching logic), and analyze its required interactions with the backend API and other frontend modules.
    * **Guidance for AI:** Review mockups/wireframes and functional specifications. Determine the appropriate React component structure, state management needs (Zustand for client state, React Query for server state), and data fetching strategy. Identify necessary API endpoints, ensuring type safety via **OpenAPI Generator**-derived clients. Map components to the **Atomic Design Methodology** hierarchy.
* **14.2. Task Planning:**
    * **Task:** Break down the module implementation into smaller, manageable sub-tasks using the "Task Planning Template". This includes designing UI components, setting up state management, defining data fetching logic, and routing.
    * **Guidance for AI:** Generate a detailed task list. Ensure the plan aligns with Next.js best practices for server/client components, component reusability, and accessibility standards. Leverage "Code Generation" tools like **Hygen** or **Plop** to scaffold new components, hooks, or contexts.
* **14.3. Implementation:**
    * **Task:** Develop the client module's components strictly adhering to React/Next.js best practices, **SOLID principles** (for component design), **DRY**, **KISS**, "complete type safety" (**TypeScript**), and utilizing **Tailwind CSS** for styling, with **CSS Modules** for complex cases. Integrate with **React Query** for server state and **Zustand** for global/local UI state, distinguishing clearly between them.
    * **Guidance for AI:** Generate React components (JSX/TSX), Tailwind CSS classes, Zustand stores, and React Query hooks. Ensure "Zero Tolerance Policies" for warnings/errors from **ESLint** and **Prettier** are met, utilizing pre-commit hooks. Implement **immutability practices** for state management. Use "Unified Patterns" for data access and error handling.
* **14.4. Verification:**
    * **Task:** Conduct comprehensive testing, including unit tests (**Vitest**/React Testing Library), integration tests, and end-to-end tests (**Playwright**), ensuring "100% code coverage for new implementations". Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate test cases. Execute `npm run test` (Vitest/React Testing Library) and `npx playwright test`. Use **MSW (Mock Service Worker)** for API mocking. Verify "100% test pass rates" and analyze coverage reports. Perform visual regression testing if applicable. Ensure adherence to **Core Web Vitals** through performance analysis.
* **14.5. Documentation & Handover:**
    * **Task:** Update the "Frontend Specification" and "Developer Handbook" with details of the new client module. Prepare a comprehensive handover package for future development and AI agent transfer.
    * **Guidance for AI:** Generate or update documentation for new components, hooks, and state management logic using **JSDoc/TypeDoc**. Ensure clear usage examples and API definitions for components. Update `README.md` if the module significantly impacts project overview.

### 15. Workflow: Adding a New Client Element to an Existing Module (e.g., a new UI component or form field)

This workflow details the process of extending an existing client module with new UI components or specific functionalities, leveraging **shadcn/ui** where appropriate.

* **15.1. Discovery & Analysis:**
    * **Task:** Understand the purpose and specific requirements of the new client element. Analyze its impact on the existing module's UI, state, and interaction patterns, considering accessibility and responsiveness.
    * **Guidance for AI:** Identify the precise location within the existing client module (e.g., within a specific React component) for the new element. Ensure consistency with the module's current design system and user experience.
* **15.2. Task Planning:**
    * **Task:** Plan the element's addition, breaking it down into manageable units. Ensure the plan minimizes disruption to existing UI functionalities and adheres to the module's established patterns and design system.
    * **Guidance for AI:** Update the relevant "Task Planning Template" to incorporate the new element's UI implementation, state integration (React Query or Zustand), testing, and documentation.
* **15.3. Implementation:**
    * **Task:** Implement the new client element, extending existing React component patterns, utilizing **Tailwind CSS**, and maintaining "complete type safety" (**TypeScript**). Leverage **shadcn/ui** for base components where applicable.
    * **Guidance for AI:** Generate code for the new UI component or logic. Ensure "Zero Tolerance Policies" for warnings/errors are strictly met. Apply **Advanced Component Composition Patterns** like Render Props or Compound Components if the element requires highly flexible or implicitly contextual behavior.
* **15.4. Verification:**
    * **Task:** Update existing tests and add new tests specifically for the new element using **Vitest**/React Testing Library. Conduct targeted **Playwright** tests for UI interaction. Maintain 100% code coverage for the changes. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate new test cases and execute the module's client-side test suite. Verify that all tests pass and that code coverage for the modified areas is 100%.
* **15.5. Documentation & Handover:**
    * **Task:** Update module-specific documentation within the "Frontend Specification" and prepare for AI agent transfer.
    * **Guidance for AI:** Generate or update the relevant sections within the module's frontend documentation, describing the new element's functionality, props, and usage using **JSDoc/TypeDoc**.

### 16. Workflow: Updating an Existing Client Module (e.g., refactoring a major UI section)

This workflow focuses on modifying or enhancing an already implemented client module, prioritizing "Performance Optimization".

* **16.1. Discovery & Analysis:**
    * **Task:** Analyze the current functionality and UI of the client module and identify specific areas that require updates, redesign, or refinement. Consider any internal (state, components) or external (API interactions) dependencies affected by the update.
    * **Guidance for AI:** Review existing module code, design specifications, and user feedback. Identify potential impacts on user experience and performance. Analyze bundle size using tools like `next-bundle-analyzer` if performance is a concern.
* **16.2. Task Planning:**
    * **Task:** Break down the update tasks into smaller units, prioritizing non-breaking UI changes, performance improvements, or bug fixes. Utilize the "Task Planning Template".
    * **Guidance for AI:** Create a detailed plan for modifying the client module, ensuring adherence to the "5-Phase Methodology" and considering accessibility and responsiveness. Plan for **Memoization** (React.memo, useMemo, useCallback) or **Virtualization** for performance.
* **16.3. Implementation:**
    * **Task:** Apply the necessary updates to the client module, ensuring continued adherence to component best practices, "unified patterns" (for data fetching/state), Tailwind CSS, and "complete type safety" (**TypeScript**). Address any identified "technical debt" in the frontend.
    * **Guidance for AI:** Generate code modifications for React components, styles, state logic, or data fetching. Ensure all "Zero Tolerance Policies" for warnings, errors, and technical debt are adhered to. Focus on "Pure Functions" for utilities.
* **16.4. Verification:**
    * **Task:** Rerun all relevant client-side tests for the module (unit, integration, E2E) and add new tests for updated functionalities. Ensure zero warnings/errors are introduced and UI regressions are prevented. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Execute **Vitest**/React Testing Library and **Playwright** tests for the module. Verify "100% test pass rates" and analyze code quality metrics. Perform visual regression tests. Conduct performance audits to ensure **Core Web Vitals** are optimized.
* **16.5. Documentation & Handover:**
    * **Task:** Update the client module's documentation within the "Frontend Specification" and any relevant user-facing guides.
    * **Guidance for AI:** Generate or update documentation reflecting the changes and improvements made to the client module's UI, state, and interactions.

### 17. Workflow: Updating an Existing Client Element (e.g., changing styling or behavior of a button)

This workflow addresses the process of modifying a specific UI component or its behavior within an existing client module, emphasizing **Props-Based Customization** and **Accessibility**.

* **17.1. Discovery & Analysis:**
    * **Task:** Pinpoint the specific client element within the module that needs updating. Clearly understand its current behavior, styling, and the precise scope of the required modification.
    * **Guidance for AI:** Review the element's existing component code, styling definitions (**Tailwind CSS**), and usage context to identify dependencies and potential side effects of the update. Consider implications for "Responsiveness" and "Accessibility".
* **17.2. Task Planning:**
    * **Task:** Plan the update focusing on isolated changes to minimize side effects and potential UI regressions.
    * **Guidance for AI:** Create a mini-plan for the element, detailing modifications, required tests, and documentation updates.
* **17.3. Implementation:**
    * **Task:** Modify the element's code (JSX/TSX), styling (**Tailwind CSS**), or associated logic, maintaining existing design principles, accessibility, and "complete type safety" (**TypeScript**). Leverage **Props-Based Customization** where appropriate.
    * **Guidance for AI:** Generate code changes for the specific client element. Ensure no new warnings or errors are introduced from ESLint/Prettier.
* **17.4. Verification:**
    * **Task:** Conduct targeted testing on the updated client element using **Vitest**/React Testing Library for unit tests and **Playwright** for functional/visual regression tests. Ensure no regressions are introduced.
    * **Guidance for AI:** Execute specific unit tests related to the element. Confirm "100% test pass rates" and no degradation in "Code Quality".
* **17.5. Documentation & Handover:**
    * **Task:** Update specific element documentation within the "Frontend Specification" to reflect the changes.
    * **Guidance for AI:** Generate or update documentation detailing the modifications to the client element's behavior, props, or styling using **JSDoc/TypeDoc**.