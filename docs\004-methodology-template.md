# Systematic Testing Approach Template - Ultimate Electrical Designer

**Template Version:** 1.0  
**Created:** 2025-07-03  
**For:** Future AI Agents  
**Project:** Ultimate Electrical Designer Backend  

## 5-Phase Methodology Template

### Phase 1: Discovery & Analysis (30-45 minutes)

#### Step 1.1: Execute Comprehensive Test Suite
```bash
# Navigate to backend directory
cd d:\Projects\ultimate-electrical-designer\backend

# Run full test suite with detailed output
.\make test

# Capture results for analysis
# Expected output: test_logs/full/test_full_YYYYMMDD_HHMMSS.log
```

#### Step 1.2: Analyze Failure Patterns
```bash
# Search for specific failure types
grep -E "FAILED|ERROR" test_logs/full/test_full_*.log | head -20

# Categorize failures by type:
# - Database constraint failures (NOT NULL, foreign key)
# - Service layer errors (missing methods, wrong decorators)
# - Validation errors (missing validation, wrong error types)
# - Integration errors (import failures, dependency issues)
```

#### Step 1.3: Use Codebase Retrieval for Context
```
# Use codebase-retrieval tool to understand:
# 1. Current implementation structure
# 2. Test expectations vs actual implementation
# 3. Dependencies and relationships
# 4. Unified patterns compliance status
```

#### Step 1.4: Document Root Causes
```markdown
## Failure Analysis Summary
- **Total Tests**: X passed, Y failed, Z errors
- **Coverage**: X% (target: 85%+)
- **Critical Categories**:
  1. Database Schema (X failures)
  2. Service Layer (Y failures)  
  3. Validation (Z failures)
  4. Integration (W failures)
```

### Phase 2: Task Planning (15-30 minutes)

#### Step 2.1: Prioritize by Dependency Order
```
Critical Priority (Fix First):
- Database layer issues (schema, constraints)
- Repository layer (@handle_repository_errors)

High Priority (Fix Second):  
- Service layer (@handle_service_errors)
- Core calculation methods

Medium Priority (Fix Third):
- API layer validation
- Error handling patterns

Low Priority (Fix Last):
- Performance monitoring
- Utility functions
```

#### Step 2.2: Create Task Breakdown
```
# Use add_tasks tool to create structured task list
# Each task should represent ~20 minutes of professional work
# Organize by dependency order and priority level
# Include specific file paths and line numbers where possible
```

#### Step 2.3: Estimate Effort
```
Total Estimated Time: X hours
- Critical: Y minutes (Z tasks)
- High: Y minutes (Z tasks)  
- Medium: Y minutes (Z tasks)
- Low: Y minutes (Z tasks)
```

### Phase 3: Implementation (60-120 minutes)

#### Step 3.1: Database Layer Fixes
```python
# Pattern: Add missing required fields to test data
operation_data = {
    "required_field": "value",  # Add missing required fields
    "input_data": [{"test": "data"}],  # Common missing field
    # ... existing fields
}
```

#### Step 3.2: Service Layer Unified Patterns
```python
# Pattern: Apply unified error handling decorators
@handle_service_errors("operation_name")
@monitor_service_performance("operation_name")  # If applicable
def service_method(self, params):
    # Service logic with unified error handling
    pass
```

#### Step 3.3: Repository Layer Compliance
```python
# Pattern: Apply repository error handling
@handle_repository_errors("entity_name")
def repository_method(self, entity_data):
    # Repository logic with unified error translation
    pass
```

#### Step 3.4: Validation Enhancements
```python
# Pattern: Add comprehensive input validation
if value <= 0:
    raise InvalidInputError("value must be positive")
if not required_field:
    raise InvalidInputError("required_field is required")
```

### Phase 4: Verification (30-45 minutes)

#### Step 4.1: Unified Patterns Compliance Check
```bash
# Verify unified patterns compliance
python scripts/analysis/inventory_analyzer.py unified-patterns

# Target improvements in migration percentages
# Document any compliance issues found
```

#### Step 4.2: Incremental Test Verification
```bash
# Test each layer individually
python -m pytest tests/test_core_repositories/ -v
python -m pytest tests/test_core_calculations/test_services/ -v
python -m pytest tests/test_core_calculations/test_power/ -v

# Verify 100% pass rate per layer before proceeding
```

#### Step 4.3: Coverage Analysis
```bash
# Check coverage improvements
python -m pytest --cov=core --cov-report=html --cov-report=term-missing

# Target: 85%+ per module
# Document any coverage gaps
```

#### Step 4.4: Full Test Suite Verification
```bash
# Final comprehensive test run
.\make test

# Target: 100% pass rate, 85%+ coverage
# Document any remaining failures
```

### Phase 5: Documentation (15-30 minutes)

#### Step 5.1: Document Lessons Learned
```markdown
# Create: backend/docs/how-to/testing/lessons-learned-YYYYMMDD.md

## Key Findings
- Root causes identified
- Patterns applied successfully  
- Common pitfalls avoided
- Recommendations for future work

## Implementation Details
- Files modified
- Patterns applied
- Verification results
```

#### Step 5.2: Create Handover Package
```markdown
# Create: backend/docs/how-to/testing/ai-agent-handover-YYYYMMDD.md

## Quick Start for Next Agent
- Immediate actions required
- Current status summary
- Remaining work breakdown

## Technical Context
- Project standards
- Implementation patterns
- Verification commands
```

#### Step 5.3: Update Task Status
```
# Use update_tasks tool to mark completed tasks
# Document any remaining work in task descriptions
# Provide clear next steps for continuation
```

## Quality Standards Checklist

### Engineering-Grade Requirements ✅
- [ ] IEEE/IEC/EN standards compliance (NO NFPA/API)
- [ ] Unified patterns applied (@handle_*_errors decorators)
- [ ] No backward compatibility (clean implementations)
- [ ] Professional terminology (no "professional_" prefixes)
- [ ] Real database connections (NO mocks in repository tests)

### Testing Standards ✅
- [ ] 85%+ coverage per module
- [ ] 100% pass rate requirement
- [ ] Systematic test isolation
- [ ] Proper error handling validation
- [ ] Business-critical functionality prioritized

### Documentation Standards ✅
- [ ] Immaculate attention to detail
- [ ] Engineering-grade robustness
- [ ] Future flexibility considerations
- [ ] Comprehensive handover packages
- [ ] Lessons learned documentation

## Common Patterns & Solutions

### Database Schema Issues
```python
# Missing required fields in test data
"calculation_type": "heat_loss",
"input_data": [{"test": "data"}],
"operation_status": "PENDING",
```

### Service Layer Error Handling
```python
@handle_service_errors("operation_name")
def service_method(self):
    try:
        # Service logic
        pass
    except Exception as e:
        if isinstance(e, (DatabaseError, InvalidInputError)):
            raise
        raise ServiceError(f"Operation failed: {str(e)}")
```

### Validation Patterns
```python
# Positive value validation
if value <= 0:
    raise InvalidInputError("value must be positive")

# Required field validation  
if not field:
    raise InvalidInputError("field is required")

# Type validation
if not isinstance(value, expected_type):
    raise InvalidInputError(f"value must be {expected_type.__name__}")
```

### Field Name Alignment
```python
# Ensure test expectations match implementation
# Common mismatches:
# - "current" vs "load_current"
# - "status" vs "operation_status"  
# - "name" vs "operation_name"
```

## Emergency Procedures

### If Tests Continue Failing
1. **Check Database State**: Consider database reset
2. **Verify Imports**: Ensure all required modules imported
3. **Check Field Names**: Verify test expectations vs implementation
4. **Review Error Messages**: Look for new constraint failures

### If Coverage Drops
1. **Analyze Missing Lines**: Use coverage report
2. **Add Targeted Tests**: Focus on uncovered code paths
3. **Maintain Quality**: Don't sacrifice test quality for numbers

### If Unified Patterns Fail
1. **Check Decorator Imports**: Verify unified_error_handler imports
2. **Apply Systematically**: Use consistent decorator patterns
3. **Test Integration**: Verify decorators work with test framework

**Template Usage**: Copy this template for each major testing initiative. Adapt steps based on specific failure patterns discovered. Maintain engineering-grade robustness throughout implementation.
