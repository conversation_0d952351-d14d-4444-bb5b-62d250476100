# !/usr/bin/env python3
"""File I/O Utilities.

This module provides secure and standardized file operations for the application,
particularly useful for import/export features, temporary file handling, and
log file management.

Key Features:
- Secure file operations with validation
- Temporary file and directory management
- CSV and JSON file helpers
- File path validation and sanitization
"""

import csv
import hashlib
import json
import shutil
import tempfile
from contextlib import contextmanager
from pathlib import Path
from typing import TYPE_CHECKING, Any

from src.config.logging_config import logger

# Lazy imports to avoid circular dependencies
if TYPE_CHECKING:
    from src.core.errors.unified_error_handler import handle_utility_errors
    from src.core.monitoring.unified_performance_monitor import (
        monitor_utility_performance,
    )


def _apply_utility_decorators(func: Any, operation_name: str) -> Any:
    """Apply utility decorators dynamically to avoid circular imports."""
    try:
        from src.core.errors.unified_error_handler import handle_utility_errors
        from src.core.monitoring.unified_performance_monitor import (
            monitor_utility_performance,
        )

        # Apply decorators
        decorated_func = handle_utility_errors(operation_name)(func)
        decorated_func = monitor_utility_performance(operation_name)(decorated_func)
        return decorated_func
    except ImportError:
        # If decorators can't be imported, return original function
        logger.warning(
            f"Could not apply decorators to {operation_name}, using original function"
        )
        return func


# File size limits (in bytes)
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
MAX_CSV_ROWS = 100000
MAX_JSON_SIZE = 50 * 1024 * 1024  # 50MB

# Allowed file extensions
ALLOWED_EXTENSIONS = {
    "csv": [".csv"],
    "json": [".json"],
    "text": [".txt", ".log"],
    "excel": [".xlsx", ".xls"],
    "image": [".jpg", ".jpeg", ".png", ".gif", ".bmp"],
    "document": [".pdf", ".doc", ".docx"],
}


class FileIOError(Exception):
    """Exception raised for file I/O operations."""


def _validate_file_path_impl(file_path: str | Path, must_exist: bool = True) -> Path:
    """Implementation of validate_file_path without decorators."""
    try:
        path = Path(file_path).resolve()

        # Security check: ensure path doesn't escape allowed directories
        # This is a basic check - in production, you'd want more robust validation
        if ".." in str(path):
            raise FileIOError(f"Invalid path: {file_path}")

        if must_exist and not path.exists():
            raise FileIOError(f"File does not exist: {file_path}")

        return path

    except Exception as e:
        raise FileIOError(f"Invalid file path: {file_path}") from e


# Create decorated version lazily
_validate_file_path_decorated = None


def validate_file_path(file_path: str | Path, must_exist: bool = True) -> Any:
    """Validate and normalize file path.

    Args:
        file_path: Path to validate
        must_exist: Whether file must exist

    Returns:
        Path: Validated and normalized path

    Raises:
        FileIOError: If path is invalid or file doesn't exist when required

    """
    global _validate_file_path_decorated
    if _validate_file_path_decorated is None:
        _validate_file_path_decorated = _apply_utility_decorators(
            _validate_file_path_impl, "validate_file_path"
        )
    return _validate_file_path_decorated(file_path, must_exist)


def validate_file_extension(file_path: str | Path, allowed_types: list[str]) -> bool:
    """Validate file extension against allowed types.

    Args:
        file_path: Path to check
        allowed_types: List of allowed file type categories

    Returns:
        bool: True if extension is allowed

    """
    path = Path(file_path)
    extension = path.suffix.lower()

    for file_type in allowed_types:
        if file_type in ALLOWED_EXTENSIONS:
            if extension in ALLOWED_EXTENSIONS[file_type]:
                return True

    return False


def get_file_size(file_path: str | Path) -> int:
    """Get file size in bytes.

    Args:
        file_path: Path to file

    Returns:
        int: File size in bytes

    Raises:
        FileIOError: If file doesn't exist or can't be accessed

    """
    try:
        path = validate_file_path(file_path, must_exist=True)
        return int(path.stat().st_size)
    except Exception as e:
        raise FileIOError(f"Cannot get file size: {e}") from e


def validate_file_size(file_path: str | Path, max_size: int = MAX_FILE_SIZE) -> bool:
    """Validate file size against maximum allowed size.

    Args:
        file_path: Path to file
        max_size: Maximum allowed size in bytes

    Returns:
        bool: True if file size is within limits

    """
    try:
        size = get_file_size(file_path)
        return size <= max_size
    except FileIOError:
        return False


def calculate_file_hash(file_path: str | Path, algorithm: str = "md5") -> str:
    """Calculate hash of file contents.

    Args:
        file_path: Path to file
        algorithm: Hash algorithm ('md5', 'sha1', 'sha256')

    Returns:
        str: Hexadecimal hash string

    Raises:
        FileIOError: If file cannot be read or algorithm is invalid

    """
    try:
        path = validate_file_path(file_path, must_exist=True)

        if algorithm == "md5":
            # Using MD5 for non-security file integrity checking only
            hasher = hashlib.md5(usedforsecurity=False)  # nosec B324
        elif algorithm == "sha1":
            # Using SHA1 for non-security file integrity checking only
            hasher = hashlib.sha1(usedforsecurity=False)  # nosec B324
        elif algorithm == "sha256":
            hasher = hashlib.sha256()
        else:
            raise FileIOError(f"Unsupported hash algorithm: {algorithm}")

        with open(path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)

        return hasher.hexdigest()

    except Exception as e:
        raise FileIOError(f"Cannot calculate file hash: {e}") from e


@contextmanager
def temporary_file(
    suffix: str = "",
    prefix: str = "tmp",
    dir: str | None = None,
    delete: bool = True,
) -> Any:
    """Context manager for temporary files.

    Args:
        suffix: File suffix
        prefix: File prefix
        dir: Directory for temporary file
        delete: Whether to delete file when done

    Yields:
        Path: Path to temporary file

    """
    temp_file = None
    temp_path = None
    try:
        # Create a temporary file and immediately close and delete it
        # We only want the unique path, not the actual file
        temp_file = tempfile.NamedTemporaryFile(
            suffix=suffix, prefix=prefix, dir=dir, delete=False
        )
        temp_path = Path(temp_file.name)
        temp_file.close()

        # Remove the file so it doesn't exist initially
        if temp_path.exists():
            temp_path.unlink()

        yield temp_path

    finally:
        if temp_path and delete and temp_path.exists():
            try:
                temp_path.unlink()
            except Exception as e:
                logger.warning(f"Failed to delete temporary file {temp_path}: {e}")


@contextmanager
def temporary_directory(
    suffix: str = "", prefix: str = "tmp", dir: str | None = None
) -> Any:
    """Context manager for temporary directories.

    Args:
        suffix: Directory suffix
        prefix: Directory prefix
        dir: Parent directory for temporary directory

    Yields:
        Path: Path to temporary directory

    """
    temp_dir = None
    try:
        temp_dir = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
        temp_path = Path(temp_dir)

        yield temp_path

    finally:
        if temp_dir and Path(temp_dir).exists():
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"Failed to delete temporary directory {temp_dir}: {e}")


def safe_write_file(
    file_path: str | Path,
    content: str | bytes,
    encoding: str = "utf-8",
    backup: bool = True,
) -> None:
    """Safely write content to file with optional backup.

    Args:
        file_path: Path to write to
        content: Content to write
        encoding: Text encoding (ignored for bytes)
        backup: Whether to create backup of existing file

    Raises:
        FileIOError: If write operation fails

    """
    try:
        path = Path(file_path).resolve()

        # Create backup if file exists and backup is requested
        if backup and path.exists():
            backup_path = path.with_suffix(path.suffix + ".bak")
            shutil.copy2(path, backup_path)

        # Ensure parent directory exists
        path.parent.mkdir(parents=True, exist_ok=True)

        # Write content
        if isinstance(content, bytes):
            with open(path, "wb") as f:
                f.write(content)
        elif isinstance(content, str):
            with open(path, "w", encoding=encoding) as f:
                f.write(content)
        else:
            raise TypeError(f"Unsupported content type: {type(content)}")

        logger.debug(f"Successfully wrote file: {path}")

    except Exception as e:
        raise FileIOError(f"Failed to write file {file_path}: {e}") from e


# Decorators will be applied lazily when functions are first called


def safe_read_file(
    file_path: str | Path, encoding: str = "utf-8", binary: bool = False
) -> str | bytes:
    """Safely read file content.

    Args:
        file_path: Path to read from
        encoding: Text encoding (ignored for binary)
        binary: Whether to read in binary mode

    Returns:
        File content as string or bytes

    Raises:
        FileIOError: If read operation fails

    """
    try:
        path = validate_file_path(file_path, must_exist=True)

        if binary:
            with open(path, "rb") as f:
                return f.read()
        else:
            with open(path, encoding=encoding) as f:
                return f.read()

    except Exception as e:
        raise FileIOError(f"Failed to read file {file_path}: {e}") from e


# Decorators will be applied lazily when functions are first called


# CSV utilities


def read_csv_file(
    file_path: str | Path,
    max_rows: int = MAX_CSV_ROWS,
    encoding: str = "utf-8",
    delimiter: str = ",",
    **kwargs: Any,
) -> list[dict[str, Any]]:
    """Read CSV file and return list of dictionaries.

    Args:
        file_path: Path to CSV file
        max_rows: Maximum number of rows to read
        encoding: File encoding
        delimiter: CSV delimiter
        **kwargs: Additional arguments for csv.DictReader

    Returns:
        List of dictionaries representing CSV rows

    Raises:
        FileIOError: If file cannot be read or is too large

    """
    try:
        path = validate_file_path(file_path, must_exist=True)

        if not validate_file_extension(path, ["csv"]):
            raise FileIOError(f"File is not a CSV file: {path}")

        if not validate_file_size(path):
            raise FileIOError(f"CSV file too large: {path}")

        rows = []
        with open(path, encoding=encoding, newline="") as f:
            reader = csv.DictReader(f, delimiter=delimiter, **kwargs)

            for i, row in enumerate(reader):
                if i >= max_rows:
                    logger.warning(f"CSV file truncated at {max_rows} rows")
                    break
                rows.append(dict(row))

        logger.debug(f"Read {len(rows)} rows from CSV: {path}")
        return rows

    except Exception as e:
        raise FileIOError(f"Failed to read CSV file {file_path}: {e}") from e


def write_csv_file(
    file_path: str | Path,
    data: list[dict[str, Any]],
    fieldnames: list[str] | None = None,
    encoding: str = "utf-8",
    delimiter: str = ",",
    **kwargs: Any,
) -> None:
    """Write list of dictionaries to CSV file.

    Args:
        file_path: Path to write CSV file
        data: List of dictionaries to write
        fieldnames: List of field names (auto-detected if None)
        encoding: File encoding
        delimiter: CSV delimiter
        **kwargs: Additional arguments for csv.DictWriter

    Raises:
        FileIOError: If write operation fails

    """
    try:
        if not data:
            raise FileIOError("No data to write to CSV")

        if fieldnames is None:
            fieldnames = list(data[0].keys())

        path = Path(file_path).resolve()
        path.parent.mkdir(parents=True, exist_ok=True)

        with open(path, "w", encoding=encoding, newline="") as f:
            writer = csv.DictWriter(
                f, fieldnames=fieldnames, delimiter=delimiter, **kwargs
            )
            writer.writeheader()
            writer.writerows(data)

        logger.debug(f"Wrote {len(data)} rows to CSV: {path}")

    except Exception as e:
        raise FileIOError(f"Failed to write CSV file {file_path}: {e}") from e


# JSON utilities


def read_json_file(
    file_path: str | Path, max_size: int = MAX_JSON_SIZE, encoding: str = "utf-8"
) -> Any:
    """Read and parse JSON file.

    Args:
        file_path: Path to JSON file
        max_size: Maximum file size in bytes
        encoding: File encoding

    Returns:
        Parsed JSON data

    Raises:
        FileIOError: If file cannot be read or parsed

    """
    try:
        path = validate_file_path(file_path, must_exist=True)

        if not validate_file_extension(path, ["json"]):
            raise FileIOError(f"File is not a JSON file: {path}")

        if not validate_file_size(path, max_size):
            raise FileIOError(f"JSON file too large: {path}")

        content = safe_read_file(path, encoding=encoding)
        data = json.loads(content)

        logger.debug(f"Successfully read JSON file: {path}")
        return data

    except json.JSONDecodeError as e:
        raise FileIOError(f"Invalid JSON in file {file_path}: {e}") from e
    except Exception as e:
        raise FileIOError(f"Failed to read JSON file {file_path}: {e}") from e


def write_json_file(
    file_path: str | Path,
    data: Any,
    encoding: str = "utf-8",
    indent: int = 2,
    **kwargs: Any,
) -> None:
    """Write data to JSON file.

    Args:
        file_path: Path to write JSON file
        data: Data to serialize to JSON
        encoding: File encoding
        indent: JSON indentation
        **kwargs: Additional arguments for json.dumps

    Raises:
        FileIOError: If write operation fails

    """
    try:
        json_content = json.dumps(data, indent=indent, ensure_ascii=False, **kwargs)
        safe_write_file(file_path, json_content, encoding=encoding)

        logger.debug(f"Successfully wrote JSON file: {file_path}")

    except Exception as e:
        raise FileIOError(f"Failed to write JSON file {file_path}: {e}") from e
