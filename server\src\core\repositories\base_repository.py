# src/core/repositories/base_repository.py
"""Base Repository.

This module provides a generic base repository class for database operations,
providing common CRUD methods and error handling.
"""

from typing import Any, Generic, TypeVar

from sqlalchemy import and_, select
from sqlalchemy.orm import Session

from src.config.logging_config import logger
from src.core.errors.exceptions import NotFoundError

# Import pagination utilities for enhanced repository functionality
from src.core.errors.unified_error_handler import handle_repository_errors

# Exceptions are handled by unified error handler
from src.core.models.base import Base

# Lazy import to avoid circular dependencies
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.utils.pagination_utils import (
    PaginationParams,
    PaginationResult,
    SortParams,
    paginate_query,
)
from src.core.utils.query_utils import QueryBuilder

# Define a generic type for SQLAlchemy models
ModelType = TypeVar("ModelType", bound=Base)  # type: ignore


class BaseRepository(Generic[ModelType]):
    """Base repository class for database operations.

    Provides common CRUD operations and error handling for repository methods.

    """

    def __init__(self, db_session: Session, model: type[ModelType]):
        """Initialize the BaseRepository.

        Args:
            db_session: SQLAlchemy database session
            model: SQLAlchemy model class for the repository

        """
        self.db_session = db_session
        self.model = model

    @handle_repository_errors("session_health")
    @monitor_repository_performance("session_health")
    def is_session_healthy(self) -> bool:
        """Check if the database session is healthy and usable.

        Returns:
            bool: True if session is healthy, False otherwise

        """
        if self.db_session is None:
            return False

        # Check if session is active
        if hasattr(self.db_session, "is_active") and not self.db_session.is_active:
            return False

        # Try a simple query to test session health
        from sqlalchemy import text

        self.db_session.execute(text("SELECT 1"))
        return True

    @handle_repository_errors("session_operation")
    @monitor_repository_performance("session_operation")
    def safe_session_operation(self, operation_func, *args, **kwargs):
        """Execute a database operation with session health checking and retry logic.

        Args:
            operation_func: Function to execute
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Result of the operation or None if session is unhealthy

        """
        max_retries = 3
        retry_delay = 0.1  # 100ms

        for attempt in range(max_retries):
            # For concurrent operations, be more permissive with session state
            result = operation_func(*args, **kwargs)
            if result is not None or attempt == max_retries - 1:
                return result

            # Add retry delay for next attempt
            import time

            time.sleep(retry_delay * (attempt + 1))

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    def get_by_id(self, item_id: int) -> ModelType | None:
        """Get item by ID.

        Args:
            item_id: ID of the item to retrieve

        Returns:
            Optional[ModelType]: Item with the specified ID or None if not found

        """

        def _get_operation():
            # Check if model has soft delete capability
            if hasattr(self.model, "is_deleted"):
                stmt = select(self.model).where(
                    and_(self.model.id == item_id, self.model.is_deleted == False)
                )
            else:
                # For models without soft delete (like ActivityLog)
                stmt = select(self.model).where(self.model.id == item_id)
            return self.db_session.scalar(stmt)

        # Use safe session operation
        result = self.safe_session_operation(_get_operation)
        return result

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    def get_all(self, skip: int = 0, limit: int = 100) -> list[ModelType]:
        """Get all items with optional pagination.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ModelType]: List of all items

        """
        stmt = select(self.model).offset(skip).limit(limit)
        return list(self.db_session.scalars(stmt).all())

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    def create(self, data: dict[str, Any]) -> ModelType:
        """Create a new item.

        Args:
            data: Dictionary containing item data

        Returns:
            ModelType: Created item

        """
        item = self.model(**data)
        self.db_session.add(item)
        self.db_session.flush()  # Flush to get the ID assigned
        return item

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    def update(self, item_id: int, data: dict[str, Any]) -> ModelType:
        """Update an existing item by ID.

        Args:
            item_id: ID of the item to update
            data: Dictionary of fields to update

        Returns:
            ModelType: Updated item

        Raises:
            NotFoundError: If item with given ID doesn't exist

        """
        # Get the existing item
        item = self.get_by_id(item_id)
        if not item:
            raise NotFoundError(
                code="404_001",
                detail=f"{self.model.__name__} with ID '{item_id}' not found.",
                category="ClientError",
                status_code=404,
                metadata={"entity_type": self.model.__name__, "entity_id": item_id},
            )

        # Update the item attributes
        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)

        self.db_session.flush()  # Flush to persist changes
        return item

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    def delete(self, item_id: int) -> bool:
        """Delete an item by ID.

        Args:
            item_id: ID of the item to delete

        Returns:
            bool: True if item was deleted

        """
        item = self.get_by_id(item_id)
        if not item:
            raise NotFoundError(
                code="404_001",
                detail=f"{self.model.__name__} with ID '{item_id}' not found.",
                category="ClientError",
                status_code=404,
                metadata={"entity_type": self.model.__name__, "entity_id": item_id},
            )

        self.db_session.delete(item)
        self.db_session.flush()  # Flush to persist deletion
        return True

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    def get_paginated(
        self,
        pagination_params: PaginationParams,
        sort_params: SortParams | None = None,
        filters: dict[str, Any] | None = None,
    ) -> PaginationResult:
        """Get paginated results with optional filters and sorting.

        Args:
            pagination_params: Pagination parameters (page, per_page)
            sort_params: Sort parameters (optional)
            filters: Dictionary of filters to apply (optional)

        Returns:
            PaginationResult: Paginated results with metadata

        """
        # Start with base query
        query = select(self.model)

        # Apply filters if provided
        if filters:
            builder = QueryBuilder(self.db_session, self.model)
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    builder.filter_by_field(field, value)
            query = builder.build()

        # Apply pagination and sorting
        return paginate_query(
            self.db_session, query, self.model, pagination_params, sort_params
        )

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    def search_paginated(
        self,
        search_term: str,
        searchable_fields: list[str],
        pagination_params: PaginationParams,
        sort_params: SortParams | None = None,
        additional_filters: dict[str, Any] | None = None,
    ) -> PaginationResult:
        """Search entities with pagination.

        Args:
            search_term: Term to search for
            searchable_fields: List of fields to search in
            pagination_params: Pagination parameters
            sort_params: Sort parameters (optional)
            additional_filters: Additional filters to apply (optional)

        Returns:
            PaginationResult: Paginated search results

        """
        # Build search query using QueryBuilder
        builder = QueryBuilder(self.db_session, self.model)

        # Add search conditions
        if search_term and searchable_fields:
            search_conditions = []
            for field in searchable_fields:
                if hasattr(self.model, field):
                    field_attr = getattr(self.model, field)
                    search_conditions.append(field_attr.ilike(f"%{search_term}%"))

            if search_conditions:
                from sqlalchemy import or_

                builder.query = builder.query.where(or_(*search_conditions))

        # Apply additional filters
        if additional_filters:
            for field, value in additional_filters.items():
                if hasattr(self.model, field) and value is not None:
                    builder.filter_by_field(field, value)

        query = builder.build()

        # Apply pagination and sorting
        return paginate_query(
            self.db_session, query, self.model, pagination_params, sort_params
        )
