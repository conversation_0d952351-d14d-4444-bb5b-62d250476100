## **6. Data Fetching & API Communication**

This section details how our frontend interacts with the FastAPI backend.

### **6.1. Integrating with Backend API (FastAPI)**

- **What:** We will define service layers on the frontend that encapsulate API calls, mirroring the backend's service structure where appropriate. We'll use generated API clients (e.g., from openapi-typescript or similar tools) where possible, or manual wrappers around fetch or Axios.  
  // src/api/services/projectService.ts (Example using a custom wrapper around fetch)  
  import { API_BASE_URL } from '@/config/constants'; // Your API base URL  
  import { ProjectCreateSchema, ProjectReadSchema, ProjectUpdateSchema } from '@/api/schemas/project'; // Import schemas  
    
  export const projectService = {  
  async getAllProjects(): Promise\<ProjectReadSchema\[\]\> {  
  const response = await fetch(\`\${API_BASE_URL}/projects\`);  
  if (!response.ok) {  
  throw new Error('Failed to fetch projects');  
  }  
  return response.json();  
  },  
    
  async getProjectById(id: string): Promise\<ProjectReadSchema\> {  
  const response = await fetch(\`\${API_BASE_URL}/projects/\${id}\`);  
  if (!response.ok) {  
  throw new Error(\`Failed to fetch project with ID: \${id}\`);  
  }  
  return response.json();  
  },  
    
  async createProject(data: ProjectCreateSchema): Promise\<ProjectReadSchema\> {  
  const response = await fetch(\`\${API_BASE_URL}/projects\`, {  
  method: 'POST',  
  headers: { 'Content-Type': 'application/json' },  
  body: JSON.stringify(data),  
  });  
  if (!response.ok) {  
  // You'd handle different error codes here (e.g., 400 for validation errors)  
  const errorData = await response.json();  
  throw new Error(errorData.detail \|\| 'Failed to create project');  
  }  
  return response.json();  
  },  
  // ... update, delete, etc.  
  };

- **Why:** Creating a dedicated API service layer provides a single point of truth for backend interactions. This separates concerns, makes API logic reusable, and simplifies error handling and authentication. Using generated clients ensures type safety across the API boundary.

### **6.2. Using a Fetching Library (React Query with fetch or Axios)**

- **What:** Our primary fetching mechanism will be the native fetch API, wrapped by React Query for its powerful caching and state management capabilities. We can also integrate Axios if its interceptor capabilities become a significant requirement (e.g., for global error handling or automatic token refreshing).

- **Why:** fetch is native to the browser, requiring no extra bundle size for basic requests. React Query then builds on top of it to provide the advanced features we need for server state management.

### **6.3. Error Handling for API Calls**

- **What:** Implement a centralized error handling strategy for API calls.

  - **Frontend Service Layer:** Each API service method should throw errors for failed requests, including parsed error messages from the backend (FastAPI's default error responses are helpful here).

  - **React Query onError:** Utilize onError callbacks in useQuery and useMutation to handle errors at the component or hook level. This is where we might display toasts, log errors, or redirect users.

  - **Global Error Boundaries (React):** For unexpected rendering errors.

- **Why:** Consistent error handling improves user experience by providing clear feedback and ensures that application state remains robust even during network issues or backend failures.

### **6.4. Data Transformation and Validation**

- **What:**

  - **Input Validation (Client-Side):** Use libraries like Zod or Yup (or simple custom validation functions) with React Hook Form to validate user input before sending data to the backend. This provides immediate feedback to the user and reduces unnecessary API calls.

  - **Data Transformation:** If backend data schemas don't perfectly align with frontend UI needs, perform necessary transformations within the API service layer or a dedicated mapper utility.  
    // src/utils/dataMappers.ts  
    import { BackendProjectSchema } from '@/api/schemas/backend-project';  
    import { FrontendProjectDisplay } from '@/types/frontend-types';  
      
    export const mapBackendProjectToFrontend = (  
    backendProject: BackendProjectSchema  
    ): FrontendProjectDisplay =\> {  
    return {  
    id: backendProject.id,  
    name: backendProject.project_name,  
    status: backendProject.status_code, // Map backend code to human-readable text  
    createdAt: new Date(backendProject.created_at_utc),  
    // ... other transformations  
    };  
    };

- **Why:** Client-side validation improves responsiveness and reduces server load. Data transformation ensures the UI works with data in its most convenient format, cleanly separating frontend concerns from backend data structures.
