## Commands

### Backend (server/)
- **Run all tests:** `poetry run pytest`
- **Run tests with coverage:** `poetry run pytest --cov=src --cov-report=html`
- **Run a single test file:** `poetry run pytest tests/api/test_user_routes.py`
- **Lint:** `poetry run ruff check .`
- **Typecheck:** `poetry run mypy src`

### Frontend (client/)
- **Run unit tests:** `npm run test`
- **Lint:** `npm run lint`
- **Typecheck:** `npm run type-check`

### C# Services (cad-integrator-service/ and computation-engine-service/)
- **Restore dependencies:** `dotnet restore`
- **Run:** `dotnet run`

## Code Style

### General
- **Architecture:** Follow the 5-layer architecture pattern (API, Service, Repository, Model, Database).
- **Error Handling:** Use the unified error handling and monitoring patterns.
- **Zero Tolerance:** No warnings, no technical debt.

### Python (Backend)
- **Typing:** Full type annotations are required (100% type coverage). Use `pydantic` for schemas. `mypy` is configured with strict rules.
- **Imports:** Use absolute imports. `isort` is configured to enforce a specific order.
- **Formatting:** Follow `ruff` for formatting and linting. The line length is 88 characters.
- **Naming:** Follow PEP 8 conventions.

### TypeScript (Frontend)
- **Typing:** Full type annotations are required.
- **State Management:** Use React Query (TanStack Query) and Zustand.

### Commits
- Use conventional commit messages (e.g., `feat:`, `fix:`, `docs:`).
