# 09 - Component Models

**Section:** 09-component-models  
**Version:** 1.0  
**Last Updated:** July 2025  
**Prerequisites:** [Script Ecosystem](080-script-ecosystem.md) completed  
**Estimated Reading Time:** 35 minutes  

## Overview

The Ultimate Electrical Designer features a comprehensive component management system with 13 professional electrical design categories, hierarchical relationships, and complete heat tracing support. This section details the component models, electrical system design patterns, circuit types, and cable management that form the foundation of professional electrical design workflows.

## Table of Contents

- [09 - Component Models](#09---component-models)
  - [Overview](#overview)
  - [Table of Contents](#table-of-contents)
  - [Component Categorization System](#component-categorization-system)
    - [13 Professional Electrical Design Categories](#13-professional-electrical-design-categories)
    - [ComponentType Enum Implementation](#componenttype-enum-implementation)
    - [Component Model Implementation](#component-model-implementation)

## Component Categorization System

### 13 Professional Electrical Design Categories

The component system organizes electrical components into 13 hierarchical categories that reflect professional electrical design practices:

```
Component Hierarchy:
├── 01. Power Generation
│   ├── Generators (Diesel, Gas, Wind, Solar)
│   ├── UPS Systems (Online, Offline, Line-Interactive)
│   └── Battery Systems (Lead-Acid, Lithium, NiCd)
├── 02. Power Distribution
│   ├── Transformers (Power, Distribution, Isolation)
│   ├── Switchboards (Main, Distribution, Motor Control)
│   └── Panelboards (Lighting, Power, Emergency)
├── 03. Protection Devices
│   ├── Circuit Breakers (ACB, MCCB, MCB, RCD)
│   ├── Fuses (HRC, Cartridge, Blade)
│   └── Surge Protection (Type 1, Type 2, Type 3)
├── 04. Control Systems
│   ├── Motor Starters (DOL, Star-Delta, Soft Start)
│   ├── Variable Frequency Drives (Low/Medium/High Voltage)
│   └── Control Relays (Auxiliary, Time, Protection)
├── 05. Instrumentation
│   ├── Measurement Devices (Meters, Transducers, Sensors)
│   ├── Monitoring Systems (SCADA, HMI, Data Loggers)
│   └── Calibration Equipment (Standards, Calibrators)
├── 06. Heat Tracing Systems
│   ├── Self-Regulating Cables (Low/Medium/High Temperature)
│   ├── Series Resistance Cables (Mineral Insulated, Polymer)
│   └── Control Systems (Thermostats, Controllers, Monitoring)
├── 07. Cables and Conductors
│   ├── Power Cables (XLPE, EPR, PVC Insulated)
│   ├── Control Cables (Multicore, Screened, Armored)
│   └── Instrumentation Cables (Twisted Pair, Triax, Fiber)
├── 08. Cable Management
│   ├── Cable Trays (Ladder, Perforated, Solid Bottom)
│   ├── Conduits (Rigid, Flexible, Liquid-tight)
│   └── Cable Glands (Armored, Unarmored, Explosion-proof)
├── 09. Grounding and Bonding
│   ├── Grounding Electrodes (Rods, Plates, Grids)
│   ├── Grounding Conductors (Bare, Insulated, Braided)
│   └── Bonding Equipment (Clamps, Lugs, Jumpers)
├── 10. Lighting Systems
│   ├── Light Sources (LED, Fluorescent, HID, Incandescent)
│   ├── Luminaires (Indoor, Outdoor, Hazardous Area)
│   └── Control Systems (Dimmers, Sensors, Smart Controls)
├── 11. Motors and Drives
│   ├── AC Motors (Induction, Synchronous, Servo)
│   ├── DC Motors (Brushed, Brushless, Stepper)
│   └── Motor Accessories (Encoders, Brakes, Couplings)
├── 12. Safety and Security
│   ├── Emergency Systems (Lighting, Power, Communication)
│   ├── Fire Detection (Smoke, Heat, Flame Detectors)
│   └── Security Systems (Access Control, CCTV, Alarms)
└── 13. Specialized Equipment
    ├── Hazardous Area Equipment (Ex d, Ex e, Ex i, Ex n)
    ├── Marine Equipment (Shipboard, Offshore, Subsea)
    └── Railway Equipment (Traction, Signaling, Power Supply)
```

### ComponentType Enum Implementation

```python
# core/enums/electrical_enums.py
"""
This module defines comprehensive enumeration types specific to electrical
engineering design within the Ultimate Electrical Designer application.
It serves as the single source of truth for classifying electrical components,
system elements, circuit characteristics, load types, and design criteria.
The enums herein are critical for accurate data modeling, calculation inputs,
and report generation in electrical design.
"""
from enum import Enum

class ComponentCategoryType(Enum):
    """
    Broad categories for electrical components and equipment.
    This helps in high-level classification and filtering.
    """
    POWER_DISTRIBUTION = "Power Distribution"
    CABLES_AND_WIRING = "Cables & Wiring"
    PROTECTION_DEVICES = "Protection Devices"
    SWITCHING_AND_CONTROL = "Switching & Control"
    MEASUREMENT_AND_MONITORING = "Measurement & Monitoring"
    ENCLOSURES_AND_MOUNTING = "Enclosures & Mounting"
    GROUNDING_AND_BONDING = "Grounding & Bonding"
    POWER_SOURCES = "Power Sources" # Renamed from 'Power Supply' for broader scope
    LOADS = "Loads" # For motors, heaters, lighting etc.
    COMMUNICATION = "Communication"
    SAFETY_AND_EMERGENCY = "Safety & Emergency"
    HEAT_TRACING = "Heat Tracing System" # Moved here as it's a specific electrical application system
    CABLE_MANAGEMENT = "Cable Management"
    OTHER_ELECTRICAL = "Other Electrical" # For anything not fitting existing categories

class ComponentType(Enum):
    """
    Detailed types of electrical components.
    This replaces both previous `ComponentType` and `EquipmentType` for a single source of truth.
    Each physical item, whether large equipment or a small device, is a ComponentType.
    """
    # Power Distribution & Control Equipment
    SWITCHBOARD = "Switchboard"
    MOTOR_CONTROL_CENTER = "Motor Control Center (MCC)"
    DISTRIBUTION_BOARD = "Distribution Board (DB)"
    PANELBOARD = "Panelboard"
    MAIN_SWITCHBOARD = "Main Switchboard (MSB)" # Specific type of switchboard
    SUB_SWITCHBOARD = "Sub Switchboard (SSB)" # Specific type of switchboard
    CONTROL_PANEL = "Control Panel"
    TRANSFORMER = "Transformer"
    GENERATOR = "Generator"
    UPS = "Uninterruptible Power Supply (UPS)"
    BATTERY_BANK = "Battery Bank"
    DC_POWER_SUPPLY = "DC Power Supply"
    SURGE_PROTECTIVE_DEVICE = "Surge Protective Device (SPD)" # Renamed for clarity
    CAPACITOR_BANK = "Capacitor Bank"
    AUTOMATIC_TRANSFER_SWITCH = "Automatic Transfer Switch (ATS)" # Renamed for clarity
    MANUAL_TRANSFER_SWITCH = "Manual Transfer Switch (MTS)" # New
    VARIABLE_FREQUENCY_DRIVE = "Variable Frequency Drive (VFD)"
    SOFT_STARTER = "Soft Starter"
    MOTOR_STARTER = "Motor Starter" # Generic starter, VFD and Soft Starter are specific types

    # Protection Devices
    CIRCUIT_BREAKER = "Circuit Breaker"
    FUSE = "Fuse"
    RESIDUAL_CURRENT_DEVICE = "Residual Current Device (RCD)"
    OVERLOAD_RELAY = "Overload Relay"
    PROTECTIVE_RELAY = "Protective Relay" # More general than just "Relay" if it's for protection

    # Cables & Wiring
    POWER_CABLE = "Power Cable"
    CONTROL_CABLE = "Control Cable"
    INSTRUMENTATION_CABLE = "Instrumentation Cable"
    COMMUNICATION_CABLE = "Communication Cable"
    FIBER_OPTIC_CABLE = "Fiber Optic Cable"
    COAXIAL_CABLE = "Coaxial Cable"
    BUSBAR = "Busbar"
    GROUNDING_CONDUCTOR = "Grounding Conductor" # Specific conductor for grounding
    BONDING_CONDUCTOR = "Bonding Conductor" # Specific conductor for bonding

    # Cable Management
    CABLE_TRAY = "Cable Tray"
    CONDUIT = "Conduit"
    CABLE_DUCT = "Cable Duct" # New
    CABLE_LADDER = "Cable Ladder" # New

    # Switching & Control
    DISCONNECT_SWITCH = "Disconnect Switch"
    LOAD_BREAK_SWITCH = "Load Break Switch" # New, distinct from disconnect
    ISOLATION_SWITCH = "Isolation Switch"
    CONTACTOR = "Contactor"
    CONTROL_RELAY = "Control Relay" # For general control applications (EMR/SSR if needed specific)
    PUSH_BUTTON = "Push Button"
    SELECTOR_SWITCH = "Selector Switch"
    PILOT_LIGHT = "Pilot Light"
    LIMIT_SWITCH = "Limit Switch"
    PROXIMITY_SWITCH = "Proximity Switch"
    TIMER = "Timer"
    COUNTER = "Counter"
    THERMOSTAT = "Thermostat" # General purpose thermal switch/controller
    PRESSURE_SWITCH = "Pressure Switch"
    FLOW_SWITCH = "Flow Switch"
    LEVEL_SWITCH = "Level Switch"

    # Measurement & Monitoring
    AMMETER = "Ammeter"
    VOLTMETER = "Voltmeter"
    POWER_METER = "Power Meter"
    ENERGY_METER = "Energy Meter"
    CURRENT_TRANSFORMER = "Current Transformer (CT)"
    VOLTAGE_TRANSFORMER = "Voltage Transformer (VT/PT)"
    TEMPERATURE_SENSOR = "Temperature Sensor"
    PRESSURE_SENSOR = "Pressure Sensor"
    FLOW_SENSOR = "Flow Sensor"
    LEVEL_SENSOR = "Level Sensor"
    HUMIDITY_SENSOR = "Humidity Sensor" # New
    VIBRATION_SENSOR = "Vibration Sensor" # New
    GAS_DETECTOR = "Gas Detector" # New
    SMOKE_DETECTOR = "Smoke Detector" # New
    MOTION_SENSOR = "Motion Sensor" # New

    # Enclosures & Mounting
    JUNCTION_BOX = "Junction Box"
    TERMINAL_BOX = "Terminal Box" # Can be distinct from junction box
    ENCLOSURE = "Enclosure" # Generic enclosure
    TERMINAL_BLOCK = "Terminal Block"
    CONNECTOR = "Connector" # General electrical connector
    CABLE_GLAND = "Cable Gland" # New
    CABLE_LUG = "Cable Lug" # New

    # Loads
    ELECTRIC_MOTOR = "Electric Motor"
    HEATER = "Heater"
    LIGHTING_FIXTURE = "Lighting Fixture"
    FAN = "Fan"
    PUMP = "Pump"
    COMPRESSOR = "Compressor"
    HVAC_UNIT = "HVAC Unit" # New
    OVEN = "Oven" # New
    FURNACE = "Furnace" # New
    WELDING_MACHINE = "Welding Machine"
    RECTIFIER = "Rectifier" # Can be a load, or part of a power supply
    VALVE_ACTUATOR = "Valve Actuator" # New, specific type of load

    # Heat Tracing Components (specific system components)
    HEAT_TRACING_CABLE = "Heat Tracing Cable"
    HEAT_TRACING_CONTROLLER = "Heat Tracing Controller"
    POWER_DISTRIBUTION_ENCLOSURE_HT = "Heat Tracing Power Distribution Enclosure"
    JUNCTION_BOX_HT = "Heat Tracing Junction Box" # Specific type of junction box for HT

    # Safety & Emergency (distinct from general protection)
    EMERGENCY_STOP_BUTTON = "Emergency Stop Button"
    FIRE_ALARM_CONTROL_PANEL = "Fire Alarm Control Panel"
    FIRE_ALARM_DETECTOR = "Fire Alarm Detector" # Smoke, heat, flame
    STROBE_LIGHT = "Strobe Light" # For alarms
    HORN = "Horn" # For alarms
    EMERGENCY_LIGHTING_FIXTURE = "Emergency Lighting Fixture"

    # Communication Network Components
    ETHERNET_SWITCH = "Ethernet Switch"
    WIRELESS_ACCESS_POINT = "Wireless Access Point"
    FIBER_OPTIC_TRANSCIEVER = "Fiber Optic Transceiver" # New
    INDUSTRIAL_ROUTER = "Industrial Router" # New

    # Other / Miscellaneous
    JUNCTION_TERMINAL = "Junction Terminal" # Can refer to a single terminal point
    MISCELLANEOUS_ELECTRICAL = "Miscellaneous Electrical Component" # Catch-all

# COMPONENT_TYPE_TO_CATEGORY_MAPPING for Data Integrity and Categorization
COMPONENT_TYPE_TO_CATEGORY_MAPPING = {
    # Power Distribution
    ComponentType.SWITCHBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MOTOR_CONTROL_CENTER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.DISTRIBUTION_BOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.PANELBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MAIN_SWITCHBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.SUB_SWITCHBOARD: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.CONTROL_PANEL: ComponentCategoryType.POWER_DISTRIBUTION, # Can also be control
    ComponentType.TRANSFORMER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.VARIABLE_FREQUENCY_DRIVE: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.SOFT_STARTER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MOTOR_STARTER: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.CAPACITOR_BANK: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.AUTOMATIC_TRANSFER_SWITCH: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.MANUAL_TRANSFER_SWITCH: ComponentCategoryType.POWER_DISTRIBUTION,
    ComponentType.BUSBAR: ComponentCategoryType.POWER_DISTRIBUTION,

    # Cables & Wiring
    ComponentType.POWER_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.CONTROL_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.INSTRUMENTATION_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.COMMUNICATION_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.FIBER_OPTIC_CABLE: ComponentCategoryType.CABLES_AND_WIRING,
    ComponentType.COAXIAL_CABLE: ComponentCategoryType.CABLES_AND_WIRING,

    # Protection Devices
    ComponentType.CIRCUIT_BREAKER: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.FUSE: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.RESIDUAL_CURRENT_DEVICE: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.OVERLOAD_RELAY: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.PROTECTIVE_RELAY: ComponentCategoryType.PROTECTION_DEVICES,
    ComponentType.SURGE_PROTECTIVE_DEVICE: ComponentCategoryType.PROTECTION_DEVICES,

    # Switching & Control
    ComponentType.DISCONNECT_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.LOAD_BREAK_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.ISOLATION_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.CONTACTOR: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.CONTROL_RELAY: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PUSH_BUTTON: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.SELECTOR_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PILOT_LIGHT: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.LIMIT_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PROXIMITY_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.TIMER: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.COUNTER: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.THERMOSTAT: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.PRESSURE_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.FLOW_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.LEVEL_SWITCH: ComponentCategoryType.SWITCHING_AND_CONTROL,
    ComponentType.VALVE_ACTUATOR: ComponentCategoryType.SWITCHING_AND_CONTROL, # Often electrically controlled

    # Measurement & Monitoring
    ComponentType.AMMETER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.VOLTMETER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.POWER_METER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.ENERGY_METER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.CURRENT_TRANSFORMER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.VOLTAGE_TRANSFORMER: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.TEMPERATURE_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.PRESSURE_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.FLOW_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.LEVEL_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.HUMIDITY_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.VIBRATION_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.GAS_DETECTOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.SMOKE_DETECTOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,
    ComponentType.MOTION_SENSOR: ComponentCategoryType.MEASUREMENT_AND_MONITORING,

    # Enclosures & Mounting
    ComponentType.JUNCTION_BOX: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.TERMINAL_BOX: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.ENCLOSURE: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.TERMINAL_BLOCK: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.CONNECTOR: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.CABLE_GLAND: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.CABLE_LUG: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,
    ComponentType.JUNCTION_TERMINAL: ComponentCategoryType.ENCLOSURES_AND_MOUNTING,

    # Grounding & Bonding
    ComponentType.GROUNDING_CONDUCTOR: ComponentCategoryType.GROUNDING_AND_BONDING,
    ComponentType.BONDING_CONDUCTOR: ComponentCategoryType.GROUNDING_AND_BONDING, # Specific conductor for bonding

    # Power Sources
    ComponentType.GENERATOR: ComponentCategoryType.POWER_SOURCES,
    ComponentType.UPS: ComponentCategoryType.POWER_SOURCES,
    ComponentType.BATTERY_BANK: ComponentCategoryType.POWER_SOURCES,
    ComponentType.DC_POWER_SUPPLY: ComponentCategoryType.POWER_SOURCES,

    # Loads
    ComponentType.ELECTRIC_MOTOR: ComponentCategoryType.LOADS,
    ComponentType.HEATER: ComponentCategoryType.LOADS,
    ComponentType.LIGHTING_FIXTURE: ComponentCategoryType.LOADS,
    ComponentType.FAN: ComponentCategoryType.LOADS,
    ComponentType.PUMP: ComponentCategoryType.LOADS,
    ComponentType.COMPRESSOR: ComponentCategoryType.LOADS,
    ComponentType.HVAC_UNIT: ComponentCategoryType.LOADS,
    ComponentType.OVEN: ComponentCategoryType.LOADS,
    ComponentType.FURNACE: ComponentCategoryType.LOADS,
    ComponentType.WELDING_MACHINE: ComponentCategoryType.LOADS,
    ComponentType.RECTIFIER: ComponentCategoryType.LOADS,


    # Communication
    ComponentType.ETHERNET_SWITCH: ComponentCategoryType.COMMUNICATION,
    ComponentType.WIRELESS_ACCESS_POINT: ComponentCategoryType.COMMUNICATION,
    ComponentType.FIBER_OPTIC_TRANSCIEVER: ComponentCategoryType.COMMUNICATION,
    ComponentType.INDUSTRIAL_ROUTER: ComponentCategoryType.COMMUNICATION,

    # Safety & Emergency
    ComponentType.EMERGENCY_STOP_BUTTON: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.FIRE_ALARM_CONTROL_PANEL: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.FIRE_ALARM_DETECTOR: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.STROBE_LIGHT: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.HORN: ComponentCategoryType.SAFETY_AND_EMERGENCY,
    ComponentType.EMERGENCY_LIGHTING_FIXTURE: ComponentCategoryType.SAFETY_AND_EMERGENCY,

    # Heat Tracing System
    ComponentType.HEAT_TRACING_CABLE: ComponentCategoryType.HEAT_TRACING,
    ComponentType.HEAT_TRACING_CONTROLLER: ComponentCategoryType.HEAT_TRACING,
    ComponentType.POWER_DISTRIBUTION_ENCLOSURE_HT: ComponentCategoryType.HEAT_TRACING,
    ComponentType.JUNCTION_BOX_HT: ComponentCategoryType.HEAT_TRACING,

    # Cable Management
    ComponentType.CABLE_TRAY: ComponentCategoryType.CABLE_MANAGEMENT,
    ComponentType.CONDUIT: ComponentCategoryType.CABLE_MANAGEMENT,
    ComponentType.CABLE_DUCT: ComponentCategoryType.CABLE_MANAGEMENT,
    ComponentType.CABLE_LADDER: ComponentCategoryType.CABLE_MANAGEMENT,

    # Other Electrical
    ComponentType.MISCELLANEOUS_ELECTRICAL: ComponentCategoryType.OTHER_ELECTRICAL,
}

class ComponentFunctionalCategory(Enum):
    """
    Functional categories for components, especially useful for systems engineering.
    This provides an alternative or complementary view to ComponentCategoryType.
    """
    POWER_GENERATION = "Power Generation"
    POWER_TRANSMISSION = "Power Transmission"
    POWER_DISTRIBUTION = "Power Distribution"
    PROCESS_CONTROL = "Process Control"
    SAFETY_INSTRUMENTED_SYSTEM = "Safety Instrumented System (SIS)"
    FIRE_AND_GAS_SYSTEM = "Fire & Gas System (F&G)"
    LIGHTING_SYSTEM = "Lighting System"
    COMMUNICATION_NETWORK = "Communication Network"
    MOTOR_CONTROL = "Motor Control"
    MEASUREMENT = "Measurement"
    ALARMING = "Alarming"
    HVAC_CONTROL = "HVAC Control"
    HEAT_TRACING_SYSTEM = "Heat Tracing System"
    DATA_ACQUISITION = "Data Acquisition"
    REMOTE_MONITORING = "Remote Monitoring"
    OTHER_SYSTEM_FUNCTION = "Other System Function"
```

### Component Model Implementation

```python
# core/models/components.py
"""Component and Component Category Data Models.

This module defines the data models for electrical components and their categories
in the Ultimate Electrical Designer backend application. It includes models for
component specifications, pricing, technical properties, and categorization.

Key models:
- ComponentCategory: Categories for organizing components
- Component: Individual electrical components with specifications and pricing

These models support the component management system and bill of materials
generation functionality.
"""

from typing import TYPE_CHECKING

from sqlalchemy import Boolean, Float, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.utils.json_validation import FlexibleJSON

from .base import (
    Base,
    CommonColumns,
    SoftDeleteColumns,
)

if TYPE_CHECKING:
    from .electrical import ElectricalNode


class ComponentCategory(CommonColumns, SoftDeleteColumns, Base):
    """Model representing a category for organizing electrical components.

    Component categories provide hierarchical organization for electrical
    components used in the Ultimate Electrical Designer system. Categories
    include type classification for professional electrical design workflows.
    """

    __tablename__ = "ComponentCategory"

    parent_category_id: Mapped[int | None] = mapped_column(
        ForeignKey("ComponentCategory.id"), nullable=True
    )
    description: Mapped[str | None] = mapped_column(Text, nullable=True)
    category_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True,
        comment="Component category type for professional electrical design classification (required)",
    )

    # Category status flags
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether the category is active and available for use",
    )

    # Relationships
    parent_category: Mapped["ComponentCategory | None"] = relationship(
        "ComponentCategory",
        remote_side=lambda: ComponentCategory.id,
        back_populates="sub_categories",
    )
    sub_categories: Mapped[list["ComponentCategory"]] = relationship(
        "ComponentCategory",
        back_populates="parent_category",
        cascade="all, delete-orphan",
    )
    components: Mapped[list["Component"]] = relationship(
        back_populates="category", cascade="all, delete-orphan"
    )

    __table_args__ = (UniqueConstraint("name", name="uq_component_category_name"),)

    def __repr__(self):
        """Return string representation of ComponentCategory instance.

        Returns:
            str: String representation showing ID and name.
        """
        return f"<ComponentCategory(id={self.id}, name='{self.name}')>"


class Component(CommonColumns, SoftDeleteColumns, Base):
    """Model representing an electrical component with specifications and pricing.

    Components are the building blocks of electrical systems, including
    cables, switches, protection devices, and other electrical equipment.
    Each component has technical specifications, pricing information,
    and categorization for bill of materials generation.
    """

    __tablename__ = "Component"

    category_id: Mapped[int] = mapped_column(
        ForeignKey("ComponentCategory.id"), nullable=False
    )
    manufacturer: Mapped[str | None] = mapped_column(nullable=True)
    model: Mapped[str | None] = mapped_column(nullable=True)

    # Enhanced component catalog fields
    component_type: Mapped[str] = mapped_column(
        String(50), nullable=False, index=True
    )  # Required: Component type for professional electrical design classification

    # Pricing fields
    unit_material_price: Mapped[float | None] = mapped_column(Float, nullable=True)
    unit_installation_price: Mapped[float | None] = mapped_column(Float, nullable=True)
    price_unit_of_measure: Mapped[str | None] = mapped_column(String(50), nullable=True)

    # Enhanced JSON field for technical properties
    technical_properties: Mapped[dict | None] = mapped_column(
        FlexibleJSON, nullable=True
    )

    # Legacy field for backward compatibility
    specific_data: Mapped[str | None] = mapped_column(Text, nullable=True)

    # Component selection and status flags
    is_selectable: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    category: Mapped["ComponentCategory"] = relationship(back_populates="components")
    electrical_node: Mapped["ElectricalNode | None"] = relationship(
        "ElectricalNode", back_populates="related_component", uselist=False
    )

    __table_args__ = (
        UniqueConstraint("name", "category_id", name="uq_component_name_category"),
    )

    def __repr__(self):
        """Return string representation of Component instance.

        Returns:
            str: String representation showing ID, name, and category.
        """
        return f"<Component(id={self.id}, name='{self.name}', category='{self.category.name}')>"
```

---

**Navigation:**  
← [Previous: Script Ecosystem](080-script-ecosystem.md) | [Handbook Home](001-cover.md) | [Next: Database Management](100-database-management.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Component Model Implementation](../../backend/core/models/component.py)
- [Heat Tracing Specifications](../../backend/docs/architecture-specifications/heat-tracing/)
- [Electrical System Design](../../backend/docs/architecture-specifications/electrical-systems/)
- [Standards Compliance](../../backend/docs/architecture-specifications/core/standards/)
