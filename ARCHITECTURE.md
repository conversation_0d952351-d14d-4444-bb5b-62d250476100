# Technical Architecture Document
## Ultimate Electrical Designer Application

### Document Information
- **Version**: 1.0
- **Date**: July 2025
- **Architecture Pattern**: 5-Layer Backend, Component-Based Frontend
- **Standards**: IEEE, IEC, EN electrical engineering standards
- **Status**: Active Development Architecture

---

## 1. Executive Summary

The Ultimate Electrical Designer employs a comprehensive 5-layer backend architecture with a modern component-based frontend, designed to deliver professional-grade electrical engineering calculations with absolute precision and complete standards compliance. This architecture ensures scalability, maintainability, and adherence to professional engineering standards.

### 1.1 Architecture Principles
- **Separation of Concerns**: Clear layer boundaries with defined responsibilities
- **Professional Standards**: IEEE, IEC, EN standards compliance throughout
- **Type Safety**: 100% type annotations and strict type checking
- **Security First**: Comprehensive security at every architectural layer
- **Performance Optimized**: Sub-200ms response times for critical operations
- **Scalability**: Horizontal scaling capabilities for enterprise deployment

### 1.2 Current Implementation Status
- **Backend Architecture**: 373/373 tests passing (100% success rate)
- **Frontend Architecture**: 66/66 tests passing (100% success rate)
- **Type Safety**: 97.6% MyPy coverage, strict TypeScript
- **Security**: Zero critical vulnerabilities, comprehensive audit trail
- **Performance**: Sub-200ms response times for current operations

---

## 2. System Architecture Overview

### 2.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
├─────────────────────────────────────────────────────────────────┤
│                     Next.js Frontend                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   UI Components │  │  State Management│  │   API Client    │ │
│  │   (shadcn/ui)   │  │  (React Query +  │  │  (TypeScript)   │ │
│  │                 │  │     Zustand)     │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                │ HTTPS/REST API
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    Backend System                               │
├─────────────────────────────────────────────────────────────────┤
│                     Layer 1: API Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   FastAPI       │  │   Authentication│  │   Middleware    │ │
│  │   Routes        │  │   & Security    │  │   & Validation  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                  Layer 2: Business Logic                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Service Layer │  │   Calculation   │  │   Standards     │ │
│  │   (Business     │  │   Engines       │  │   Validation    │ │
│  │    Rules)       │  │                 │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                  Layer 3: Data Access                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Repository    │  │   Query Builder │  │   Transaction   │ │
│  │   Pattern       │  │   & Optimizer   │  │   Management    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                  Layer 4: Data Validation                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Pydantic      │  │   Input         │  │   Output        │ │
│  │   Schemas       │  │   Validation    │  │   Serialization │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                  Layer 5: Data Models                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   SQLAlchemy    │  │   Relationships │  │   Constraints   │ │
│  │   Models        │  │   & Indexing    │  │   & Validation  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                │ Database Connection
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    Data Layer                                   │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   PostgreSQL    │  │   Redis Cache   │  │   File Storage  │ │
│  │   Database      │  │                 │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 Component Interaction Flow

```
User Request → API Layer → Business Logic → Data Access → Data Validation → Data Models → Database
     ↓              ↓              ↓              ↓              ↓              ↓
Response ← API Layer ← Business Logic ← Data Access ← Data Validation ← Data Models ← Database
```

---

## 3. Backend Architecture (5-Layer Pattern)

### 3.1 Layer 1: API Layer - Request/Response Handling

#### **Purpose**: Handle HTTP requests, route management, and response formatting
#### **Location**: `server/src/api/`
#### **Technologies**: FastAPI, Uvicorn, OpenAPI 3.0+

#### **Key Components**:

##### **Router System**
```python
# Main API Router Structure
server/src/api/
├── main_router.py              # Main application router
├── v1/                         # API version 1
│   ├── router.py              # V1 main router
│   ├── auth_routes.py         # Authentication endpoints
│   ├── user_routes.py         # User management endpoints
│   ├── component_routes.py    # Component management endpoints
│   ├── component_category_routes.py  # Component categories
│   ├── component_type_routes.py      # Component types
│   └── calculation_routes.py  # Calculation endpoints
└── middleware/                 # API middleware
    ├── auth_middleware.py     # Authentication middleware
    ├── cors_middleware.py     # CORS configuration
    └── rate_limiting.py       # Rate limiting
```

##### **Authentication & Security**
```python
# JWT Authentication Implementation
from fastapi.security import HTTPBearer
from core.security.jwt_handler import JWTHandler

security = HTTPBearer()

@router.post("/login")
async def login(credentials: LoginCredentials):
    user = await auth_service.authenticate(credentials)
    token = JWTHandler.create_token(user.id)
    return {"access_token": token, "token_type": "bearer"}

@router.get("/protected")
async def protected_route(token: str = Depends(security)):
    payload = JWTHandler.validate_token(token)
    return {"user_id": payload["sub"]}
```

##### **CRUD Endpoint Factory**
```python
# Automated CRUD endpoint generation
from core.utils.crud_endpoint_factory import create_crud_endpoints

def create_component_endpoints(router: APIRouter):
    return create_crud_endpoints(
        router=router,
        model=Component,
        schema_create=ComponentCreate,
        schema_update=ComponentUpdate,
        schema_response=ComponentResponse,
        service=component_service,
        prefix="/components"
    )
```

#### **API Specifications**:
- **OpenAPI 3.0+**: Automatic documentation generation
- **RESTful Design**: Standard HTTP methods and status codes
- **JSON API**: Consistent request/response formats
- **Error Handling**: Standardized error responses
- **Versioning**: API versioning with backward compatibility

### 3.2 Layer 2: Business Logic Layer - Domain Logic

#### **Purpose**: Implement business rules, electrical engineering logic, and workflow orchestration
#### **Location**: `server/src/core/services/`
#### **Technologies**: Python, Domain-Driven Design, Dependency Injection

#### **Key Components**:

##### **Service Architecture**
```python
# Service Layer Structure
server/src/core/services/
├── dependencies.py            # Service dependencies
├── auth/                      # Authentication services
│   ├── auth_service.py       # Authentication logic
│   └── user_service.py       # User management
├── general/                   # General domain services
│   ├── component_service.py  # Component business logic
│   ├── component_category_service.py  # Category logic
│   └── component_type_service.py     # Type logic
└── calculations/              # Calculation services
    ├── load_calculation_service.py      # Load calculations
    ├── voltage_drop_service.py         # Voltage drop
    └── short_circuit_service.py        # Short circuit
```

##### **Electrical Engineering Service Example**
```python
# Component Service with Professional Standards
class ComponentService:
    def __init__(self, repository: ComponentRepository):
        self.repository = repository
    
    @handle_database_errors
    @monitor_performance
    async def calculate_component_load(
        self, 
        component_id: str, 
        conditions: ElectricalConditions
    ) -> LoadCalculationResult:
        # Retrieve component with specifications
        component = await self.repository.get_by_id(component_id)
        
        # Apply IEEE standards validation
        validator = IEEE141Validator()
        validated_conditions = validator.validate(conditions)
        
        # Perform electrical calculations
        calculator = ElectricalCalculator()
        result = calculator.calculate_load(
            component.specifications,
            validated_conditions
        )
        
        # Store calculation history
        await self.repository.store_calculation(
            component_id=component_id,
            calculation_type="load",
            result=result,
            standards_used=["IEEE-141"]
        )
        
        return result
```

##### **Standards Compliance Integration**
```python
# Professional Standards Service
class StandardsService:
    def __init__(self, standards_repository: StandardsRepository):
        self.standards_repository = standards_repository
    
    async def validate_ieee_compliance(
        self, 
        calculation_data: CalculationData
    ) -> ComplianceResult:
        # Load IEEE standards
        ieee_standards = await self.standards_repository.get_ieee_standards()
        
        # Validate against IEEE-141 (Power System Analysis)
        ieee_141 = ieee_standards.get("IEEE-141")
        load_compliance = self._validate_load_calculation(
            calculation_data, ieee_141
        )
        
        # Validate against IEEE-142 (Grounding)
        ieee_142 = ieee_standards.get("IEEE-142")
        grounding_compliance = self._validate_grounding(
            calculation_data, ieee_142
        )
        
        return ComplianceResult(
            ieee_141_compliant=load_compliance.is_compliant,
            ieee_142_compliant=grounding_compliance.is_compliant,
            recommendations=load_compliance.recommendations
        )
```

#### **Business Logic Patterns**:
- **Domain-Driven Design**: Clear domain boundaries and ubiquitous language
- **Command Query Responsibility Segregation (CQRS)**: Separate read/write operations
- **Event-Driven Architecture**: Asynchronous processing for complex calculations
- **Professional Standards**: IEEE, IEC, EN compliance throughout

### 3.3 Layer 3: Data Access Layer - Repository Pattern

#### **Purpose**: Abstract database operations and provide data persistence
#### **Location**: `server/src/core/repositories/`
#### **Technologies**: SQLAlchemy 2.0+, Async/Await, Connection Pooling

#### **Key Components**:

##### **Repository Architecture**
```python
# Repository Layer Structure
server/src/core/repositories/
├── base_repository.py         # Base repository class
├── repository_dependencies.py # Repository dependencies
├── auth/                      # Authentication repositories
│   ├── user_repository.py    # User data access
│   └── role_repository.py    # Role management
├── general/                   # General domain repositories
│   ├── component_repository.py        # Component data access
│   ├── component_category_repository.py  # Category data access
│   └── component_type_repository.py      # Type data access
└── calculations/              # Calculation repositories
    ├── calculation_result_repository.py  # Calculation storage
    └── calculation_history_repository.py # History tracking
```

##### **Generic Repository Pattern**
```python
# Base Repository with Type Safety
from typing import Generic, TypeVar, Type, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession

T = TypeVar('T')

class BaseRepository(Generic[T]):
    def __init__(self, model: Type[T], session: AsyncSession):
        self.model = model
        self.session = session
    
    async def get_by_id(self, id: str) -> Optional[T]:
        return await self.session.get(self.model, id)
    
    async def get_all(
        self, 
        filters: Optional[Dict[str, Any]] = None,
        offset: int = 0,
        limit: int = 100
    ) -> List[T]:
        query = select(self.model)
        
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key):
                    query = query.where(getattr(self.model, key) == value)
        
        query = query.offset(offset).limit(limit)
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def create(self, entity: T) -> T:
        self.session.add(entity)
        await self.session.commit()
        await self.session.refresh(entity)
        return entity
    
    async def update(self, entity: T) -> T:
        await self.session.commit()
        await self.session.refresh(entity)
        return entity
    
    async def delete(self, id: str) -> bool:
        entity = await self.get_by_id(id)
        if entity:
            await self.session.delete(entity)
            await self.session.commit()
            return True
        return False
```

##### **Specialized Repository Example**
```python
# Component Repository with Electrical Engineering Queries
class ComponentRepository(BaseRepository[Component]):
    async def get_by_specifications(
        self, 
        specifications: ComponentSpecifications
    ) -> List[Component]:
        query = select(Component).where(
            Component.voltage_rating >= specifications.min_voltage,
            Component.current_rating >= specifications.min_current,
            Component.power_rating >= specifications.min_power
        )
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def get_by_category_tree(
        self, 
        category_id: str, 
        include_subcategories: bool = True
    ) -> List[Component]:
        if include_subcategories:
            # Recursive query for category hierarchy
            category_tree = await self._get_category_tree(category_id)
            category_ids = [cat.id for cat in category_tree]
            query = select(Component).where(
                Component.category_id.in_(category_ids)
            )
        else:
            query = select(Component).where(
                Component.category_id == category_id
            )
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def search_components(
        self, 
        search_criteria: ComponentSearchCriteria
    ) -> List[Component]:
        query = select(Component)
        
        # Text search
        if search_criteria.text:
            query = query.where(
                or_(
                    Component.name.ilike(f"%{search_criteria.text}%"),
                    Component.description.ilike(f"%{search_criteria.text}%")
                )
            )
        
        # Electrical specifications
        if search_criteria.voltage_range:
            query = query.where(
                Component.voltage_rating.between(
                    search_criteria.voltage_range.min,
                    search_criteria.voltage_range.max
                )
            )
        
        # Standards compliance
        if search_criteria.standards:
            query = query.where(
                Component.compliance_standards.contains(
                    search_criteria.standards
                )
            )
        
        result = await self.session.execute(query)
        return result.scalars().all()
```

#### **Data Access Patterns**:
- **Unit of Work**: Transaction management and consistency
- **Query Builder**: Dynamic query construction
- **Connection Pooling**: Efficient database connection management
- **Caching Strategy**: Redis integration for frequently accessed data

### 3.4 Layer 4: Data Validation Layer - Schema Validation

#### **Purpose**: Validate and transform data between layers
#### **Location**: `server/src/core/schemas/`
#### **Technologies**: Pydantic 2.0+, Type Validation, Custom Validators

#### **Key Components**:

##### **Schema Architecture**
```python
# Schema Layer Structure
server/src/core/schemas/
├── base.py                    # Base schema classes
├── auth/                      # Authentication schemas
│   ├── user_schemas.py       # User validation schemas
│   └── auth_schemas.py       # Authentication schemas
├── general/                   # General domain schemas
│   ├── component_schemas.py  # Component validation
│   ├── component_category_schemas.py  # Category validation
│   └── component_type_schemas.py     # Type validation
└── calculations/              # Calculation schemas
    ├── load_calculation_schemas.py      # Load calculation validation
    ├── voltage_drop_schemas.py         # Voltage drop validation
    └── short_circuit_schemas.py        # Short circuit validation
```

##### **Professional Schema Validation**
```python
# Component Schema with Electrical Engineering Validation
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from enum import Enum

class VoltageLevel(str, Enum):
    LOW_VOLTAGE = "low_voltage"      # < 1000V
    MEDIUM_VOLTAGE = "medium_voltage" # 1000V - 35kV
    HIGH_VOLTAGE = "high_voltage"    # > 35kV

class ElectricalSpecifications(BaseModel):
    voltage_rating: float = Field(..., gt=0, description="Voltage rating in volts")
    current_rating: float = Field(..., gt=0, description="Current rating in amperes")
    power_rating: Optional[float] = Field(None, gt=0, description="Power rating in watts")
    frequency: float = Field(50.0, description="Frequency in Hz")
    power_factor: Optional[float] = Field(None, ge=0, le=1, description="Power factor")
    efficiency: Optional[float] = Field(None, ge=0, le=1, description="Efficiency")
    
    @validator('voltage_rating')
    def validate_voltage_rating(cls, v):
        if v <= 0:
            raise ValueError('Voltage rating must be positive')
        if v > 1000000:  # 1MV limit
            raise ValueError('Voltage rating exceeds maximum limit')
        return v
    
    @validator('current_rating')
    def validate_current_rating(cls, v):
        if v <= 0:
            raise ValueError('Current rating must be positive')
        if v > 100000:  # 100kA limit
            raise ValueError('Current rating exceeds maximum limit')
        return v
    
    @validator('power_factor')
    def validate_power_factor(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError('Power factor must be between 0 and 1')
        return v

class ComponentCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    category_id: str = Field(..., description="Component category ID")
    type_id: str = Field(..., description="Component type ID")
    manufacturer: Optional[str] = Field(None, max_length=255)
    model_number: Optional[str] = Field(None, max_length=100)
    specifications: ElectricalSpecifications
    compliance_standards: List[str] = Field(default_factory=list)
    
    @validator('compliance_standards')
    def validate_standards(cls, v):
        valid_standards = ['IEEE-141', 'IEEE-142', 'IEC-60364', 'EN-50110']
        for standard in v:
            if standard not in valid_standards:
                raise ValueError(f'Invalid standard: {standard}')
        return v

class ComponentResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    category_id: str
    type_id: str
    manufacturer: Optional[str]
    model_number: Optional[str]
    specifications: ElectricalSpecifications
    compliance_standards: List[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
```

##### **Calculation Schema Validation**
```python
# Load Calculation Schema with IEEE Standards
class LoadCalculationInput(BaseModel):
    component_ids: List[str] = Field(..., min_items=1)
    voltage_level: VoltageLevel
    power_factor: float = Field(..., ge=0, le=1)
    temperature: float = Field(25.0, ge=-40, le=85, description="Temperature in Celsius")
    altitude: float = Field(0.0, ge=0, le=5000, description="Altitude in meters")
    installation_method: str = Field(..., description="Installation method")
    diversity_factor: float = Field(1.0, ge=0, le=1, description="Diversity factor")
    demand_factor: float = Field(1.0, ge=0, le=1, description="Demand factor")
    
    @validator('component_ids')
    def validate_component_ids(cls, v):
        if len(v) > 1000:
            raise ValueError('Maximum 1000 components per calculation')
        return v
    
    @validator('installation_method')
    def validate_installation_method(cls, v):
        valid_methods = [
            'cable_tray', 'conduit', 'direct_burial', 
            'overhead', 'free_air', 'enclosed'
        ]
        if v not in valid_methods:
            raise ValueError(f'Invalid installation method: {v}')
        return v

class LoadCalculationResult(BaseModel):
    calculation_id: str
    total_load: float = Field(..., description="Total load in watts")
    connected_load: float = Field(..., description="Connected load in watts")
    demand_load: float = Field(..., description="Demand load in watts")
    current_load: float = Field(..., description="Current load in amperes")
    power_factor: float = Field(..., description="Resulting power factor")
    components: List[ComponentLoadResult]
    compliance_status: ComplianceStatus
    calculation_timestamp: datetime
    standards_used: List[str]
    
    class Config:
        from_attributes = True
```

#### **Validation Patterns**:
- **Type Safety**: Comprehensive type validation and conversion
- **Business Rules**: Domain-specific validation logic
- **Custom Validators**: Electrical engineering specific validators
- **Error Handling**: Detailed validation error messages

### 3.5 Layer 5: Data Models - Object-Relational Mapping

#### **Purpose**: Define data structures, relationships, and constraints
#### **Location**: `server/src/core/models/`
#### **Technologies**: SQLAlchemy 2.0+, Alembic, PostgreSQL/SQLite

#### **Key Components**:

##### **Model Architecture**
```python
# Model Layer Structure
server/src/core/models/
├── __init__.py                # Model imports
├── base.py                    # Base model classes
├── auth/                      # Authentication models
│   ├── user.py               # User model
│   └── role.py               # Role model
├── general/                   # General domain models
│   ├── component.py          # Component model
│   ├── component_category.py # Category model
│   └── component_type.py     # Type model
└── calculations/              # Calculation models
    ├── calculation_result.py  # Calculation storage
    ├── calculation_history.py # History tracking
    └── standards_validation.py # Standards compliance
```

##### **Base Model Implementation**
```python
# Base Model with Common Fields
from sqlalchemy import Column, String, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Mapped, mapped_column
import uuid
from datetime import datetime

Base = declarative_base()

class BaseModel(Base):
    __abstract__ = True
    
    id: Mapped[str] = mapped_column(
        String, 
        primary_key=True, 
        default=lambda: str(uuid.uuid4())
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=func.now(),
        onupdate=func.now()
    )
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"
```

##### **Professional Electrical Model**
```python
# Component Model with Electrical Engineering Fields
from sqlalchemy import Column, String, Text, Float, JSON, ForeignKey
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import Dict, Any, List

class Component(BaseModel):
    __tablename__ = 'components'
    
    # Basic Information
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    manufacturer: Mapped[str] = mapped_column(String(255), nullable=True)
    model_number: Mapped[str] = mapped_column(String(100), nullable=True)
    
    # Relationships
    category_id: Mapped[str] = mapped_column(
        String, 
        ForeignKey('component_categories.id'), 
        nullable=False
    )
    type_id: Mapped[str] = mapped_column(
        String, 
        ForeignKey('component_types.id'), 
        nullable=False
    )
    
    # Electrical Specifications (JSON field for flexibility)
    specifications: Mapped[Dict[str, Any]] = mapped_column(
        JSON, 
        nullable=False,
        default=dict
    )
    
    # Standards Compliance
    compliance_standards: Mapped[List[str]] = mapped_column(
        JSON, 
        nullable=False,
        default=list
    )
    
    # Relationships
    category = relationship("ComponentCategory", back_populates="components")
    type = relationship("ComponentType", back_populates="components")
    calculation_results = relationship("CalculationResult", back_populates="component")
    
    # Electrical Engineering Properties
    @property
    def voltage_rating(self) -> float:
        return self.specifications.get('voltage_rating', 0.0)
    
    @property
    def current_rating(self) -> float:
        return self.specifications.get('current_rating', 0.0)
    
    @property
    def power_rating(self) -> float:
        return self.specifications.get('power_rating', 0.0)
    
    @property
    def power_factor(self) -> float:
        return self.specifications.get('power_factor', 1.0)
    
    def is_ieee_compliant(self) -> bool:
        ieee_standards = [s for s in self.compliance_standards if s.startswith('IEEE')]
        return len(ieee_standards) > 0
    
    def get_load_characteristics(self) -> Dict[str, Any]:
        return {
            'voltage_rating': self.voltage_rating,
            'current_rating': self.current_rating,
            'power_rating': self.power_rating,
            'power_factor': self.power_factor,
            'efficiency': self.specifications.get('efficiency', 0.9)
        }
```

##### **Calculation Result Model**
```python
# Calculation Result with Audit Trail
class CalculationResult(BaseModel):
    __tablename__ = 'calculation_results'
    
    # Calculation Information
    calculation_type: Mapped[str] = mapped_column(String(50), nullable=False)
    component_id: Mapped[str] = mapped_column(
        String, 
        ForeignKey('components.id'), 
        nullable=False
    )
    
    # Input Parameters
    input_parameters: Mapped[Dict[str, Any]] = mapped_column(
        JSON, 
        nullable=False
    )
    
    # Calculation Results
    results: Mapped[Dict[str, Any]] = mapped_column(
        JSON, 
        nullable=False
    )
    
    # Standards Compliance
    standards_used: Mapped[List[str]] = mapped_column(
        JSON, 
        nullable=False,
        default=list
    )
    compliance_status: Mapped[Dict[str, Any]] = mapped_column(
        JSON, 
        nullable=False,
        default=dict
    )
    
    # Performance Metrics
    calculation_time_ms: Mapped[float] = mapped_column(Float, nullable=True)
    memory_usage_mb: Mapped[float] = mapped_column(Float, nullable=True)
    
    # Audit Information
    calculated_by: Mapped[str] = mapped_column(String, nullable=False)
    calculation_timestamp: Mapped[datetime] = mapped_column(
        DateTime, 
        default=func.now()
    )
    
    # Relationships
    component = relationship("Component", back_populates="calculation_results")
    history = relationship("CalculationHistory", back_populates="calculation_result")
```

#### **Model Patterns**:
- **Inheritance Hierarchy**: Base model with common fields
- **Relationship Mapping**: Proper foreign key relationships
- **JSON Fields**: Flexible schema for electrical specifications
- **Indexing Strategy**: Optimized database indexing for performance

---

## 4. Frontend Architecture (Component-Based)

### 4.1 Next.js Application Structure

#### **Purpose**: Modern React application with server-side rendering and static generation
#### **Location**: `client/src/`
#### **Technologies**: Next.js 15+, React 18+, TypeScript 5+

#### **Key Components**:

##### **Application Structure**
```
client/src/
├── app/                       # Next.js App Router
│   ├── globals.css           # Global styles
│   ├── layout.tsx            # Root layout
│   ├── page.tsx              # Home page
│   ├── (auth)/               # Authentication pages
│   │   ├── login/           # Login page
│   │   └── register/        # Registration page
│   ├── admin/               # Admin dashboard
│   │   ├── layout.tsx       # Admin layout
│   │   ├── page.tsx         # Admin dashboard
│   │   └── users/           # User management
│   └── profile/             # User profile
├── components/               # React Components
│   ├── ui/                  # shadcn/ui base components
│   │   ├── button.tsx       # Button component
│   │   ├── input.tsx        # Input component
│   │   ├── card.tsx         # Card component
│   │   └── form.tsx         # Form component
│   ├── auth/                # Authentication components
│   │   ├── login-form.tsx   # Login form
│   │   ├── user-profile.tsx # User profile
│   │   └── route-guard.tsx  # Route protection
│   ├── admin/               # Admin components
│   │   ├── user-management.tsx  # User management
│   │   ├── dashboard.tsx    # Admin dashboard
│   │   └── statistics.tsx   # Statistics charts
│   └── common/              # Common components
│       ├── header.tsx       # Application header
│       ├── footer.tsx       # Application footer
│       └── sidebar.tsx      # Navigation sidebar
├── hooks/                   # Custom React Hooks
│   ├── useAuth.ts          # Authentication hook
│   ├── useApi.ts           # API client hook
│   └── api/                # API-specific hooks
│       ├── useComponents.ts # Component management
│       ├── useUsers.ts     # User management
│       └── useCalculations.ts # Calculation hooks
├── lib/                    # Core Libraries
│   ├── api/               # API client
│   │   ├── client.ts      # Main API client
│   │   └── types.ts       # API type definitions
│   ├── auth/              # Authentication utilities
│   │   ├── token-manager.ts # JWT token management
│   │   └── auth-utils.ts  # Authentication utilities
│   └── react-query.tsx    # React Query configuration
├── stores/                # Zustand State Management
│   ├── auth-store.ts      # Authentication state
│   ├── user-store.ts      # User state
│   └── calculation-store.ts # Calculation state
├── types/                 # TypeScript Definitions
│   ├── auth.ts           # Authentication types
│   ├── user.ts           # User types
│   ├── component.ts      # Component types
│   └── calculation.ts    # Calculation types
└── utils/                # Utility Functions
    ├── formatting.ts     # Data formatting
    ├── validation.ts     # Form validation
    └── constants.ts      # Application constants
```

##### **Component Architecture Pattern**
```typescript
// Atomic Design Component Example
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/useAuth';

// Atom: Basic UI Component
export const ElectricalInput: React.FC<ElectricalInputProps> = ({
  label,
  value,
  onChange,
  unit,
  min,
  max,
  precision = 2
}) => {
  return (
    <div className="flex flex-col space-y-2">
      <label className="text-sm font-medium">{label}</label>
      <div className="flex items-center space-x-2">
        <Input
          type="number"
          value={value}
          onChange={(e) => onChange(parseFloat(e.target.value))}
          min={min}
          max={max}
          step={Math.pow(10, -precision)}
          className="flex-1"
        />
        <span className="text-sm text-gray-500">{unit}</span>
      </div>
    </div>
  );
};

// Molecule: Composite Component
export const ComponentSpecificationForm: React.FC<ComponentSpecificationFormProps> = ({
  specifications,
  onChange
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Electrical Specifications</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <ElectricalInput
          label="Voltage Rating"
          value={specifications.voltage_rating}
          onChange={(value) => onChange({ ...specifications, voltage_rating: value })}
          unit="V"
          min={0}
          max={1000000}
          precision={1}
        />
        <ElectricalInput
          label="Current Rating"
          value={specifications.current_rating}
          onChange={(value) => onChange({ ...specifications, current_rating: value })}
          unit="A"
          min={0}
          max={100000}
          precision={2}
        />
        <ElectricalInput
          label="Power Rating"
          value={specifications.power_rating}
          onChange={(value) => onChange({ ...specifications, power_rating: value })}
          unit="W"
          min={0}
          max={10000000}
          precision={0}
        />
      </CardContent>
    </Card>
  );
};

// Organism: Complex Component
export const ComponentManagement: React.FC = () => {
  const { user } = useAuth();
  const [specifications, setSpecifications] = useState<ElectricalSpecifications>({
    voltage_rating: 0,
    current_rating: 0,
    power_rating: 0,
    power_factor: 1.0
  });

  const { mutate: createComponent } = useCreateComponent();

  const handleSubmit = (data: ComponentCreateData) => {
    createComponent({
      ...data,
      specifications,
      created_by: user?.id
    });
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <h1 className="text-3xl font-bold">Component Management</h1>
      
      <ComponentSpecificationForm
        specifications={specifications}
        onChange={setSpecifications}
      />
      
      <Button 
        onClick={handleSubmit}
        className="w-full"
        disabled={!user?.can_create_components}
      >
        Create Component
      </Button>
    </div>
  );
};
```

### 4.2 State Management Architecture

#### **Purpose**: Efficient state management with React Query and Zustand
#### **Technologies**: React Query (TanStack Query), Zustand, TypeScript

#### **Key Components**:

##### **React Query Configuration**
```typescript
// React Query Setup with Professional Caching
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

export const ReactQueryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};
```

##### **Zustand Store Implementation**
```typescript
// Authentication Store with Professional Features
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  
  // Professional Features
  hasPermission: (permission: string) => boolean;
  canAccess: (resource: string) => boolean;
  isAdmin: () => boolean;
  isProfessionalEngineer: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authApi.login(credentials);
          const { user, token } = response.data;
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
          
          // Store token in API client
          apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        } catch (error) {
          set({
            error: error.message,
            isLoading: false,
            isAuthenticated: false
          });
        }
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null
        });
        
        // Clear token from API client
        delete apiClient.defaults.headers.common['Authorization'];
      },
      
      refreshToken: async () => {
        const { token } = get();
        if (!token) return;
        
        try {
          const response = await authApi.refreshToken(token);
          const { user, token: newToken } = response.data;
          
          set({ user, token: newToken });
          apiClient.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
        } catch (error) {
          get().logout();
        }
      },
      
      updateUser: (userData) => {
        set(state => ({
          user: state.user ? { ...state.user, ...userData } : null
        }));
      },
      
      hasPermission: (permission) => {
        const { user } = get();
        return user?.permissions?.includes(permission) || false;
      },
      
      canAccess: (resource) => {
        const { user } = get();
        return user?.roles?.some(role => 
          role.permissions?.includes(resource)
        ) || false;
      },
      
      isAdmin: () => {
        const { user } = get();
        return user?.roles?.some(role => role.name === 'admin') || false;
      },
      
      isProfessionalEngineer: () => {
        const { user } = get();
        return user?.certifications?.includes('PE') || false;
      }
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
```

##### **API Integration Hooks**
```typescript
// Component Management Hooks with Professional Features
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export const useComponents = (filters?: ComponentFilters) => {
  return useQuery({
    queryKey: ['components', filters],
    queryFn: () => componentsApi.getComponents(filters),
    select: (data) => data.data,
    staleTime: 2 * 60 * 1000, // 2 minutes for component data
  });
};

export const useComponent = (id: string) => {
  return useQuery({
    queryKey: ['component', id],
    queryFn: () => componentsApi.getComponent(id),
    select: (data) => data.data,
    enabled: !!id,
  });
};

export const useCreateComponent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: componentsApi.createComponent,
    onSuccess: (data) => {
      // Invalidate components list
      queryClient.invalidateQueries({ queryKey: ['components'] });
      
      // Add to cache
      queryClient.setQueryData(['component', data.data.id], data);
      
      // Show success notification
      toast.success('Component created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create component: ${error.message}`);
    }
  });
};

export const useElectricalCalculation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: calculationsApi.calculateLoad,
    onSuccess: (data) => {
      // Cache calculation result
      queryClient.setQueryData(
        ['calculation', data.data.calculation_id], 
        data
      );
      
      // Invalidate calculation history
      queryClient.invalidateQueries({ queryKey: ['calculations'] });
      
      toast.success('Calculation completed successfully');
    },
    onError: (error) => {
      toast.error(`Calculation failed: ${error.message}`);
    }
  });
};
```

### 4.3 UI Component Library (shadcn/ui)

#### **Purpose**: Professional UI components with accessibility and customization
#### **Technologies**: shadcn/ui, Radix UI, Tailwind CSS, TypeScript

#### **Key Components**:

##### **Professional Component Theme**
```typescript
// Tailwind Configuration for Electrical Engineering
const config = {
  theme: {
    extend: {
      colors: {
        // Professional Electrical Engineering Colors
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          500: '#3b82f6',  // Engineering blue
          600: '#2563eb',
          700: '#1d4ed8',
          900: '#1e3a8a',
        },
        danger: {
          50: '#fef2f2',
          100: '#fee2e2',
          500: '#ef4444',  // Alert red
          600: '#dc2626',
          700: '#b91c1c',
        },
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          500: '#22c55e',  // Success green
          600: '#16a34a',
          700: '#15803d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          500: '#f59e0b',  // Warning amber
          600: '#d97706',
          700: '#b45309',
        },
        electrical: {
          voltage: '#ff6b35',    // Voltage orange
          current: '#4a90e2',    // Current blue
          power: '#7b68ee',      // Power purple
          ground: '#8b4513',     // Ground brown
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
};
```

##### **Professional Component Examples**
```typescript
// Electrical Engineering Specific Components
export const ElectricalMeter: React.FC<ElectricalMeterProps> = ({
  label,
  value,
  unit,
  min,
  max,
  precision = 2,
  color = 'primary',
  showAlert = true
}) => {
  const percentage = ((value - min) / (max - min)) * 100;
  const isAlert = showAlert && (percentage > 90 || percentage < 10);
  
  return (
    <Card className={`p-4 ${isAlert ? 'border-danger-500' : 'border-gray-200'}`}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">{label}</span>
        <span className={`text-lg font-bold ${isAlert ? 'text-danger-600' : 'text-gray-900'}`}>
          {value.toFixed(precision)} {unit}
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isAlert ? 'bg-danger-500' : `bg-${color}-500`
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      
      <div className="flex justify-between text-xs text-gray-500 mt-1">
        <span>{min}</span>
        <span>{max}</span>
      </div>
      
      {isAlert && (
        <div className="mt-2 flex items-center text-danger-600">
          <AlertTriangle className="w-4 h-4 mr-1" />
          <span className="text-xs">Value outside normal range</span>
        </div>
      )}
    </Card>
  );
};

export const CalculationResults: React.FC<CalculationResultsProps> = ({
  results,
  standards,
  onExport
}) => {
  return (
    <Card className="p-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calculator className="w-5 h-5 mr-2" />
          Calculation Results
        </CardTitle>
        <div className="flex gap-2">
          {standards.map((standard) => (
            <Badge key={standard} variant="secondary">
              {standard}
            </Badge>
          ))}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ElectricalMeter
            label="Load Current"
            value={results.current_load}
            unit="A"
            min={0}
            max={results.max_current}
            color="electrical-current"
          />
          
          <ElectricalMeter
            label="Power Consumption"
            value={results.power_consumption}
            unit="W"
            min={0}
            max={results.max_power}
            color="electrical-power"
          />
          
          <ElectricalMeter
            label="Voltage Drop"
            value={results.voltage_drop}
            unit="V"
            min={0}
            max={results.max_voltage_drop}
            color="electrical-voltage"
          />
        </div>
        
        <div className="mt-6 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-success-500" />
            <span className="text-sm text-success-600">
              IEEE-141 Compliant
            </span>
          </div>
          
          <Button onClick={onExport} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
```

### 4.4 Authentication & Authorization

#### **Purpose**: Secure user authentication with role-based access control
#### **Technologies**: JWT, React Query, Zustand, TypeScript

#### **Key Components**:

##### **Authentication Hook**
```typescript
// Comprehensive Authentication Hook
export const useAuth = () => {
  const authStore = useAuthStore();
  const queryClient = useQueryClient();
  
  // Get current user data
  const { data: user, isLoading } = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: () => authApi.getCurrentUser(),
    enabled: !!authStore.token,
    staleTime: 5 * 60 * 1000,
    select: (data) => data.data,
    onError: () => {
      authStore.logout();
    }
  });
  
  // Login mutation
  const loginMutation = useMutation({
    mutationFn: authApi.login,
    onSuccess: (response) => {
      const { user, token } = response.data;
      authStore.login({ user, token });
      queryClient.setQueryData(['auth', 'user'], user);
    },
    onError: (error) => {
      toast.error(`Login failed: ${error.message}`);
    }
  });
  
  // Logout function
  const logout = useCallback(() => {
    authStore.logout();
    queryClient.clear();
    router.push('/login');
  }, [authStore, queryClient]);
  
  // Permission checking
  const hasPermission = useCallback((permission: string) => {
    return user?.permissions?.includes(permission) || false;
  }, [user]);
  
  const canAccess = useCallback((resource: string) => {
    return user?.roles?.some(role => 
      role.permissions?.includes(resource)
    ) || false;
  }, [user]);
  
  const isAdmin = useCallback(() => {
    return user?.roles?.some(role => role.name === 'admin') || false;
  }, [user]);
  
  const isProfessionalEngineer = useCallback(() => {
    return user?.certifications?.includes('PE') || false;
  }, [user]);
  
  return {
    user,
    isLoading: isLoading || loginMutation.isLoading,
    isAuthenticated: !!user,
    login: loginMutation.mutate,
    logout,
    hasPermission,
    canAccess,
    isAdmin,
    isProfessionalEngineer,
    error: loginMutation.error
  };
};
```

##### **Route Protection**
```typescript
// Route Guard Component
export const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requiredPermission,
  requiredRole,
  adminOnly = false,
  peOnly = false
}) => {
  const { user, isLoading, isAuthenticated, hasPermission, canAccess, isAdmin, isProfessionalEngineer } = useAuth();
  const router = useRouter();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }
    
    if (!isLoading && isAuthenticated) {
      // Check admin access
      if (adminOnly && !isAdmin()) {
        router.push('/unauthorized');
        return;
      }
      
      // Check professional engineer access
      if (peOnly && !isProfessionalEngineer()) {
        router.push('/unauthorized');
        return;
      }
      
      // Check specific permission
      if (requiredPermission && !hasPermission(requiredPermission)) {
        router.push('/unauthorized');
        return;
      }
      
      // Check role access
      if (requiredRole && !canAccess(requiredRole)) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [isLoading, isAuthenticated, adminOnly, peOnly, requiredPermission, requiredRole]);
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }
  
  if (!isAuthenticated) {
    return null;
  }
  
  return <>{children}</>;
};
```

---

## 5. Database Architecture

### 5.1 Database Schema Design

#### **Purpose**: Professional electrical engineering data storage with audit trails
#### **Technologies**: PostgreSQL (production), SQLite (development), Alembic migrations

#### **Key Components**:

##### **Core Tables Structure**
```sql
-- Users and Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    certifications JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Component Categories (Hierarchical)
CREATE TABLE component_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES component_categories(id),
    category_code VARCHAR(50) UNIQUE,
    specifications_template JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Component Types
CREATE TABLE component_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES component_categories(id) NOT NULL,
    type_code VARCHAR(50) UNIQUE,
    specifications_template JSONB DEFAULT '{}',
    default_specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Electrical Components
CREATE TABLE components (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    manufacturer VARCHAR(255),
    model_number VARCHAR(100),
    category_id UUID REFERENCES component_categories(id) NOT NULL,
    type_id UUID REFERENCES component_types(id) NOT NULL,
    specifications JSONB NOT NULL DEFAULT '{}',
    compliance_standards JSONB NOT NULL DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Calculation Results with Audit Trail
CREATE TABLE calculation_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    calculation_type VARCHAR(50) NOT NULL,
    component_id UUID REFERENCES components(id),
    input_parameters JSONB NOT NULL,
    results JSONB NOT NULL,
    standards_used JSONB NOT NULL DEFAULT '[]',
    compliance_status JSONB NOT NULL DEFAULT '{}',
    calculation_time_ms FLOAT,
    memory_usage_mb FLOAT,
    calculated_by UUID REFERENCES users(id) NOT NULL,
    calculation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Activity Logs for Audit Trail
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Indexing Strategy**
```sql
-- Performance Indexes
CREATE INDEX idx_components_category_id ON components(category_id);
CREATE INDEX idx_components_type_id ON components(type_id);
CREATE INDEX idx_components_manufacturer ON components(manufacturer);
CREATE INDEX idx_components_specifications ON components USING GIN(specifications);

-- Calculation Performance Indexes
CREATE INDEX idx_calculation_results_type ON calculation_results(calculation_type);
CREATE INDEX idx_calculation_results_component ON calculation_results(component_id);
CREATE INDEX idx_calculation_results_timestamp ON calculation_results(calculation_timestamp);

-- Audit Trail Indexes
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_action ON activity_logs(action);
CREATE INDEX idx_activity_logs_resource ON activity_logs(resource_type, resource_id);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);

-- Text Search Indexes
CREATE INDEX idx_components_name_search ON components USING GIN(to_tsvector('english', name));
CREATE INDEX idx_components_description_search ON components USING GIN(to_tsvector('english', description));
```

### 5.2 Database Relationships

#### **Entity Relationship Diagram**
```
Users (1) ←→ (∞) CalculationResults
Users (1) ←→ (∞) ActivityLogs

ComponentCategories (1) ←→ (∞) Components
ComponentCategories (1) ←→ (∞) ComponentCategories (self-referential)

ComponentTypes (1) ←→ (∞) Components
ComponentCategories (1) ←→ (∞) ComponentTypes

Components (1) ←→ (∞) CalculationResults
```

### 5.3 Data Migration Strategy

#### **Alembic Migration Management**
```python
# Migration File Structure
server/src/alembic/versions/
├── 001_initial_schema.py              # Initial database schema
├── 002_add_component_categories.py    # Component categories
├── 003_add_component_types.py         # Component types
├── 004_add_calculations.py            # Calculation results
├── 005_add_activity_logs.py           # Activity logging
└── 006_add_indexes.py                 # Performance indexes

# Migration Example
def upgrade():
    # Create components table
    op.create_table('components',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('manufacturer', sa.String(length=255), nullable=True),
        sa.Column('model_number', sa.String(length=100), nullable=True),
        sa.Column('category_id', sa.String(), nullable=False),
        sa.Column('type_id', sa.String(), nullable=False),
        sa.Column('specifications', sa.JSON(), nullable=False),
        sa.Column('compliance_standards', sa.JSON(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['category_id'], ['component_categories.id'], ),
        sa.ForeignKeyConstraint(['type_id'], ['component_types.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('idx_components_category_id', 'components', ['category_id'])
    op.create_index('idx_components_type_id', 'components', ['type_id'])
    op.create_index('idx_components_manufacturer', 'components', ['manufacturer'])
```

---

## 6. Security Architecture

### 6.1 Authentication & Authorization

#### **JWT Implementation**
```python
# JWT Security Configuration
from datetime import datetime, timedelta
import jwt
from passlib.context import CryptContext

class JWTHandler:
    SECRET_KEY = "your-secret-key-here"
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 1440  # 24 hours
    
    password_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    @classmethod
    def create_token(cls, user_id: str, expires_delta: timedelta = None) -> str:
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=cls.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        payload = {
            "sub": user_id,
            "exp": expire,
            "iat": datetime.utcnow()
        }
        
        return jwt.encode(payload, cls.SECRET_KEY, algorithm=cls.ALGORITHM)
    
    @classmethod
    def decode_token(cls, token: str) -> dict:
        try:
            payload = jwt.decode(token, cls.SECRET_KEY, algorithms=[cls.ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token expired")
        except jwt.JWTError:
            raise HTTPException(status_code=401, detail="Invalid token")
    
    @classmethod
    def hash_password(cls, password: str) -> str:
        return cls.password_context.hash(password)
    
    @classmethod
    def verify_password(cls, plain_password: str, hashed_password: str) -> bool:
        return cls.password_context.verify(plain_password, hashed_password)
```

#### **Role-Based Access Control**
```python
# RBAC Implementation
from enum import Enum
from functools import wraps

class UserRole(str, Enum):
    ADMIN = "admin"
    ENGINEER = "engineer"
    SENIOR_ENGINEER = "senior_engineer"
    VIEWER = "viewer"

class Permission(str, Enum):
    CREATE_COMPONENTS = "create_components"
    EDIT_COMPONENTS = "edit_components"
    DELETE_COMPONENTS = "delete_components"
    VIEW_COMPONENTS = "view_components"
    PERFORM_CALCULATIONS = "perform_calculations"
    MANAGE_USERS = "manage_users"
    GENERATE_REPORTS = "generate_reports"
    DIGITAL_SIGNATURE = "digital_signature"

ROLE_PERMISSIONS = {
    UserRole.ADMIN: [
        Permission.CREATE_COMPONENTS,
        Permission.EDIT_COMPONENTS,
        Permission.DELETE_COMPONENTS,
        Permission.VIEW_COMPONENTS,
        Permission.PERFORM_CALCULATIONS,
        Permission.MANAGE_USERS,
        Permission.GENERATE_REPORTS,
        Permission.DIGITAL_SIGNATURE
    ],
    UserRole.SENIOR_ENGINEER: [
        Permission.CREATE_COMPONENTS,
        Permission.EDIT_COMPONENTS,
        Permission.VIEW_COMPONENTS,
        Permission.PERFORM_CALCULATIONS,
        Permission.GENERATE_REPORTS,
        Permission.DIGITAL_SIGNATURE
    ],
    UserRole.ENGINEER: [
        Permission.VIEW_COMPONENTS,
        Permission.PERFORM_CALCULATIONS,
        Permission.GENERATE_REPORTS
    ],
    UserRole.VIEWER: [
        Permission.VIEW_COMPONENTS
    ]
}

def require_permission(permission: Permission):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = get_current_user()
            user_permissions = ROLE_PERMISSIONS.get(current_user.role, [])
            
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=403,
                    detail=f"Permission '{permission}' required"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

### 6.2 Data Protection

#### **Encryption Strategy**
```python
# Data Encryption for Sensitive Information
from cryptography.fernet import Fernet
import os
import base64

class DataEncryption:
    def __init__(self):
        self.key = os.environ.get('ENCRYPTION_KEY', '').encode()
        if not self.key:
            self.key = Fernet.generate_key()
        self.cipher = Fernet(self.key)
    
    def encrypt(self, data: str) -> str:
        return base64.b64encode(
            self.cipher.encrypt(data.encode())
        ).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        return self.cipher.decrypt(
            base64.b64decode(encrypted_data.encode())
        ).decode()

# Sensitive Field Encryption
class EncryptedField:
    def __init__(self):
        self.encryption = DataEncryption()
    
    def __set_name__(self, owner, name):
        self.name = name
    
    def __set__(self, obj, value):
        if value is not None:
            encrypted_value = self.encryption.encrypt(str(value))
            setattr(obj, f"_{self.name}", encrypted_value)
        else:
            setattr(obj, f"_{self.name}", None)
    
    def __get__(self, obj, objtype=None):
        if obj is None:
            return self
        
        encrypted_value = getattr(obj, f"_{self.name}", None)
        if encrypted_value is not None:
            return self.encryption.decrypt(encrypted_value)
        return None
```

### 6.3 Audit Trail Implementation

#### **Activity Logging**
```python
# Comprehensive Activity Logging
from contextlib import contextmanager
from sqlalchemy import event
from sqlalchemy.orm import Session

class ActivityLogger:
    def __init__(self, session: Session, user_id: str):
        self.session = session
        self.user_id = user_id
    
    def log_activity(
        self,
        action: str,
        resource_type: str,
        resource_id: str = None,
        details: dict = None,
        ip_address: str = None,
        user_agent: str = None
    ):
        activity_log = ActivityLog(
            user_id=self.user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details or {},
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.session.add(activity_log)
        self.session.commit()

# Automatic Activity Logging Decorator
def log_activity(action: str, resource_type: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get current user and request info
            current_user = get_current_user()
            request = get_current_request()
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Log activity
            logger = ActivityLogger(
                session=get_db_session(),
                user_id=current_user.id
            )
            
            logger.log_activity(
                action=action,
                resource_type=resource_type,
                resource_id=getattr(result, 'id', None),
                details={
                    'function': func.__name__,
                    'args': str(args),
                    'kwargs': str(kwargs)
                },
                ip_address=request.client.host,
                user_agent=request.headers.get('user-agent')
            )
            
            return result
        return wrapper
    return decorator
```

---

## 7. Performance Architecture

### 7.1 Caching Strategy

#### **Redis Caching Implementation**
```python
# Redis Caching for Performance
import redis
import json
from typing import Any, Optional
from functools import wraps

class CacheManager:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.default_ttl = 3600  # 1 hour
    
    def get(self, key: str) -> Optional[Any]:
        try:
            cached_data = self.redis_client.get(key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"Cache get error: {e}")
        return None
    
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        try:
            ttl = ttl or self.default_ttl
            serialized_data = json.dumps(value, default=str)
            return self.redis_client.setex(key, ttl, serialized_data)
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        try:
            return self.redis_client.delete(key)
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
        except Exception as e:
            logger.error(f"Cache clear pattern error: {e}")
        return 0

# Caching Decorator
def cache_result(key_prefix: str, ttl: int = 3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_manager = CacheManager()
            
            # Generate cache key
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result
            cache_manager.set(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator
```

### 7.2 Database Optimization

#### **Query Optimization**
```python
# Database Query Optimization
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload, joinedload

class OptimizedQueries:
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def get_components_with_relationships(
        self, 
        filters: dict = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Component]:
        """Optimized component query with eager loading"""
        query = select(Component).options(
            selectinload(Component.category),
            selectinload(Component.type),
            selectinload(Component.calculation_results)
        )
        
        if filters:
            for key, value in filters.items():
                if hasattr(Component, key):
                    query = query.where(getattr(Component, key) == value)
        
        query = query.limit(limit).offset(offset)
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def get_calculation_statistics(
        self, 
        user_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> dict:
        """Optimized calculation statistics query"""
        query = select(
            CalculationResult.calculation_type,
            func.count(CalculationResult.id).label('count'),
            func.avg(CalculationResult.calculation_time_ms).label('avg_time'),
            func.max(CalculationResult.calculation_time_ms).label('max_time')
        ).where(
            CalculationResult.calculated_by == user_id,
            CalculationResult.calculation_timestamp.between(start_date, end_date)
        ).group_by(CalculationResult.calculation_type)
        
        result = await self.session.execute(query)
        return {
            row.calculation_type: {
                'count': row.count,
                'avg_time': row.avg_time,
                'max_time': row.max_time
            }
            for row in result
        }
```

### 7.3 Performance Monitoring

#### **Performance Monitoring Decorator**
```python
# Performance Monitoring
import time
import psutil
from functools import wraps

class PerformanceMonitor:
    def __init__(self):
        self.metrics = []
    
    def measure_performance(self, func_name: str):
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                
                try:
                    result = await func(*args, **kwargs)
                    success = True
                    error = None
                except Exception as e:
                    success = False
                    error = str(e)
                    raise
                finally:
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                    
                    execution_time = (end_time - start_time) * 1000  # ms
                    memory_usage = end_memory - start_memory  # MB
                    
                    metric = {
                        'function': func_name,
                        'execution_time_ms': execution_time,
                        'memory_usage_mb': memory_usage,
                        'success': success,
                        'error': error,
                        'timestamp': datetime.utcnow()
                    }
                    
                    self.metrics.append(metric)
                    
                    # Log performance if slow
                    if execution_time > 1000:  # > 1 second
                        logger.warning(
                            f"Slow operation: {func_name} took {execution_time:.2f}ms"
                        )
                
                return result
            return wrapper
        return decorator
    
    def get_metrics(self) -> List[dict]:
        return self.metrics
    
    def clear_metrics(self):
        self.metrics = []

# Global performance monitor
performance_monitor = PerformanceMonitor()
monitor_performance = performance_monitor.measure_performance
```

---

## 8. Integration Architecture

### 8.1 API Design Patterns

#### **RESTful API Standards**
```python
# RESTful API Design Pattern
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional

router = APIRouter(prefix="/api/v1/components", tags=["components"])

@router.get("/", response_model=List[ComponentResponse])
async def get_components(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category_id: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user)
):
    """Get components with filtering and pagination"""
    filters = {}
    if category_id:
        filters['category_id'] = category_id
    if search:
        filters['search'] = search
    
    components = await component_service.get_components(
        filters=filters,
        skip=skip,
        limit=limit
    )
    
    return components

@router.post("/", response_model=ComponentResponse)
async def create_component(
    component_data: ComponentCreate,
    current_user: User = Depends(get_current_user)
):
    """Create a new component"""
    if not current_user.has_permission(Permission.CREATE_COMPONENTS):
        raise HTTPException(403, "Insufficient permissions")
    
    component = await component_service.create_component(component_data)
    return component

@router.get("/{component_id}", response_model=ComponentResponse)
async def get_component(
    component_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get a specific component"""
    component = await component_service.get_component(component_id)
    if not component:
        raise HTTPException(404, "Component not found")
    
    return component

@router.put("/{component_id}", response_model=ComponentResponse)
async def update_component(
    component_id: str,
    component_data: ComponentUpdate,
    current_user: User = Depends(get_current_user)
):
    """Update a component"""
    if not current_user.has_permission(Permission.EDIT_COMPONENTS):
        raise HTTPException(403, "Insufficient permissions")
    
    component = await component_service.update_component(component_id, component_data)
    if not component:
        raise HTTPException(404, "Component not found")
    
    return component

@router.delete("/{component_id}")
async def delete_component(
    component_id: str,
    current_user: User = Depends(get_current_user)
):
    """Delete a component"""
    if not current_user.has_permission(Permission.DELETE_COMPONENTS):
        raise HTTPException(403, "Insufficient permissions")
    
    success = await component_service.delete_component(component_id)
    if not success:
        raise HTTPException(404, "Component not found")
    
    return {"message": "Component deleted successfully"}
```

### 8.2 External System Integration

#### **Professional Standards Integration**
```python
# IEEE/IEC/EN Standards Integration
class StandardsIntegration:
    def __init__(self):
        self.ieee_standards = self._load_ieee_standards()
        self.iec_standards = self._load_iec_standards()
        self.en_standards = self._load_en_standards()
    
    def _load_ieee_standards(self) -> dict:
        return {
            "IEEE-141": {
                "title": "Recommended Practice for Electric Power Distribution",
                "version": "1993",
                "load_calculation_rules": self._load_ieee_141_rules(),
                "voltage_drop_limits": {"low_voltage": 0.05, "medium_voltage": 0.03},
                "diversity_factors": {"lighting": 0.75, "power": 0.6, "mixed": 0.7}
            },
            "IEEE-142": {
                "title": "Recommended Practice for Grounding of Industrial and Commercial Power Systems",
                "version": "2007",
                "grounding_rules": self._load_ieee_142_rules(),
                "resistance_limits": {"commercial": 25, "industrial": 10}
            }
        }
    
    def validate_calculation_against_standards(
        self, 
        calculation_type: str,
        parameters: dict,
        results: dict
    ) -> StandardsValidationResult:
        """Validate calculation results against professional standards"""
        validation_results = []
        
        # IEEE Standards Validation
        if calculation_type == "load_calculation":
            ieee_141_result = self._validate_ieee_141_load(parameters, results)
            validation_results.append(ieee_141_result)
        
        if calculation_type == "voltage_drop":
            ieee_voltage_result = self._validate_ieee_voltage_drop(parameters, results)
            validation_results.append(ieee_voltage_result)
        
        # IEC Standards Validation
        iec_result = self._validate_iec_standards(calculation_type, parameters, results)
        validation_results.append(iec_result)
        
        # EN Standards Validation
        en_result = self._validate_en_standards(calculation_type, parameters, results)
        validation_results.append(en_result)
        
        return StandardsValidationResult(
            overall_compliance=all(r.compliant for r in validation_results),
            standard_results=validation_results,
            recommendations=self._generate_recommendations(validation_results)
        )
    
    def _validate_ieee_141_load(self, parameters: dict, results: dict) -> StandardResult:
        """Validate load calculation against IEEE-141 standards"""
        ieee_141 = self.ieee_standards["IEEE-141"]
        
        # Check diversity factors
        diversity_factor = parameters.get("diversity_factor", 1.0)
        load_type = parameters.get("load_type", "mixed")
        recommended_diversity = ieee_141["diversity_factors"].get(load_type, 0.7)
        
        if diversity_factor > recommended_diversity * 1.1:  # 10% tolerance
            return StandardResult(
                standard="IEEE-141",
                compliant=False,
                message=f"Diversity factor {diversity_factor} exceeds recommended {recommended_diversity}",
                recommendation=f"Consider using diversity factor of {recommended_diversity}"
            )
        
        # Check voltage drop limits
        voltage_drop_percent = results.get("voltage_drop_percent", 0)
        voltage_level = parameters.get("voltage_level", "low_voltage")
        max_voltage_drop = ieee_141["voltage_drop_limits"].get(voltage_level, 0.05)
        
        if voltage_drop_percent > max_voltage_drop:
            return StandardResult(
                standard="IEEE-141",
                compliant=False,
                message=f"Voltage drop {voltage_drop_percent*100:.1f}% exceeds limit {max_voltage_drop*100:.1f}%",
                recommendation="Consider larger conductor size or shorter run length"
            )
        
        return StandardResult(
            standard="IEEE-141",
            compliant=True,
            message="Load calculation complies with IEEE-141 standards"
        )
```

---

## 9. Deployment Architecture

### 9.1 Containerization Strategy

#### **Docker Configuration**
```dockerfile
# Backend Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# Frontend Dockerfile
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Run application
CMD ["npm", "start"]
```

### 9.2 Production Deployment

#### **Docker Compose Configuration**
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: 
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/ultimate_electrical_designer
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - ./server/logs:/app/logs
    restart: unless-stopped

  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000/api/v1
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=ultimate_electrical_designer
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

---

## 10. Monitoring & Observability

### 10.1 Application Monitoring

#### **Prometheus Metrics**
```python
# Application Metrics
from prometheus_client import Counter, Histogram, Gauge, start_http_server

# Metrics definitions
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
CALCULATION_COUNT = Counter('calculations_total', 'Total calculations performed', ['type'])
CALCULATION_DURATION = Histogram('calculation_duration_seconds', 'Calculation duration')
ACTIVE_USERS = Gauge('active_users', 'Number of active users')
DATABASE_CONNECTIONS = Gauge('database_connections', 'Number of database connections')

# Metrics middleware
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    # Record metrics
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    REQUEST_DURATION.observe(time.time() - start_time)
    
    return response
```

### 10.2 Health Checks

#### **Application Health Monitoring**
```python
# Health Check System
from fastapi import APIRouter
from sqlalchemy import text

health_router = APIRouter()

@health_router.get("/health")
async def health_check():
    """Comprehensive health check"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # Database health
    try:
        await db.session.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {"status": "healthy"}
    except Exception as e:
        health_status["checks"]["database"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "unhealthy"
    
    # Redis health
    try:
        redis_client.ping()
        health_status["checks"]["redis"] = {"status": "healthy"}
    except Exception as e:
        health_status["checks"]["redis"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "unhealthy"
    
    # Memory usage
    memory_usage = psutil.virtual_memory().percent
    health_status["checks"]["memory"] = {
        "status": "healthy" if memory_usage < 80 else "warning",
        "usage_percent": memory_usage
    }
    
    return health_status
```

---

## 11. Conclusion

This comprehensive technical architecture document provides the complete blueprint for implementing the Ultimate Electrical Designer application. The architecture ensures professional-grade quality, scalability, and maintainability while meeting the demanding requirements of electrical engineering professionals.

The combination of 5-layer backend architecture, modern React frontend, comprehensive security framework, and professional standards compliance creates a robust foundation for delivering a world-class electrical engineering application that meets the highest professional standards.

---

**Document Control**
- **Version**: 1.0
- **Last Updated**: July 2025
- **Next Review**: Monthly
- **Owner**: Technical Architecture Team