# Deployment and Operations Guide
## Ultimate Electrical Designer Application

### Document Information
- **Version**: 1.0
- **Date**: July 2025
- **Scope**: Production deployment and operations
- **Environment**: AWS/Azure/GCP cloud infrastructure
- **Standards**: DevOps and SRE best practices

---

## 1. Executive Summary

This document provides comprehensive deployment and operations guidance for the Ultimate Electrical Designer application. The guide covers production deployment strategies, infrastructure management, monitoring, security hardening, and operational procedures for maintaining a professional-grade electrical engineering application.

### 1.1 Deployment Architecture
- **Container-Based Deployment**: Docker containers with Kubernetes orchestration
- **Cloud-Native**: AWS/Azure/GCP cloud infrastructure
- **High Availability**: Multi-zone deployment with 99.9% uptime SLA
- **Scalability**: Auto-scaling based on demand and load patterns
- **Security**: Multi-layered security with encryption and access controls

### 1.2 Operational Objectives
- **99.9% Uptime**: High availability with minimal downtime
- **Sub-200ms Response Time**: Optimal performance for calculations
- **Zero-Downtime Deployments**: Continuous deployment without service interruption
- **Automatic Recovery**: Self-healing infrastructure with monitoring
- **Compliance**: SOC 2, ISO 27001, and electrical engineering standards

### 1.3 Current Status
- **Development Environment**: Complete and operational
- **Staging Environment**: Configured and tested
- **Production Infrastructure**: Ready for deployment
- **Monitoring Systems**: Implemented and operational
- **Security Measures**: Hardened and validated

---

## 2. Infrastructure Architecture

### 2.1 Cloud Infrastructure Overview

#### 2.1.1 Multi-Cloud Architecture
```yaml
# infrastructure/terraform/main.tf
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC Configuration
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "ultimate-electrical-designer-vpc"
    Environment = var.environment
    Project     = "ultimate-electrical-designer"
  }
}

# Public Subnets
resource "aws_subnet" "public" {
  count = 3

  vpc_id                  = aws_vpc.main.id
  cidr_block              = "10.0.${count.index + 1}.0/24"
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true

  tags = {
    Name        = "public-subnet-${count.index + 1}"
    Environment = var.environment
    Type        = "public"
  }
}

# Private Subnets
resource "aws_subnet" "private" {
  count = 3

  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "private-subnet-${count.index + 1}"
    Environment = var.environment
    Type        = "private"
  }
}

# EKS Cluster
resource "aws_eks_cluster" "main" {
  name     = "ultimate-electrical-designer-cluster"
  role_arn = aws_iam_role.eks_cluster.arn
  version  = "1.27"

  vpc_config {
    subnet_ids              = concat(aws_subnet.public[*].id, aws_subnet.private[*].id)
    endpoint_private_access = true
    endpoint_public_access  = true
    public_access_cidrs     = var.allowed_cidr_blocks
  }

  encryption_config {
    provider {
      key_arn = aws_kms_key.eks.arn
    }
    resources = ["secrets"]
  }

  depends_on = [
    aws_iam_role_policy_attachment.eks_cluster_policy,
    aws_iam_role_policy_attachment.eks_service_policy,
  ]

  tags = {
    Name        = "ultimate-electrical-designer-cluster"
    Environment = var.environment
  }
}

# EKS Node Group
resource "aws_eks_node_group" "main" {
  cluster_name    = aws_eks_cluster.main.name
  node_group_name = "ultimate-electrical-designer-nodes"
  node_role_arn   = aws_iam_role.eks_node.arn
  subnet_ids      = aws_subnet.private[*].id

  instance_types = ["t3.large", "t3.xlarge"]
  
  scaling_config {
    desired_size = 3
    max_size     = 10
    min_size     = 2
  }

  update_config {
    max_unavailable = 1
  }

  depends_on = [
    aws_iam_role_policy_attachment.eks_worker_node_policy,
    aws_iam_role_policy_attachment.eks_cni_policy,
    aws_iam_role_policy_attachment.eks_container_registry_policy,
  ]

  tags = {
    Name        = "ultimate-electrical-designer-nodes"
    Environment = var.environment
  }
}

# RDS Database
resource "aws_db_instance" "main" {
  identifier = "ultimate-electrical-designer-db"
  
  engine         = "postgres"
  engine_version = "15.3"
  instance_class = "db.t3.large"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type         = "gp3"
  storage_encrypted    = true
  kms_key_id          = aws_kms_key.rds.arn
  
  db_name  = "ultimate_electrical_designer"
  username = var.database_username
  password = var.database_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = false
  final_snapshot_identifier = "ultimate-electrical-designer-final-snapshot"
  
  performance_insights_enabled = true
  monitoring_interval         = 60
  monitoring_role_arn        = aws_iam_role.rds_monitoring.arn
  
  tags = {
    Name        = "ultimate-electrical-designer-db"
    Environment = var.environment
  }
}

# Redis Cache
resource "aws_elasticache_subnet_group" "main" {
  name       = "ultimate-electrical-designer-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_replication_group" "main" {
  replication_group_id       = "ultimate-electrical-designer-cache"
  description                = "Redis cluster for Ultimate Electrical Designer"
  
  num_cache_clusters         = 3
  node_type                  = "cache.t3.medium"
  parameter_group_name       = "default.redis7"
  port                       = 6379
  
  subnet_group_name          = aws_elasticache_subnet_group.main.name
  security_group_ids         = [aws_security_group.redis.id]
  
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  auth_token                 = var.redis_auth_token
  
  automatic_failover_enabled = true
  multi_az_enabled          = true
  
  tags = {
    Name        = "ultimate-electrical-designer-cache"
    Environment = var.environment
  }
}
```

#### 2.1.2 Load Balancer Configuration
```yaml
# infrastructure/kubernetes/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ultimate-electrical-designer-ingress
  namespace: ultimate-electrical-designer
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
    - hosts:
        - api.ultimate-electrical-designer.com
        - app.ultimate-electrical-designer.com
      secretName: ultimate-electrical-designer-tls
  rules:
    - host: api.ultimate-electrical-designer.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: backend-service
                port:
                  number: 8000
    - host: app.ultimate-electrical-designer.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: frontend-service
                port:
                  number: 3000

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: ultimate-electrical-designer
spec:
  selector:
    app: backend
  ports:
    - port: 8000
      targetPort: 8000
      protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: ultimate-electrical-designer
spec:
  selector:
    app: frontend
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  type: ClusterIP
```

### 2.2 Container Configuration

#### 2.2.1 Backend Container
```dockerfile
# server/Dockerfile
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Copy poetry files
COPY pyproject.toml poetry.lock ./

# Configure poetry
RUN poetry config virtualenvs.create false

# Install dependencies
RUN poetry install --only=main --no-dev

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

# Copy installed packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Create app user
RUN useradd --create-home --shell /bin/bash app

# Set working directory
WORKDIR /app

# Copy application code
COPY --chown=app:app . .

# Switch to app user
USER app

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Command to run the application
CMD ["python", "main.py", "run", "--host", "0.0.0.0", "--port", "8000"]
```

#### 2.2.2 Frontend Container
```dockerfile
# client/Dockerfile
FROM node:20-alpine as builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine as production

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Set ownership
RUN chown -R nextjs:nodejs /usr/share/nginx/html
RUN chown -R nextjs:nodejs /var/cache/nginx
RUN chown -R nextjs:nodejs /var/log/nginx

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

### 2.3 Kubernetes Deployment

#### 2.3.1 Backend Deployment
```yaml
# infrastructure/kubernetes/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-deployment
  namespace: ultimate-electrical-designer
  labels:
    app: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
        - name: backend
          image: ultimate-electrical-designer/backend:latest
          ports:
            - containerPort: 8000
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: database-url
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: redis-url
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: jwt-secret
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
          securityContext:
            runAsNonRoot: true
            runAsUser: 1001
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
      securityContext:
        fsGroup: 1001

---
apiVersion: v1
kind: Secret
metadata:
  name: backend-secrets
  namespace: ultimate-electrical-designer
type: Opaque
data:
  database-url: <base64-encoded-database-url>
  redis-url: <base64-encoded-redis-url>
  jwt-secret: <base64-encoded-jwt-secret>

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: ultimate-electrical-designer
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  minReplicas: 3
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
```

#### 2.3.2 Frontend Deployment
```yaml
# infrastructure/kubernetes/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-deployment
  namespace: ultimate-electrical-designer
  labels:
    app: frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
        - name: frontend
          image: ultimate-electrical-designer/frontend:latest
          ports:
            - containerPort: 3000
          env:
            - name: NEXT_PUBLIC_API_URL
              value: "https://api.ultimate-electrical-designer.com"
            - name: NEXT_PUBLIC_APP_ENV
              value: "production"
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "200m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
          securityContext:
            runAsNonRoot: true
            runAsUser: 1001
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
      securityContext:
        fsGroup: 1001

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: frontend-hpa
  namespace: ultimate-electrical-designer
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend-deployment
  minReplicas: 3
  maxReplicas: 8
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
```

---

## 3. CI/CD Pipeline

### 3.1 GitHub Actions Workflow

#### 3.1.1 Build and Test Pipeline
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  backend-test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: latest
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Cache Poetry dependencies
        uses: actions/cache@v3
        with:
          path: server/.venv
          key: poetry-${{ hashFiles('server/poetry.lock') }}

      - name: Install dependencies
        working-directory: server
        run: poetry install

      - name: Run linting
        working-directory: server
        run: poetry run ruff check .

      - name: Run type checking
        working-directory: server
        run: poetry run mypy src/

      - name: Run tests
        working-directory: server
        run: poetry run pytest --cov=src --cov-report=xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: server/coverage.xml
          flags: backend

  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: client/package-lock.json

      - name: Install dependencies
        working-directory: client
        run: npm ci

      - name: Run linting
        working-directory: client
        run: npm run lint

      - name: Run type checking
        working-directory: client
        run: npm run type-check

      - name: Run tests
        working-directory: client
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: client/coverage/lcov.info
          flags: frontend

  build-and-push:
    needs: [backend-test, frontend-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: server
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:latest
          labels: ${{ steps.meta.outputs.labels }}

      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: client
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --region us-east-1 --name ultimate-electrical-designer-cluster

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f infrastructure/kubernetes/namespace.yaml
          kubectl apply -f infrastructure/kubernetes/backend-deployment.yaml
          kubectl apply -f infrastructure/kubernetes/frontend-deployment.yaml
          kubectl apply -f infrastructure/kubernetes/ingress.yaml
          kubectl rollout status deployment/backend-deployment -n ultimate-electrical-designer
          kubectl rollout status deployment/frontend-deployment -n ultimate-electrical-designer

      - name: Run smoke tests
        run: |
          kubectl wait --for=condition=ready pod -l app=backend -n ultimate-electrical-designer --timeout=300s
          kubectl wait --for=condition=ready pod -l app=frontend -n ultimate-electrical-designer --timeout=300s
          
          # Test backend health
          kubectl port-forward service/backend-service 8000:8000 -n ultimate-electrical-designer &
          sleep 10
          curl -f http://localhost:8000/health || exit 1
          
          # Test frontend health
          kubectl port-forward service/frontend-service 3000:3000 -n ultimate-electrical-designer &
          sleep 10
          curl -f http://localhost:3000/health || exit 1
```

### 3.2 Blue-Green Deployment

#### 3.2.1 Blue-Green Deployment Script
```bash
#!/bin/bash
# scripts/blue-green-deploy.sh

set -e

NAMESPACE="ultimate-electrical-designer"
SERVICE_NAME="backend-service"
DEPLOYMENT_NAME="backend-deployment"
NEW_VERSION=$1

if [ -z "$NEW_VERSION" ]; then
    echo "Usage: $0 <new-version>"
    exit 1
fi

echo "Starting blue-green deployment for version $NEW_VERSION"

# Create green deployment
echo "Creating green deployment..."
kubectl create -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${DEPLOYMENT_NAME}-green
  namespace: ${NAMESPACE}
  labels:
    app: backend
    version: green
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
      version: green
  template:
    metadata:
      labels:
        app: backend
        version: green
    spec:
      containers:
        - name: backend
          image: ultimate-electrical-designer/backend:${NEW_VERSION}
          ports:
            - containerPort: 8000
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: backend-secrets
                  key: database-url
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
EOF

# Wait for green deployment to be ready
echo "Waiting for green deployment to be ready..."
kubectl rollout status deployment/${DEPLOYMENT_NAME}-green -n ${NAMESPACE} --timeout=300s

# Test green deployment
echo "Testing green deployment..."
kubectl run test-pod --image=curlimages/curl --rm -i --restart=Never -n ${NAMESPACE} -- \
  curl -f http://${SERVICE_NAME}-green.${NAMESPACE}.svc.cluster.local:8000/health

# Switch traffic to green
echo "Switching traffic to green deployment..."
kubectl patch service ${SERVICE_NAME} -n ${NAMESPACE} -p '{"spec":{"selector":{"version":"green"}}}'

# Wait for confirmation
echo "Green deployment is now receiving traffic. Verify everything is working correctly."
read -p "Press Enter to continue with cleanup of blue deployment, or Ctrl+C to abort..."

# Clean up old blue deployment
echo "Cleaning up blue deployment..."
kubectl delete deployment ${DEPLOYMENT_NAME} -n ${NAMESPACE} --ignore-not-found=true

# Rename green deployment to blue
echo "Renaming green deployment to blue..."
kubectl patch deployment ${DEPLOYMENT_NAME}-green -n ${NAMESPACE} -p '{"metadata":{"name":"'${DEPLOYMENT_NAME}'"}}'
kubectl patch deployment ${DEPLOYMENT_NAME}-green -n ${NAMESPACE} -p '{"spec":{"selector":{"matchLabels":{"version":"blue"}},"template":{"metadata":{"labels":{"version":"blue"}}}}}'

# Update service selector
kubectl patch service ${SERVICE_NAME} -n ${NAMESPACE} -p '{"spec":{"selector":{"version":"blue"}}}'

echo "Blue-green deployment completed successfully!"
```

---

## 4. Security Hardening

### 4.1 Network Security

#### 4.1.1 Network Policies
```yaml
# infrastructure/kubernetes/network-policies.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-network-policy
  namespace: ultimate-electrical-designer
spec:
  podSelector:
    matchLabels:
      app: backend
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from:
        - podSelector:
            matchLabels:
              app: frontend
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 8000
  egress:
    - to:
        - podSelector:
            matchLabels:
              app: database
      ports:
        - protocol: TCP
          port: 5432
    - to:
        - podSelector:
            matchLabels:
              app: redis
      ports:
        - protocol: TCP
          port: 6379
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 80
        - protocol: UDP
          port: 53

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: frontend-network-policy
  namespace: ultimate-electrical-designer
spec:
  podSelector:
    matchLabels:
      app: frontend
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 3000
  egress:
    - to:
        - podSelector:
            matchLabels:
              app: backend
      ports:
        - protocol: TCP
          port: 8000
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 80
        - protocol: UDP
          port: 53
```

#### 4.1.2 Security Groups
```yaml
# infrastructure/terraform/security-groups.tf
resource "aws_security_group" "eks_cluster" {
  name        = "ultimate-electrical-designer-eks-cluster"
  description = "Security group for EKS cluster"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "ultimate-electrical-designer-eks-cluster"
  }
}

resource "aws_security_group" "rds" {
  name        = "ultimate-electrical-designer-rds"
  description = "Security group for RDS database"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.eks_cluster.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "ultimate-electrical-designer-rds"
  }
}

resource "aws_security_group" "redis" {
  name        = "ultimate-electrical-designer-redis"
  description = "Security group for Redis cache"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [aws_security_group.eks_cluster.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "ultimate-electrical-designer-redis"
  }
}
```

### 4.2 Application Security

#### 4.2.1 Pod Security Standards
```yaml
# infrastructure/kubernetes/pod-security-policy.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ultimate-electrical-designer
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: backend-service-account
  namespace: ultimate-electrical-designer
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: backend-role
  namespace: ultimate-electrical-designer
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: backend-role-binding
  namespace: ultimate-electrical-designer
subjects:
  - kind: ServiceAccount
    name: backend-service-account
    namespace: ultimate-electrical-designer
roleRef:
  kind: Role
  name: backend-role
  apiGroup: rbac.authorization.k8s.io
```

#### 4.2.2 Container Security
```yaml
# infrastructure/kubernetes/security-context.yaml
apiVersion: v1
kind: SecurityContext
metadata:
  name: restricted-security-context
spec:
  runAsNonRoot: true
  runAsUser: 1001
  runAsGroup: 1001
  fsGroup: 1001
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - ALL
  seccompProfile:
    type: RuntimeDefault
  seLinuxOptions:
    level: "s0:c123,c456"
```

### 4.3 Secrets Management

#### 4.3.1 AWS Secrets Manager Integration
```yaml
# infrastructure/kubernetes/secrets-store.yaml
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: ultimate-electrical-designer-secrets
  namespace: ultimate-electrical-designer
spec:
  provider: aws
  parameters:
    region: us-east-1
    objects: |
      - objectName: "ultimate-electrical-designer/database-credentials"
        objectType: "secretsmanager"
        jmesPath:
          - path: "username"
            objectAlias: "database-username"
          - path: "password"
            objectAlias: "database-password"
      - objectName: "ultimate-electrical-designer/jwt-secret"
        objectType: "secretsmanager"
        objectAlias: "jwt-secret"
      - objectName: "ultimate-electrical-designer/redis-auth"
        objectType: "secretsmanager"
        objectAlias: "redis-auth-token"
  secretObjects:
    - secretName: backend-secrets
      type: Opaque
      data:
        - objectName: "database-username"
          key: "database-username"
        - objectName: "database-password"
          key: "database-password"
        - objectName: "jwt-secret"
          key: "jwt-secret"
        - objectName: "redis-auth-token"
          key: "redis-auth-token"
```

---

## 5. Monitoring and Observability

### 5.1 Prometheus Monitoring

#### 5.1.1 Prometheus Configuration
```yaml
# infrastructure/monitoring/prometheus.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - /etc/prometheus/rules/*.yml
    
    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
      
      - job_name: 'backend'
        static_configs:
          - targets: ['backend-service.ultimate-electrical-designer.svc.cluster.local:8000']
        metrics_path: /metrics
        scrape_interval: 10s
      
      - job_name: 'frontend'
        static_configs:
          - targets: ['frontend-service.ultimate-electrical-designer.svc.cluster.local:3000']
        metrics_path: /metrics
        scrape_interval: 10s
      
      - job_name: 'node-exporter'
        static_configs:
          - targets: ['node-exporter.monitoring.svc.cluster.local:9100']
      
      - job_name: 'postgres-exporter'
        static_configs:
          - targets: ['postgres-exporter.monitoring.svc.cluster.local:9187']
      
      - job_name: 'redis-exporter'
        static_configs:
          - targets: ['redis-exporter.monitoring.svc.cluster.local:9121']

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
        - name: prometheus
          image: prom/prometheus:latest
          ports:
            - containerPort: 9090
          volumeMounts:
            - name: config
              mountPath: /etc/prometheus
            - name: storage
              mountPath: /prometheus
          args:
            - '--config.file=/etc/prometheus/prometheus.yml'
            - '--storage.tsdb.path=/prometheus'
            - '--web.console.libraries=/usr/share/prometheus/console_libraries'
            - '--web.console.templates=/usr/share/prometheus/consoles'
            - '--web.enable-lifecycle'
            - '--web.enable-admin-api'
      volumes:
        - name: config
          configMap:
            name: prometheus-config
        - name: storage
          persistentVolumeClaim:
            claimName: prometheus-storage
```

### 5.2 Grafana Dashboards

#### 5.2.1 Application Dashboard
```json
{
  "dashboard": {
    "id": null,
    "title": "Ultimate Electrical Designer - Application Metrics",
    "tags": ["ultimate-electrical-designer", "application"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"backend\"}[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ],
        "yAxes": [
          {
            "label": "requests/sec",
            "min": 0
          }
        ]
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"backend\"}[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"backend\"}[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "yAxes": [
          {
            "label": "seconds",
            "min": 0
          }
        ]
      },
      {
        "id": 3,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"backend\",status=~\"5..\"}[5m])",
            "legendFormat": "5xx errors"
          },
          {
            "expr": "rate(http_requests_total{job=\"backend\",status=~\"4..\"}[5m])",
            "legendFormat": "4xx errors"
          }
        ],
        "yAxes": [
          {
            "label": "errors/sec",
            "min": 0
          }
        ]
      },
      {
        "id": 4,
        "title": "Active Connections",
        "type": "singlestat",
        "targets": [
          {
            "expr": "sum(active_connections{job=\"backend\"})",
            "legendFormat": "Active Connections"
          }
        ]
      },
      {
        "id": 5,
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "process_resident_memory_bytes{job=\"backend\"} / 1024 / 1024",
            "legendFormat": "Backend Memory (MB)"
          },
          {
            "expr": "process_resident_memory_bytes{job=\"frontend\"} / 1024 / 1024",
            "legendFormat": "Frontend Memory (MB)"
          }
        ],
        "yAxes": [
          {
            "label": "MB",
            "min": 0
          }
        ]
      },
      {
        "id": 6,
        "title": "CPU Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(process_cpu_seconds_total{job=\"backend\"}[5m]) * 100",
            "legendFormat": "Backend CPU %"
          },
          {
            "expr": "rate(process_cpu_seconds_total{job=\"frontend\"}[5m]) * 100",
            "legendFormat": "Frontend CPU %"
          }
        ],
        "yAxes": [
          {
            "label": "%",
            "min": 0,
            "max": 100
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

### 5.3 Alerting Rules

#### 5.3.1 Prometheus Alerting
```yaml
# infrastructure/monitoring/alert-rules.yaml
groups:
  - name: ultimate-electrical-designer
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{job="backend",status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "High error rate detected"
          description: "Backend service is experiencing high error rate: {{ $value }} errors/sec"
      
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="backend"}[5m])) > 1.0
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High response time detected"
          description: "Backend service 95th percentile response time is {{ $value }}s"
      
      - alert: DatabaseConnectionFailure
        expr: up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Database connection failure"
          description: "Unable to connect to PostgreSQL database"
      
      - alert: RedisConnectionFailure
        expr: up{job="redis-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: cache
        annotations:
          summary: "Redis connection failure"
          description: "Unable to connect to Redis cache"
      
      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes / 1024 / 1024) > 1000
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "High memory usage detected"
          description: "{{ $labels.job }} is using {{ $value }}MB of memory"
      
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[5m]) > 0
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is crash looping"
```

---

## 6. Backup and Disaster Recovery

### 6.1 Database Backup Strategy

#### 6.1.1 Automated Backup Script
```bash
#!/bin/bash
# scripts/backup-database.sh

set -e

# Configuration
BACKUP_DIR="/backups/postgresql"
RETENTION_DAYS=30
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="ultimate_electrical_designer_backup_${TIMESTAMP}.sql.gz"

# Database connection details
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-ultimate_electrical_designer}
DB_USER=${DB_USER:-postgres}

# S3 backup configuration
S3_BUCKET=${S3_BUCKET:-ultimate-electrical-designer-backups}
S3_PREFIX="database-backups"

# Create backup directory
mkdir -p ${BACKUP_DIR}

echo "Starting database backup: ${BACKUP_FILE}"

# Create database dump
pg_dump -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} \
    --no-password \
    --verbose \
    --clean \
    --if-exists \
    --format=custom \
    --compress=9 \
    --file=${BACKUP_DIR}/${BACKUP_FILE}

# Verify backup
if [ -f "${BACKUP_DIR}/${BACKUP_FILE}" ]; then
    echo "Backup created successfully: ${BACKUP_FILE}"
    BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_FILE}" | cut -f1)
    echo "Backup size: ${BACKUP_SIZE}"
else
    echo "ERROR: Backup file not created"
    exit 1
fi

# Upload to S3
echo "Uploading backup to S3..."
aws s3 cp "${BACKUP_DIR}/${BACKUP_FILE}" \
    "s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_FILE}" \
    --storage-class STANDARD_IA

# Verify S3 upload
if aws s3 ls "s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_FILE}" > /dev/null 2>&1; then
    echo "Backup uploaded to S3 successfully"
else
    echo "ERROR: Failed to upload backup to S3"
    exit 1
fi

# Clean up local backup files older than retention period
find ${BACKUP_DIR} -name "*.sql.gz" -mtime +${RETENTION_DAYS} -delete

# Clean up S3 backups older than retention period
aws s3 ls s3://${S3_BUCKET}/${S3_PREFIX}/ | \
    awk '{print $4}' | \
    while read file; do
        if [[ $file =~ ^ultimate_electrical_designer_backup_([0-9]{8})_ ]]; then
            file_date=${BASH_REMATCH[1]}
            if [[ $(date -d "${file_date}" +%s) -lt $(date -d "${RETENTION_DAYS} days ago" +%s) ]]; then
                echo "Deleting old backup: ${file}"
                aws s3 rm "s3://${S3_BUCKET}/${S3_PREFIX}/${file}"
            fi
        fi
    done

echo "Database backup completed successfully"
```

#### 6.1.2 Backup Restoration Script
```bash
#!/bin/bash
# scripts/restore-database.sh

set -e

BACKUP_FILE=$1
RESTORE_DB_NAME=${2:-ultimate_electrical_designer_restored}

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file> [restore-database-name]"
    exit 1
fi

# Database connection details
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_USER=${DB_USER:-postgres}

echo "Starting database restoration from: ${BACKUP_FILE}"

# Download backup from S3 if it's an S3 URL
if [[ $BACKUP_FILE == s3://* ]]; then
    LOCAL_BACKUP_FILE="/tmp/$(basename $BACKUP_FILE)"
    echo "Downloading backup from S3..."
    aws s3 cp "$BACKUP_FILE" "$LOCAL_BACKUP_FILE"
    BACKUP_FILE=$LOCAL_BACKUP_FILE
fi

# Verify backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "ERROR: Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Create restore database
echo "Creating restore database: ${RESTORE_DB_NAME}"
createdb -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} ${RESTORE_DB_NAME}

# Restore database
echo "Restoring database from backup..."
pg_restore -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} \
    --dbname=${RESTORE_DB_NAME} \
    --verbose \
    --clean \
    --if-exists \
    --no-owner \
    --no-privileges \
    "$BACKUP_FILE"

# Verify restoration
echo "Verifying restoration..."
TABLE_COUNT=$(psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${RESTORE_DB_NAME} \
    -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")

if [ "$TABLE_COUNT" -gt 0 ]; then
    echo "Database restoration completed successfully"
    echo "Tables restored: $TABLE_COUNT"
else
    echo "ERROR: No tables found in restored database"
    exit 1
fi

# Clean up temporary files
if [[ $LOCAL_BACKUP_FILE != "" ]]; then
    rm -f "$LOCAL_BACKUP_FILE"
fi

echo "Database restoration completed: ${RESTORE_DB_NAME}"
```

### 6.2 Disaster Recovery Plan

#### 6.2.1 Disaster Recovery Procedures
```yaml
# infrastructure/disaster-recovery/dr-runbook.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: disaster-recovery-runbook
  namespace: ultimate-electrical-designer
data:
  runbook.md: |
    # Disaster Recovery Runbook
    
    ## Incident Response Procedures
    
    ### 1. Incident Detection
    - Monitor alerting systems (Prometheus, Grafana)
    - Check application health endpoints
    - Verify database connectivity
    - Validate external dependencies
    
    ### 2. Incident Classification
    - **P0 - Critical**: Complete service outage
    - **P1 - High**: Major functionality impaired
    - **P2 - Medium**: Minor functionality impaired
    - **P3 - Low**: Cosmetic issues
    
    ### 3. Incident Response Steps
    
    #### P0 - Critical Incident
    1. Activate incident response team
    2. Assess impact and scope
    3. Implement immediate workaround if available
    4. Communicate with stakeholders
    5. Restore service from backup if necessary
    6. Conduct post-incident review
    
    #### Database Failure Recovery
    1. Verify database status
    2. Check backup availability
    3. Restore from most recent backup
    4. Validate data integrity
    5. Resume application services
    
    #### Application Failure Recovery
    1. Check application logs
    2. Verify infrastructure status
    3. Restart services if necessary
    4. Deploy previous stable version
    5. Monitor recovery progress
    
    ### 4. Recovery Time Objectives (RTO)
    - Database restoration: 30 minutes
    - Application recovery: 15 minutes
    - Full service restoration: 1 hour
    
    ### 5. Recovery Point Objectives (RPO)
    - Database: 1 hour (based on backup frequency)
    - Application data: 15 minutes
    - User sessions: 5 minutes
    
    ### 6. Communication Plan
    - Internal team: Slack #incidents channel
    - External stakeholders: Email updates
    - Users: Status page updates
    - Regulatory bodies: As required
    
    ### 7. Post-Incident Actions
    - Document lessons learned
    - Update procedures
    - Improve monitoring
    - Conduct training
```

---

## 7. Performance Optimization

### 7.1 Application Performance Tuning

#### 7.1.1 Database Optimization
```sql
-- scripts/database-optimization.sql

-- Index optimization for common queries
CREATE INDEX CONCURRENTLY idx_users_email_active ON users(email) WHERE is_active = TRUE;
CREATE INDEX CONCURRENTLY idx_projects_engineer_status ON projects(engineer_id, status);
CREATE INDEX CONCURRENTLY idx_circuits_system_type ON circuits(system_id, circuit_type);
CREATE INDEX CONCURRENTLY idx_calculations_project_date ON calculations(project_id, calculation_date);
CREATE INDEX CONCURRENTLY idx_activity_logs_timestamp ON activity_logs(activity_timestamp);

-- Partial indexes for specific use cases
CREATE INDEX CONCURRENTLY idx_calculations_pending_review ON calculations(calculated_by) 
WHERE status = 'reviewed' AND pe_approved = FALSE;

-- Optimize frequent queries
-- Update table statistics
ANALYZE users;
ANALYZE projects;
ANALYZE electrical_systems;
ANALYZE circuits;
ANALYZE calculations;

-- Vacuum maintenance
VACUUM ANALYZE users;
VACUUM ANALYZE projects;
VACUUM ANALYZE electrical_systems;
VACUUM ANALYZE circuits;
VACUUM ANALYZE calculations;
```

#### 7.1.2 Application Cache Strategy
```python
# server/src/core/utils/cache_manager.py
import redis
import json
import hashlib
from typing import Any, Optional
from datetime import datetime, timedelta

class CacheManager:
    """
    Advanced cache management for Ultimate Electrical Designer
    
    Features:
    - Redis-based caching
    - Automatic cache invalidation
    - Performance monitoring
    - Cache warming strategies
    """
    
    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
        self.default_ttl = 3600  # 1 hour
        
    def get_cache_key(self, prefix: str, **kwargs) -> str:
        """Generate cache key from parameters"""
        key_data = json.dumps(kwargs, sort_keys=True)
        key_hash = hashlib.sha256(key_data.encode()).hexdigest()[:16]
        return f"{prefix}:{key_hash}"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            cached_value = self.redis_client.get(key)
            if cached_value:
                return json.loads(cached_value)
        except Exception as e:
            # Log error but don't fail
            print(f"Cache get error: {e}")
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        try:
            ttl = ttl or self.default_ttl
            serialized_value = json.dumps(value, default=str)
            return self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            print(f"Cache set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            print(f"Cache delete error: {e}")
            return False
    
    def warm_calculation_cache(self, calculation_types: list):
        """Warm cache with common calculations"""
        for calc_type in calculation_types:
            # Pre-calculate common scenarios
            common_scenarios = self.get_common_scenarios(calc_type)
            for scenario in common_scenarios:
                cache_key = self.get_cache_key(f"calculation:{calc_type}", **scenario)
                if not self.get(cache_key):
                    # Calculate and cache result
                    result = self.perform_calculation(calc_type, scenario)
                    self.set(cache_key, result, ttl=7200)  # 2 hours
    
    def invalidate_calculation_cache(self, calculation_id: str):
        """Invalidate cache for specific calculation"""
        pattern = f"calculation:*:{calculation_id}:*"
        keys = self.redis_client.keys(pattern)
        if keys:
            self.redis_client.delete(*keys)
```

### 7.2 Infrastructure Scaling

#### 7.2.1 Auto-scaling Configuration
```yaml
# infrastructure/kubernetes/hpa-advanced.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa-advanced
  namespace: ultimate-electrical-designer
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  minReplicas: 3
  maxReplicas: 20
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    - type: Pods
      pods:
        metric:
          name: http_requests_per_second
        target:
          type: AverageValue
          averageValue: "100"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 2
          periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60

---
apiVersion: autoscaling/v2
kind: VerticalPodAutoscaler
metadata:
  name: backend-vpa
  namespace: ultimate-electrical-designer
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-deployment
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
      - containerName: backend
        controlledResources: ["cpu", "memory"]
        minAllowed:
          cpu: "100m"
          memory: "128Mi"
        maxAllowed:
          cpu: "2"
          memory: "2Gi"
```

---

## 8. Conclusion

This comprehensive deployment and operations guide provides the complete framework for successfully deploying and maintaining the Ultimate Electrical Designer application in production environments.

The deployment strategy ensures:
- **High Availability**: 99.9% uptime with multi-zone deployment
- **Scalability**: Auto-scaling based on demand and load patterns
- **Security**: Multi-layered security with encryption and access controls
- **Monitoring**: Complete observability with Prometheus and Grafana
- **Disaster Recovery**: Comprehensive backup and recovery procedures
- **Performance**: Optimized for sub-200ms response times

All deployment procedures follow DevOps best practices and maintain professional engineering standards for electrical engineering applications.

---

**Document Control**
- **Document Owner**: DevOps Engineer
- **Review Authority**: Technical Lead, Security Officer
- **Approval Authority**: Technical Lead, Operations Manager
- **Review Frequency**: Monthly
- **Next Review Date**: [Month + 1]

**Version History**
- **v1.0** (July 2025): Initial deployment and operations guide
- **[Future versions will be tracked here]**