#!/usr/bin/env python3
"""Tests for Component Category functionality.

This module provides comprehensive tests for component category management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentCategory model validation and business logic
- ComponentCategoryRepository data access operations
- ComponentCategoryService business logic and validation
- Component Category API endpoints and error handling
- Hierarchical operations and tree management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_category_repository import ComponentCategoryRepository
from src.core.services.general.component_category_service import ComponentCategoryService
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategorySearchSchema,
)
from src.core.errors.exceptions import BusinessLogic<PERSON>rror, NotFoundError, ValidationError
from src.core.utils.pagination_utils import PaginationParams


class TestComponentCategoryModel:
    """Test ComponentCategory model functionality."""

    def test_component_category_creation(self, db_session: Session):
        """Test creating a ComponentCategory instance."""
        category = ComponentCategory(
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        
        db_session.add(category)
        db_session.commit()
        
        assert category.id is not None
        assert category.name == "Test Category"
        assert category.description == "Test description"
        assert category.is_active is True
        assert category.is_root_category is True
        assert category.level == 0

    def test_hierarchical_category_creation(self, db_session: Session):
        """Test creating hierarchical categories."""
        # Create parent category
        parent = ComponentCategory(
            name="Parent Category",
            description="Parent description",
            is_active=True,
        )
        db_session.add(parent)
        db_session.commit()
        
        # Create child category
        child = ComponentCategory(
            name="Child Category",
            description="Child description",
            parent_category_id=parent.id,
            is_active=True,
        )
        db_session.add(child)
        db_session.commit()
        
        assert child.parent_category_id == parent.id
        assert child.is_root_category is False
        assert child.level == 1
        assert child.full_path == "Parent Category > Child Category"

    def test_category_properties(self, db_session: Session):
        """Test category computed properties."""
        category = ComponentCategory(
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        
        assert category.component_count == 0
        assert category.has_children is False
        assert category.can_delete() == (True, None)

    def test_category_soft_delete(self, db_session: Session):
        """Test category soft delete functionality."""
        category = ComponentCategory(
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        
        # Test successful soft delete
        result = category.soft_delete(deleted_by_user_id=1)
        assert result is True
        assert category.is_deleted is True
        assert category.deleted_at is not None
        assert category.deleted_by_user_id == 1
        assert category.is_active is False
