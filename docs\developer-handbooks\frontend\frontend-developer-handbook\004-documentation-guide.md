# **Frontend Documentation Guide: JSDoc with TypeScript**

This guide explains what JSDoc is, its role in a TypeScript project, and why and how we should configure it for our Heat Tracing Design Application's frontend. JSDoc will serve as a powerful tool to complement our TypeScript types, providing rich, human-readable documentation directly within our code.

## **1. What is JSDoc?**

JSDoc is a markup language used to annotate JavaScript source code files. It allows developers to add structured comments that describe functions, parameters, return values, classes, and modules. These comments typically start with /\*\* and end with \*/.

While originally for JavaScript, JSDoc comments are universally understood by modern IDEs and documentation generation tools.

## **2. JSDoc's Role in a TypeScript Project**

In a TypeScript project, JSDoc plays a crucial, *complementary* role to explicit TypeScript types:

- **Enhancing Type Information:** TypeScript's language service understands JSDoc. When you hover over a function or variable in your IDE, it will display information derived from both explicit TypeScript type annotations *and* relevant JSDoc comments.

- **Providing Context Beyond Types:** TypeScript types define *what* the data looks like. JSDoc explains *why* a function exists, *how* it should be used, describes complex business logic, or provides usage examples that types alone cannot convey.

- **Automated Documentation Generation:** Tools like TypeDoc (specifically for TypeScript) can parse JSDoc comments and explicit TypeScript types to generate beautiful, browsable API documentation websites.

**JSDoc in TypeScript is about augmenting, not replacing, explicit type annotations.**

## **3. Why JSDoc is Crucial for Our Application**

For our Heat Tracing Design Application, JSDoc offers significant benefits that directly support our goals of robust, modular, and maintainable code:

- **Superior Developer Experience (DX):**

  - **Rich IDE Hover Information:** When a developer hovers over a component prop, a hook's return value, or a utility function, the IDE will show a detailed description, parameter explanations, and examples, making it much easier to understand and use code without digging into implementation details.

  - **Intelligent Auto-completion & Hints:** IDEs leverage JSDoc to provide more relevant auto-completion suggestions and parameter hints.

- **Automated API Documentation:** With tools like TypeDoc, we can generate a living API documentation website directly from our source code. This is invaluable for onboarding new team members, providing a centralized reference for frontend developers, and serving as a contract for reusable modules.

- **Clarity for Complex Logic:** Our application involves complex calculations, data transformations (e.g., backend API response to frontend-friendly types), and specific business rules. JSDoc can explain:

  - The purpose of a particular calculation utility (e.g., calculateHeatLoss).

  - Assumptions made by a function.

  - Side effects of a hook.

  - Specific error conditions or edge cases.

- **Promotes Consistency & Standardization:** Enforcing JSDoc for public interfaces encourages developers to think about how their code will be consumed and documented, leading to more consistent and higher-quality code.

- **Cross-Team Understanding:** While we primarily use TypeScript, JSDoc is a widely recognized standard across JavaScript ecosystems, making it easier for any JavaScript developer to understand our code's intent.

## **4. Considerations and Trade-offs**

- **Verbosity:** JSDoc comments add lines to your code, which can make files appear longer.

- **Maintenance Overhead:** Documentation requires maintenance. Outdated or incorrect JSDoc comments are worse than no comments, as they can misguide developers. A commitment to keeping docs updated is essential.

- **Redundancy (if misused):** Avoid duplicating information that TypeScript already explicitly provides. For instance, if you have name: string, don't write @param {string} name. Instead, explain *what* name is.

**Our strategy is to use JSDoc strategically to complement, not replace, TypeScript, focusing on context and usage examples.**

## **5. Recommendation & Strategic Approach for Our Application**

**Recommendation: YES, we should configure and strategically implement JSDoc.**

### **5.1. Where to Prioritize JSDoc:**

- **Public API of Reusable Modules/Components/Hooks (High Priority):**

  - **Components:** For any component in src/components/common/, src/components/ui/ (if customized significantly), or reusable components exported from src/modules/\*/components/. Document props, purpose, and usage.

  - **Hooks:** For all custom hooks in src/hooks/ and src/modules/\*/hooks/. Document parameters, return values, and side effects.

  - **Utilities:** For all functions exported from src/utils/ and src/modules/\*/utils/. Document purpose, parameters, return value, and potential exceptions.

  - **Services:** For public methods of services in src/services/ and src/modules/\*/api/ (for client-side API wrappers).

  - **Stores:** For state properties and actions in Zustand stores.

- **Complex Types/Interfaces (Medium Priority):**

  - For key interfaces and types defined in src/types/ (especially those derived from backend schemas or representing core domain entities like Project, Circuit, Component's technical_properties), add JSDoc to explain their *domain meaning*, not just their structure.

- **Complex Logic Sections (Targeted Priority):**

  - Within functions or blocks of code that perform non-obvious calculations, intricate data transformations, or handle specific business rules.

  - Document the *why* behind complex decisions or unusual logic.

### **5.2. How to Write Effective JSDoc in TypeScript:**

- **Focus on Context & Purpose:** Explain *what* the code does from a high-level perspective and *why* it does it.

- **Use @param for Descriptions (not types):** If paramName: Type is in the signature, use @param paramName Description of parameter.

- **Use @returns for Description (not types):** If (): ReturnType is in the signature, use @returns Description of what the function returns.

- **Provide Examples (@example):** This is incredibly valuable for quick understanding.  
  /\*\*  
  \* Calculates the total heat loss for a given pipe segment under specified conditions.  
  \* This function considers insulation thickness, ambient temperature, and pipe material.  
  \* @param {number} pipeLengthMeters - The length of the pipe segment in meters.  
  \* @param {number} pipeDiameterMm - The outer diameter of the pipe in millimeters.  
  \* @param {number} insulationThicknessMm - The thickness of the insulation in millimeters.  
  \* @param {number} ambientTemperatureC - The ambient temperature in degrees Celsius.  
  \* @returns {number} The calculated total heat loss in Watts.  
  \* @throws {Error} If any input parameters are invalid (e.g., negative length).  
  \* @example  
  \* // Calculate heat loss for a 50m pipe, 100mm diameter, 25mm insulation, at -10C  
  \* const totalLoss = calculateHeatLoss(50, 100, 25, -10);  
  \* console.log(\`Total heat loss: \${totalLoss}W\`);  
  \*/  
  function calculateHeatLoss(  
  pipeLengthMeters: number,  
  pipeDiameterMm: number,  
  insulationThicknessMm: number,  
  ambientTemperatureC: number  
  ): number {  
  // ... calculation logic ...  
  return 123.45; // Placeholder  
  }

- **Use Other Tags:**

  - @see: Refer to related functions, documents, or external resources.

  - @throws: Document potential errors a function might throw (e.g., DataImportError, ValidationError).

  - @deprecated: Mark functions that should no longer be used.

  - @todo: Note outstanding tasks.

  - @link: Create inline links.

  - @memberof: For class members.

  - @type: For complex variable types that might not be obvious.

## **6. How to Configure & Integrate JSDoc**

### **6.1. tsconfig.json Configuration:**

TypeScript inherently understands JSDoc. No special plugin is strictly required in tsconfig.json for basic JSDoc support. However, ensure compilerOptions.lib includes dom if you're documenting browser APIs.

### **6.2. ESLint Integration (eslint-plugin-jsdoc)**

To enforce JSDoc presence and style, integrate eslint-plugin-jsdoc.

- **Installation:**  
  npm install --save-dev eslint-plugin-jsdoc

- **.eslintrc.js Configuration:**  
  // .eslintrc.js (Add to your existing configuration)  
  module.exports = {  
  // ...  
  extends: \[  
  // ... existing extends  
  'plugin:jsdoc/recommended-typescript-error', // Recommends rules as errors for TS files  
  'plugin:jsdoc/recommended-typescript', // General JSDoc recommended rules (can be warn/off)  
  // Ensure Prettier config is still last!  
  \],  
  plugins: \[  
  // ... existing plugins  
  'jsdoc',  
  \],  
  rules: {  
  // ... existing rules  
    
  // --- JSDoc Specific Rules ---  
  'jsdoc/require-returns': 'off', // Turn off if return type is explicitly in TS signature  
  'jsdoc/require-param-type': 'off', // Turn off if param type is explicitly in TS signature  
  'jsdoc/check-param-names': 'warn', // Ensure param names in JSDoc match code  
  'jsdoc/require-jsdoc': \[ // Enforce JSDoc on certain elements  
  'warn', // Change to 'error' for stricter enforcement  
  {  
  require: {  
  FunctionDeclaration: true,  
  MethodDefinition: true,  
  ClassDeclaration: true,  
  ArrowFunctionExpression: true,  
  FunctionExpression: true,  
  },  
  // Ignore specific elements, e.g., React components where types are primarily props  
  // 'ContextType' for React.FC should be handled by prop-types, not JSDoc for params  
  contexts: \[  
  'FunctionDeclaration', 'MethodDefinition', 'ClassDeclaration',  
  'PropertyDefinition', // For class properties  
  // Example: Only require JSDoc for exported functions, not internal ones  
  // 'FunctionExpression:has(ExportNamedDeclaration)',  
  // 'ArrowFunctionExpression:has(VariableDeclarator:has(ExportNamedDeclaration))'  
  \],  
  // Ignore for specific contexts or patterns  
  exemptEmptyFunctions: true,  
  // Tags to ignore for missing descriptions, typically when TS provides enough  
  checkGetters: false, // Don't require JSDoc on getters  
  checkSetters: false, // Don't require JSDoc on setters  
  enableFixer: true, // Allow auto-fixing  
  },  
  \],  
  'jsdoc/no-types': 'error', // Crucial: Disallow JSDoc @type tags if TypeScript is used  
  // This forces you to use TS for types, JSDoc for description  
  'jsdoc/check-tag-names': 'warn', // Ensure correct JSDoc tag names  
  'jsdoc/check-line-alignment': \['warn', 'always'\], // Consistent alignment  
  'jsdoc/require-description': 'warn', // Encourage description for documented items  
  'jsdoc/tag-lines': \['warn', 'any', { start: true }\], // Tags should be on new lines  
  'jsdoc/newline-after-description': \['warn', 'always'\], // Newline after description  
  'jsdoc/empty-tags': 'warn', // Warn for empty tags like @returns {}  
  },  
  settings: {  
  jsdoc: {  
  mode: 'typescript', // Tell JSDoc plugin to work with TypeScript  
  // You can also specify specific tag preferences here  
  // tagNamePreference: {  
  // returns: { message: "Use @returns for return description" },  
  // },  
  },  
  },  
  // Override for test files to relax JSDoc requirements  
  overrides: \[  
  // ... existing overrides  
  {  
  files: \['\*\*/\*.spec.ts', '\*\*/\*.test.ts', '\*\*/\*.d.ts', '\*\*/\*.stories.tsx'\],  
  rules: {  
  'jsdoc/require-jsdoc': 'off', // No JSDoc required in test/declaration files  
  'jsdoc/no-types': 'off', // Might need to specify types in JSDoc for test mocks if not fully typed  
  },  
  },  
  \],  
  };

- **Why:** eslint-plugin-jsdoc helps enforce the presence and correctness of JSDoc comments. The jsdoc/no-types rule is particularly important as it prevents redundancy by forcing type definitions to be done via TypeScript syntax, leaving JSDoc for descriptive text.

### **6.3. Documentation Generation (TypeDoc - Optional but Recommended)**

- **What:** TypeDoc is a documentation generator that creates an API documentation website from your TypeScript source code and JSDoc comments.

- **Installation:**  
  npm install --save-dev typedoc

- **tsconfig.json (for TypeDoc, usually in its own tsconfig.json for docs):**  
  // tsconfig.docs.json (Example)  
  {  
  "extends": "./tsconfig.json", // Extend your main TS config  
  "compilerOptions": {  
  "outDir": "docs-build", // Output directory for generated docs  
  "emitDeclarationOnly": false, // TypeDoc needs actual files sometimes  
  "declaration": false,  
  "plugins": \[  
  {"transform": "typedoc-plugin-missing-exports"} // Optional: helpful plugin  
  \]  
  },  
  "include": \["src/\*\*/\*.ts", "src/\*\*/\*.tsx"\], // Files to include in docs  
  "exclude": \["src/\*\*/\*.test.ts", "src/\*\*/\*.spec.ts"\] // Files to exclude  
  }

- **package.json Script:**  
  // package.json (Add script)  
  {  
  "scripts": {  
  "docs": "typedoc --tsconfig tsconfig.docs.json --entryPointStrategy expand --out docs"  
  }  
  }

- **Why:** Provides an automated way to generate a comprehensive, browsable API documentation website, which is invaluable for a growing project and team.

By implementing this strategic approach to JSDoc, we will significantly improve the internal documentation of our frontend codebase, making it more readable, understandable, and maintainable for all developers.
