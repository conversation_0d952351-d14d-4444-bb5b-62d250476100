@import "tailwindcss";

/* Define custom design tokens using @theme */
@theme {
  --color-brand-primary: 220 90% 50%;
  --color-brand-secondary: 240 50% 50%;
  --color-text-muted: 0 0% 40%;
  --spacing-extra-large: 96px;
}

/* Base styles and CSS variables */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  font-family: 'Inter', sans-serif;
}

/* Custom component styles using @layer */
@layer components {
  .card-base-style {
    @apply bg-white p-6 rounded-xl shadow-lg border border-gray-200;
  }
  
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .hero-gradient {
    @apply bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800;
  }
}
