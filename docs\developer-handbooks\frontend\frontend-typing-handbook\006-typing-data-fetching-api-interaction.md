# **Typing Data Fetching & API Interaction**

This guide is critical for our Heat Tracing Design Application. It details how to leverage TypeScript for seamless, type-safe data fetching and interaction with our backend APIs, utilizing the generated API client and React Query.

## **1. Consuming Generated API Client Types (src/types/api.d.ts)**

Our backend's OpenAPI specification is used to automatically generate a type-safe TypeScript API client. This is the **most important** step for robust backend integration.

- **What it is:** A tool (e.g., openapi-typescript-codegen) processes the backend's openapi.json and produces TypeScript interfaces for all request bodies, response objects, and API method signatures. These typically reside in src/types/api.d.ts and an API client class in src/api/generated/.  
  // src/types/api.d.ts (Example snippet from generated file)  
  export type ProjectReadSchema = {  
  id: string;  
  name: string;  
  status: 'Active' \| 'Completed' \| 'Pending';  
  createdAt: string; // ISO 8601 datetime string  
  updatedAt: string;  
  defaultCableCircumstanceId?: string; // Our new FK  
  defaultCableSelectionMatrixId?: string; // Our new FK  
  \# ... other project fields  
  };  
    
  export type ProjectCreateSchema = {  
  name: string;  
  description?: string;  
  // ...  
  };  
    
  export type ErrorResponseSchema = {  
  detail: string;  
  code?: string;  
  field_errors?: { \[key: string\]: string\[\] };  
  };  
    
  // src/api/generated/projectsApi.ts (Example snippet from generated client)  
  import { HttpClient } from './httpClient'; // Your configured HttpClient  
  import { ProjectReadSchema, ProjectCreateSchema, ErrorResponseSchema } from '../types/api';  
    
  export class ProjectsApi {  
  constructor(private readonly httpClient: HttpClient) {}  
    
  public async getProjectById(args: { projectId: string }): Promise\<ProjectReadSchema\> {  
  return this.httpClient.request({  
  method: 'GET',  
  url: \`/api/v1/projects/\${args.projectId}\`,  
  });  
  }  
    
  public async createProject(args: { body: ProjectCreateSchema }): Promise\<ProjectReadSchema\> {  
  return this.httpClient.request({  
  method: 'POST',  
  url: \`/api/v1/projects\`,  
  body: args.body,  
  });  
  }  
  // ...  
  }

- **Why it helps:** This provides **compile-time validation** of your frontend's API calls. If the backend changes a field name, a type, or a required parameter, TypeScript will immediately flag it in your frontend code, preventing runtime errors and ensuring that frontend and backend contracts remain synchronized.

## **2. Mapping Backend Pydantic Schemas to Frontend Types**

Sometimes, the backend's exact schema doesn't perfectly fit the frontend's needs (e.g., Date objects vs. ISO strings).

- **What it is:** Create specific frontend types that might transform or augment the raw backend types.  
  // src/types/project.ts (Frontend specific type)  
  import { ProjectReadSchema } from './api'; // Import generated backend type  
    
  // Augmenting or transforming the generated type for frontend usage  
  interface FrontendProject extends Omit\<ProjectReadSchema, 'createdAt' \| 'updatedAt'\> {  
  createdAt: Date; // Convert string to Date object for frontend  
  updatedAt: Date;  
  // Add any frontend-only derived properties here  
  isNew: boolean;  
  }  
    
  // Function to transform the raw API response  
  export function transformProjectApiData(data: ProjectReadSchema): FrontendProject {  
  return {  
  ...data,  
  createdAt: new Date(data.createdAt),  
  updatedAt: new Date(data.updatedAt),  
  isNew: (new Date().getTime() - new Date(data.createdAt).getTime()) \< (7 \* 24 \* 60 \* 60 \* 1000) // less than 7 days old  
  };  
  }

- **Why it helps:** Provides a clean separation between raw API data and the data format used within frontend components, allowing for frontend-specific data enrichment or type conversions (e.g., from string to Date objects) while still leveraging the backend's contract.

## **3. Typing React Query Hooks (useQuery, useMutation)**

React Query's hooks are generic, allowing you to explicitly type the data, error, and variables.

- **TData, TError, TVariables Parameters:**

  - TData: The type of the successful data returned by the queryFn or mutationFn.

  - TError: The type of the error object returned by the queryFn or mutationFn. This should typically be our ErrorResponseSchema or a custom AppError type.

  - TVariables: The type of the variables passed to the mutationFn.

> // src/modules/projects/hooks/useProjectDetails.ts  
> import { useQuery, useMutation } from '@tanstack/react-query';  
> import { projectsApi } from '@/api/generated'; // Generated API client  
> import { ProjectReadSchema, ProjectCreateSchema, ErrorResponseSchema } from '@/types/api'; // Generated types  
> import { transformProjectApiData, FrontendProject } from '@/types/project'; // Our frontend-specific types  
>   
> // GET Request Example  
> export function useProjectDetails(projectId: string) {  
> return useQuery\<ProjectReadSchema, ErrorResponseSchema, FrontendProject\>({  
> queryKey: \['project', projectId\], // Unique key for caching  
> queryFn: async () =\> projectsApi.getProjectById({ projectId }),  
> enabled: !!projectId, // Only run query if projectId exists  
> // Selector to transform data for frontend  
> select: (data) =\> transformProjectApiData(data),  
> // Default error handling for React Query can be global or per hook  
> // onError: (error) =\> ErrorMonitoringService.captureException(error),  
> });  
> }  
>   
> // POST/PUT/PATCH/DELETE Mutation Example  
> interface CreateProjectVariables {  
> projectData: ProjectCreateSchema;  
> }  
>   
> export function useCreateProject() {  
> return useMutation\<ProjectReadSchema, ErrorResponseSchema, CreateProjectVariables\>(  
> async ({ projectData }) =\> projectsApi.createProject({ body: projectData }),  
> {  
> onSuccess: (data) =\> {  
> // Invalidate caches or update UI after successful creation  
> console.log('Project created:', data.id);  
> // queryClient.invalidateQueries(\['projects'\]); // Example invalidation  
> },  
> onError: (error) =\> {  
> console.error('Failed to create project:', error);  
> // Handle specific error codes or display toast  
> },  
> }  
> );  
> }

- **Typing Query Keys:** Query keys are the identifiers for cached data. They should be consistently typed for proper caching and invalidation.  
  // src/utils/queryKeys.ts (Centralized query keys for type safety and consistency)  
  export const QueryKeys = {  
  PROJECTS: (projectId?: string) =\> \['projects', projectId\] as const, // \`as const\` for tuple type inference  
  COMPONENTS: (type?: string, manufacturer?: string) =\> \['components', type, manufacturer\] as const,  
  INSTALLATION_CIRCUMSTANCES: () =\> \['installationCircumstances'\] as const,  
  // ... more keys  
  };  
    
  // Usage:  
  const { data: project } = useProjectDetails(projectId, QueryKeys.PROJECTS(projectId));  
  const { data: allComponents } = useQuery({  
  queryKey: QueryKeys.COMPONENTS(),  
  queryFn: () =\> componentsApi.getAllComponents(),  
  });

- **Why:** Type-safe React Query usage provides full type checking for your fetched data, mutation variables, and error handling, making your data layer extremely robust. Query keys are critical for managing the React Query cache effectively.

## **4. Handling Backend Error Responses with Types**

Our backend uses a consistent ErrorResponseSchema. Frontend should type its error handling accordingly.

- **What it is:** Ensure that the TError generic in React Query (or any raw try/catch block) correctly expects the ErrorResponseSchema type.  
  // src/components/project/ProjectForm.tsx (Example error display)  
  import { useCreateProject } from '@/modules/projects/hooks/useProjectDetails';  
  import { ErrorResponseSchema } from '@/types/api';  
  import { isAxiosError } from 'axios'; // If using Axios, otherwise check type directly  
    
  const ProjectForm: React.FC = () =\> {  
  const { mutate: createProject, isLoading, error } = useCreateProject();  
    
  const handleSubmit = async (values: { name: string; description: string }) =\> {  
  createProject({ projectData: values });  
  };  
    
  return (  
  \<form onSubmit={handleSubmit}\>  
  {/\* ... input fields ... \*/}  
  {error && (  
  \<div className="text-red-600 mt-2"\>  
  \<p\>Error: {error.detail}\</p\>  
  {error.field_errors && Object.entries(error.field_errors).map((\[field, messages\]) =\> (  
  \<p key={field}\>Field "{field}": {messages.join(', ')}\</p\>  
  ))}  
  \</div\>  
  )}  
  \<button type="submit" disabled={isLoading}\>  
  {isLoading ? 'Creating...' : 'Create Project'}  
  \</button\>  
  \</form\>  
  );  
  };  
    
  // Utility to check if error is our custom schema  
  function isErrorResponse(error: unknown): error is ErrorResponseSchema {  
  return (  
  typeof error === 'object' &&  
  error !== null &&  
  'detail' in error &&  
  typeof (error as ErrorResponseSchema).detail === 'string'  
  );  
  }

- **Why:** Consistent error typing allows for centralized error parsing and display logic, leading to a much better user experience when things go wrong. It prevents frontend from crashing on unexpected error formats.

## **5. Type Assertions for API Responses (When and When Not To)**

- **When to avoid as Type:** Generally, with a robust generated API client and React Query, you shouldn't need as Type directly on API responses. The types should be automatically inferred.

- **When it's acceptable (and sometimes necessary):**

  - When dealing with truly external, untyped JSON data (e.g., from a public, un-schemed API, though we discourage this).

  - When processing unknown data (e.g., from JSON.parse) after manual type narrowing checks (typeof, instanceof, in operator).

  - When integrating with very old or poorly typed libraries.

- **Why:** Overusing type assertions bypasses TypeScript's type safety and can hide real bugs. Rely on proper type generation and inference first.
