# Pre-commit hooks for Ultimate Electrical Designer
# Type Safety and Code Quality Enforcement

repos:
  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        args: [--line-length=88]
        files: ^server/

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]
        files: ^server/

  # Python linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        files: ^server/

  # Type Safety Validation - Critical Modules Only
  - repo: local
    hooks:
      - id: mypy-performance-optimizer
        name: MyPy Type Check - Performance Optimizer
        entry: bash -c 'cd server && poetry run mypy src/core/utils/performance_optimizer.py --show-error-codes --ignore-missing-imports'
        language: system
        files: ^server/src/core/utils/performance_optimizer\.py$
        pass_filenames: false

      - id: mypy-memory-manager
        name: MyPy Type Check - Memory Manager
        entry: bash -c 'cd server && poetry run mypy src/core/utils/memory_manager.py --show-error-codes --ignore-missing-imports'
        language: system
        files: ^server/src/core/utils/memory_manager\.py$
        pass_filenames: false

      - id: mypy-json-validation
        name: MyPy Type Check - JSON Validation
        entry: bash -c 'cd server && poetry run mypy src/core/utils/json_validation.py --show-error-codes --ignore-missing-imports'
        language: system
        files: ^server/src/core/utils/json_validation\.py$
        pass_filenames: false

      - id: mypy-file-io-utils
        name: MyPy Type Check - File IO Utils
        entry: bash -c 'cd server && poetry run mypy src/core/utils/file_io_utils.py --show-error-codes --ignore-missing-imports'
        language: system
        files: ^server/src/core/utils/file_io_utils\.py$
        pass_filenames: false

      - id: mypy-settings
        name: MyPy Type Check - Settings
        entry: bash -c 'cd server && poetry run mypy src/config/settings.py --show-error-codes --ignore-missing-imports'
        language: system
        files: ^server/src/config/settings\.py$
        pass_filenames: false

      - id: mypy-security-modules
        name: MyPy Type Check - Security Modules
        entry: bash -c 'cd server && poetry run mypy src/core/security/ --show-error-codes --ignore-missing-imports'
        language: system
        files: ^server/src/core/security/
        pass_filenames: false

  # Documentation and markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.38.0
    hooks:
      - id: markdownlint
        args: [--disable=MD013,MD033,MD041]  # Disable line length, HTML, first line rules

  # YAML validation
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        args: [-d, relaxed]

  # Security scanning
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, server/src, -f, json, -o, bandit-report.json]
        files: ^server/

# Configuration for pre-commit
default_stages: [commit]
fail_fast: false

# Custom type safety validation script
# Usage: Run manually for comprehensive type checking
# ./scripts/type_safety_check.sh
