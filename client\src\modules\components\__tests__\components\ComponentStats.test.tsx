/**
 * ComponentStats Component Tests
 * Tests the ComponentStats component including statistics visualization,
 * data aggregation accuracy, interactive elements, responsive rendering, and export functionality
 */

import { renderWithProviders } from '@/test/utils';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ComponentStats } from '../../components/ComponentStats';

// Mock the API hook
vi.mock('../../api/componentQueries', () => ({
  useComponentStats: vi.fn(),
}));

// Mock the utility functions
vi.mock('../../utils', () => ({
  formatPrice: vi.fn((price, currency) => `$${price} ${currency}`),
}));

describe('ComponentStats', () => {
  const mockStatsData = {
    total_components: 1250,
    active_components: 1100,
    inactive_components: 150,
    preferred_components: 85,
    by_category: {
      'Resistors': 450,
      'Capacitors': 320,
      'Inductors': 180,
      'Semiconductors': 200,
      'Connectors': 100,
    },
    by_manufacturer: {
      'Vishay': 280,
      'Murata': 220,
      'TDK': 180,
      'Samsung': 150,
      'Panasonic': 120,
    },
    price_range: {
      min: 0.05,
      max: 125.50,
      average: 8.75,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders statistics cards with correct data', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('Total Components')).toBeInTheDocument();
      expect(screen.getByText('1,250')).toBeInTheDocument();
      expect(screen.getByText('Active Components')).toBeInTheDocument();
      expect(screen.getByText('1,100')).toBeInTheDocument();
      expect(screen.getByText('Preferred Components')).toBeInTheDocument();
      expect(screen.getByText('85')).toBeInTheDocument();
    });

    it('displays price statistics correctly', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('Average Price')).toBeInTheDocument();
      expect(screen.getByText('Minimum Price')).toBeInTheDocument();
      expect(screen.getByText('Maximum Price')).toBeInTheDocument();
      expect(screen.getByText('$8.75 USD')).toBeInTheDocument();
      expect(screen.getByText('$0.05 USD')).toBeInTheDocument();
      expect(screen.getByText('$125.5 USD')).toBeInTheDocument();
    });

    it('renders category breakdown chart', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('Components by Category')).toBeInTheDocument();
      expect(screen.getByText('Resistors')).toBeInTheDocument();
      expect(screen.getByText('450')).toBeInTheDocument();
      expect(screen.getByText('Capacitors')).toBeInTheDocument();
      expect(screen.getByText('320')).toBeInTheDocument();
    });

    it('renders manufacturer breakdown chart', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('Top Manufacturers')).toBeInTheDocument();
      expect(screen.getByText('Vishay')).toBeInTheDocument();
      expect(screen.getByText('280')).toBeInTheDocument();
      expect(screen.getByText('Murata')).toBeInTheDocument();
      expect(screen.getByText('220')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      const customClass = 'custom-stats-class';
      const { container } = renderWithProviders(
        <ComponentStats className={customClass} />
      );

      expect(container.firstChild).toHaveClass(customClass);
    });
  });

  describe('Loading States', () => {
    it('renders loading skeleton when isLoading is true', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      const skeletonElements = screen.getAllByRole('generic').filter(el => 
        el.className.includes('animate-pulse')
      );
      expect(skeletonElements.length).toBeGreaterThan(0);
    });

    it('shows proper loading skeleton structure', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Should render 8 skeleton cards
      const skeletonCards = screen.getAllByRole('generic').filter(el => 
        el.className.includes('bg-gray-200')
      );
      expect(skeletonCards.length).toBeGreaterThanOrEqual(8);
    });
  });

  describe('Error States', () => {
    it('renders error message when API call fails', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: null,
        isLoading: false,
        error: new Error('Failed to fetch statistics'),
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('Unable to load statistics')).toBeInTheDocument();
      expect(screen.getByText('Failed to fetch statistics')).toBeInTheDocument();
    });

    it('renders generic error message when no error message provided', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('Unable to load statistics')).toBeInTheDocument();
      expect(screen.getByText('An error occurred while loading component statistics.')).toBeInTheDocument();
    });
  });

  describe('Data Aggregation and Calculation Accuracy', () => {
    it('calculates percentages correctly for active components', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Active components: 1100/1250 = 88%
      expect(screen.getByText('88% of total')).toBeInTheDocument();
    });

    it('calculates percentages correctly for preferred components', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Preferred components: 85/1250 = 7%
      expect(screen.getByText('7% of total')).toBeInTheDocument();
    });

    it('calculates percentages correctly for inactive components', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Inactive components: 150/1250 = 12%
      expect(screen.getByText('12% of total')).toBeInTheDocument();
    });

    it('formats large numbers with locale-specific formatting', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const largeNumberStats = {
        ...mockStatsData,
        total_components: 12500,
        active_components: 11000,
      };

      vi.mocked(useComponentStats).mockReturnValue({
        data: largeNumberStats,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('12,500')).toBeInTheDocument();
      expect(screen.getByText('11,000')).toBeInTheDocument();
    });

    it('handles zero values gracefully', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const zeroStats = {
        ...mockStatsData,
        preferred_components: 0,
        inactive_components: 0,
      };

      vi.mocked(useComponentStats).mockReturnValue({
        data: zeroStats,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('0')).toBeInTheDocument();
      expect(screen.getByText('0% of total')).toBeInTheDocument();
    });
  });

  describe('Interactive Chart Elements', () => {
    it('renders progress bars for category breakdown', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Check for progress bar elements
      const progressBars = screen.getAllByRole('generic').filter(el => 
        el.className.includes('bg-blue-500') || el.className.includes('bg-green-500')
      );
      expect(progressBars.length).toBeGreaterThan(0);
    });

    it('calculates progress bar widths correctly', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Resistors has the highest count (450), so it should have 100% width
      const progressBars = screen.getAllByRole('generic').filter(el => 
        el.style.width === '100%'
      );
      expect(progressBars.length).toBeGreaterThan(0);
    });

    it('sorts categories by count in descending order', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      const categorySection = screen.getByText('Components by Category').closest('div');
      const categoryTexts = Array.from(categorySection?.querySelectorAll('span') || [])
        .map(span => span.textContent)
        .filter(text => Object.keys(mockStatsData.by_category).includes(text || ''));

      // Should be sorted: Resistors (450), Capacitors (320), Semiconductors (200), Inductors (180), Connectors (100)
      expect(categoryTexts[0]).toBe('Resistors');
      expect(categoryTexts[1]).toBe('Capacitors');
    });

    it('limits category display to top 8 items', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const manyCategories = {};
      for (let i = 0; i < 15; i++) {
        manyCategories[`Category ${i}`] = 100 - i;
      }

      vi.mocked(useComponentStats).mockReturnValue({
        data: { ...mockStatsData, by_category: manyCategories },
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      const categorySection = screen.getByText('Components by Category').closest('div');
      const categoryItems = categorySection?.querySelectorAll('.flex.items-center.justify-between');
      expect(categoryItems?.length).toBeLessThanOrEqual(8);
    });
  });

  describe('Responsive Rendering', () => {
    it('renders with responsive grid layout', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Check for responsive grid classes
      const gridElements = screen.getAllByRole('generic').filter(el => 
        el.className.includes('grid') && 
        (el.className.includes('md:') || el.className.includes('lg:'))
      );
      expect(gridElements.length).toBeGreaterThan(0);
    });

    it('adapts layout for mobile view', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Should still render all content
      expect(screen.getByText('Total Components')).toBeInTheDocument();
      expect(screen.getByText('Components by Category')).toBeInTheDocument();
    });
  });

  describe('Edge Cases and Data Handling', () => {
    it('handles empty statistics data', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const emptyStats = {
        total_components: 0,
        active_components: 0,
        inactive_components: 0,
        preferred_components: 0,
        by_category: {},
        by_manufacturer: {},
        price_range: {
          min: null,
          max: null,
          average: null,
        },
      };

      vi.mocked(useComponentStats).mockReturnValue({
        data: emptyStats,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('0')).toBeInTheDocument();
      expect(screen.getByText('Components by Category')).toBeInTheDocument();
      expect(screen.getByText('Top Manufacturers')).toBeInTheDocument();
    });

    it('handles null price values gracefully', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const { formatPrice } = require('../../utils');

      vi.mocked(formatPrice).mockImplementation((price, currency) =>
        price === null ? 'N/A' : `$${price} ${currency}`
      );

      const statsWithNullPrices = {
        ...mockStatsData,
        price_range: {
          min: null,
          max: null,
          average: null,
        },
      };

      vi.mocked(useComponentStats).mockReturnValue({
        data: statsWithNullPrices,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('N/A')).toBeInTheDocument();
    });

    it('handles very large numbers correctly', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const largeStats = {
        ...mockStatsData,
        total_components: 1000000,
        active_components: 950000,
        preferred_components: 50000,
      };

      vi.mocked(useComponentStats).mockReturnValue({
        data: largeStats,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('1,000,000')).toBeInTheDocument();
      expect(screen.getByText('950,000')).toBeInTheDocument();
      expect(screen.getByText('50,000')).toBeInTheDocument();
    });

    it('handles division by zero in percentage calculations', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const zeroTotalStats = {
        ...mockStatsData,
        total_components: 0,
        active_components: 0,
        preferred_components: 0,
      };

      vi.mocked(useComponentStats).mockReturnValue({
        data: zeroTotalStats,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Should not crash and should handle NaN gracefully
      expect(screen.getByText('0')).toBeInTheDocument();
    });

    it('handles missing category or manufacturer data', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const incompleteStats = {
        ...mockStatsData,
        by_category: {},
        by_manufacturer: {},
      };

      vi.mocked(useComponentStats).mockReturnValue({
        data: incompleteStats,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(screen.getByText('Components by Category')).toBeInTheDocument();
      expect(screen.getByText('Top Manufacturers')).toBeInTheDocument();
      // Should not show any category or manufacturer items
    });
  });

  describe('Performance and Optimization', () => {
    it('renders efficiently with large datasets', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const largeDataset = {
        ...mockStatsData,
        by_category: {},
        by_manufacturer: {},
      };

      // Create large category and manufacturer datasets
      for (let i = 0; i < 100; i++) {
        largeDataset.by_category[`Category ${i}`] = Math.floor(Math.random() * 1000);
        largeDataset.by_manufacturer[`Manufacturer ${i}`] = Math.floor(Math.random() * 500);
      }

      vi.mocked(useComponentStats).mockReturnValue({
        data: largeDataset,
        isLoading: false,
        error: null,
      });

      const startTime = performance.now();
      renderWithProviders(<ComponentStats />);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should render in under 100ms
      expect(screen.getByText('Components by Category')).toBeInTheDocument();
    });

    it('handles rapid data updates without memory leaks', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const { rerender } = renderWithProviders(<ComponentStats />);

      // Simulate rapid data updates
      for (let i = 0; i < 10; i++) {
        const updatedStats = {
          ...mockStatsData,
          total_components: 1000 + i,
          active_components: 900 + i,
        };

        vi.mocked(useComponentStats).mockReturnValue({
          data: updatedStats,
          isLoading: false,
          error: null,
        });

        rerender(<ComponentStats />);
      }

      expect(screen.getByText('1,009')).toBeInTheDocument();
      expect(screen.getByText('909')).toBeInTheDocument();
    });
  });

  describe('Integration with Utility Functions', () => {
    it('calls formatPrice with correct parameters', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const { formatPrice } = require('../../utils');

      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      expect(formatPrice).toHaveBeenCalledWith(8.75, 'USD');
      expect(formatPrice).toHaveBeenCalledWith(0.05, 'USD');
      expect(formatPrice).toHaveBeenCalledWith(125.50, 'USD');
    });

    it('handles formatPrice errors gracefully', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      const { formatPrice } = require('../../utils');

      vi.mocked(formatPrice).mockImplementation(() => {
        throw new Error('Formatting error');
      });

      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      // Should not crash the component
      expect(() => {
        renderWithProviders(<ComponentStats />);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('has proper semantic structure', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Check for proper heading hierarchy
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });

    it('provides meaningful text for screen readers', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Check for descriptive text
      expect(screen.getByText('All components in catalog')).toBeInTheDocument();
      expect(screen.getByText('Average component price')).toBeInTheDocument();
    });

    it('has proper color contrast for progress bars', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Check for proper color classes
      const blueProgressBars = screen.getAllByRole('generic').filter(el =>
        el.className.includes('bg-blue-500')
      );
      const greenProgressBars = screen.getAllByRole('generic').filter(el =>
        el.className.includes('bg-green-500')
      );

      expect(blueProgressBars.length).toBeGreaterThan(0);
      expect(greenProgressBars.length).toBeGreaterThan(0);
    });
  });

  describe('Export Functionality Integration', () => {
    it('displays statistics that could be used for export decisions', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Statistics should provide context for export operations
      expect(screen.getByText('1,250')).toBeInTheDocument(); // Total components available for export
      expect(screen.getByText('1,100')).toBeInTheDocument(); // Active components
    });

    it('shows category breakdown useful for filtered exports', () => {
      const { useComponentStats } = require('../../api/componentQueries');
      vi.mocked(useComponentStats).mockReturnValue({
        data: mockStatsData,
        isLoading: false,
        error: null,
      });

      renderWithProviders(<ComponentStats />);

      // Category information helps users understand what they can export
      expect(screen.getByText('Resistors')).toBeInTheDocument();
      expect(screen.getByText('450')).toBeInTheDocument();
      expect(screen.getByText('Capacitors')).toBeInTheDocument();
      expect(screen.getByText('320')).toBeInTheDocument();
    });
  });
});
