'use client'

import { UserProfile } from '@/components/auth/UserProfile'
import { RouteGuard } from '@/components/auth/RouteGuard'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

export default function ProfilePage() {
  return (
    <RouteGuard requireAuth={true}>
      <DashboardLayout title="User Profile">
        <UserProfile />
      </DashboardLayout>
    </RouteGuard>
  )
}
