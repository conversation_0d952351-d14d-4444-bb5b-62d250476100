# **Typing Next.js Specifics (App Router, Routing)**

This guide focuses on leveraging TypeScript for type-safe development within the Next.js App Router, particularly for pages, layouts, and route parameters.

## **1. Typing App Router layout.tsx and page.tsx Props**

The Next.js App Router provides specific types for props passed to layout.tsx and page.tsx components.

- **layout.tsx Props:** Layouts receive children and potentially params (if they are within a dynamic route segment).  
  // src/app/layout.tsx  
  import React from 'react';  
    
  interface RootLayoutProps {  
  children: React.ReactNode; // All layout components take a children prop  
  // If this layout is within a dynamic route (e.g., app/\[projectId\]/layout.tsx),  
  // it would also receive params:  
  // params: { projectId: string };  
  }  
    
  const RootLayout: React.FC\<RootLayoutProps\> = ({ children }) =\> {  
  return (  
  \<html lang="en"\>  
  \<body\>  
  {/\* Your global layout (e.g., header, sidebar, footer) \*/}  
  \<main\>{children}\</main\> {/\* This is where the page content renders \*/}  
  \</body\>  
  \</html\>  
  );  
  };  
    
  export default RootLayout;

- **page.tsx Props:** Pages receive params (for dynamic routes) and searchParams (for query parameters).  
  // src/app/projects/\[projectId\]/page.tsx  
  import React from 'react';  
  import { notFound } from 'next/navigation'; // For handling 404  
    
  // Define the type for dynamic route parameters  
  interface ProjectPageParams {  
  projectId: string; // Corresponds to the folder name \[projectId\]  
  }  
    
  // Define the type for search (query) parameters  
  interface ProjectPageSearchParams {  
  view?: 'details' \| 'calculations' \| 'reports'; // Example: ?view=calculations  
  status?: string; // Example: ?status=active  
  }  
    
  // Type the props for the page component  
  interface ProjectPageProps {  
  params: ProjectPageParams;  
  searchParams: ProjectPageSearchParams;  
  }  
    
  const ProjectPage: React.FC\<ProjectPageProps\> = ({ params, searchParams }) =\> {  
  const { projectId } = params;  
  const { view, status } = searchParams;  
    
  // Example: Fetch project data based on projectId  
  // const { data: project, isLoading, error } = useProjectDetails(projectId);  
    
  if (!projectId) {  
  notFound(); // Use Next.js notFound() for 404  
  }  
    
  return (  
  \<div className="p-4"\>  
  \<h1 className="text-2xl font-bold"\>Project: {projectId}\</h1\>  
  \<p\>Current View: {view \|\| 'Default Details'}\</p\>  
  \<p\>Filter Status: {status \|\| 'None'}\</p\>  
  {/\* Render different components based on 'view' param \*/}  
  {/\* ... \*/}  
  \</div\>  
  );  
  };  
    
  export default ProjectPage;

- **Why it helps:** Ensures that your page and layout components correctly receive and utilize the data provided by Next.js's routing system, preventing errors when accessing dynamic parameters or query strings.

## **2. Typing Route Parameters (params, searchParams)**

- **params**: Used for dynamic route segments (e.g., \[id\], \[slug\]). The type is typically Record\<string, string \| string\[\]\>, but you should define a more specific interface.  
  // Example: For app/users/\[userId\]/profile/page.tsx  
  interface UserProfilePageParams {  
  userId: string;  
  }  
    
  type UserProfilePageProps = {  
  params: UserProfilePageParams;  
  };  
    
  const UserProfilePage: React.FC\<UserProfilePageProps\> = ({ params }) =\> {  
  const { userId } = params;  
  // ... fetch user profile for userId  
  return \<div\>User Profile for: {userId}\</div\>;  
  };

- **searchParams**: For URL query parameters (e.g., ?query=value&filter=type). These are always strings or arrays of strings.  
  // Example: For app/reports/page.tsx?type=pdf&status=generated  
  interface ReportListPageSearchParams {  
  type?: string;  
  status?: 'generated' \| 'pending' \| 'failed';  
  page?: string; // Query params are strings, convert to number if needed  
  }  
    
  type ReportListPageProps = {  
  searchParams: ReportListPageSearchParams;  
  };  
    
  const ReportListPage: React.FC\<ReportListPageProps\> = ({ searchParams }) =\> {  
  const { type, status, page } = searchParams;  
  const currentPage = page ? parseInt(page) : 1;  
  // ... filter reports by type and status, paginate by page  
  return (  
  \<div\>  
  \<h1\>Reports\</h1\>  
  \<p\>Filtered by Type: {type \|\| 'All'}\</p\>  
  \<p\>Status: {status \|\| 'All'}\</p\>  
  \<p\>Page: {currentPage}\</p\>  
  \</div\>  
  );  
  };

- **Why it helps:** Provides strong typing for URL-driven data, making it safer to access and use route parameters and query strings, and improving code readability.

## **3. Typing Next.js API Routes (app/api/)**

While our backend handles most API logic, Next.js also allows you to define API routes (src/app/api/) for specific frontend-only needs (e.g., proxying external APIs, handling sensitive logic that shouldn't be client-side).

- **NextRequest and NextResponse:** Next.js provides specific types for request and response objects in API routes.  
  // src/app/api/my-proxy/route.ts  
  import { NextRequest, NextResponse } from 'next/server';  
    
  // Example: Proxy an external API call  
  export async function GET(request: NextRequest) {  
  const url = new URL(request.url);  
  const externalApiEndpoint = url.searchParams.get('endpoint');  
    
  if (!externalApiEndpoint) {  
  return NextResponse.json({ error: 'Missing endpoint parameter' }, { status: 400 });  
  }  
    
  try {  
  const externalResponse = await fetch(externalApiEndpoint);  
  if (!externalResponse.ok) {  
  throw new Error(\`External API error: \${externalResponse.statusText}\`);  
  }  
  const data = await externalResponse.json();  
  return NextResponse.json(data);  
  } catch (error: any) {  
  console.error('Proxy error:', error);  
  return NextResponse.json({ error: 'Failed to proxy request', details: error.message }, { status: 500 });  
  }  
  }  
    
  // Example: Handling a POST request with a specific body type  
  interface MyApiRequestBody {  
  someId: string;  
  action: 'start' \| 'stop';  
  }  
    
  export async function POST(request: NextRequest) {  
  try {  
  const body: MyApiRequestBody = await request.json(); // Type assertion or validation  
  // Or use Zod for robust validation:  
  // const validatedBody = myRequestBodySchema.parse(await request.json());  
    
  if (body.action === 'start') {  
  console.log(\`Starting process for \${body.someId}\`);  
  }  
  return NextResponse.json({ status: 'success', id: body.someId });  
  } catch (error) {  
  console.error('API route error:', error);  
  return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });  
  }  
  }

- **Why it helps:** Ensures that your Next.js API routes correctly handle incoming requests (headers, body, query params) and return appropriate responses, maintaining type safety for data flowing through these serverless functions.
