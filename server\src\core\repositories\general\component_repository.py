# src/core/repositories/general/component_repository.py
"""Component Repository.

This module provides data access layer for Component entities, extending the base
repository with component-specific query methods and operations for electrical
component management.
"""

from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, asc, desc, func, or_, select, update
from sqlalchemy.orm import Session

from src.config.logging_config import logger
from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.component import Component
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository
from src.core.schemas.general.component_schemas import (
    AdvancedFilterSchema,
    ComponentAdvancedSearchSchema,
    ComponentSearchResultSchema,
    RangeFilterSchema,
    SpecificationFilterSchema,
)
from src.core.utils.advanced_cache_manager import cache_manager, cached
from src.core.utils.pagination_utils import PaginationParams, PaginationResult
from src.core.utils.query_optimizer import monitor_query_performance, optimized_session
from src.core.utils.search_query_builder import (
    ComponentSearchQueryBuilder,
    FilterOperator,
    LogicalOperator,
)


class ComponentRepository(BaseRepository[Component]):
    """Repository for Component entity data access operations.

    Extends BaseRepository with component-specific query methods and
    enhanced error handling for electrical component operations.
    """

    def __init__(self, db_session: Session):
        """Initialize the Component repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, Component)
        logger.debug("ComponentRepository initialized")

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_type(
        self, component_type: ComponentType, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by type.

        Args:
            component_type: Component type enum
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components of the specified type

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by type: {component_type}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.component_type == component_type,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components of type: {component_type}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_category(
        self, category: ComponentCategoryType, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by category.

        Args:
            category: Component category enum
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components in the specified category

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by category: {category}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.category == category,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components in category: {category}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_manufacturer(
        self, manufacturer: str, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by manufacturer.

        Args:
            manufacturer: Manufacturer name
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components from the specified manufacturer

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by manufacturer: {manufacturer}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.manufacturer.ilike(f"%{manufacturer}%"),
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components from manufacturer: {manufacturer}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_by_part_number(self, part_number: str) -> Optional[Component]:
        """Get component by part number.

        Args:
            part_number: Component part number

        Returns:
            Optional[Component]: Component with the specified part number or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving component by part number: {part_number}")

        stmt = select(self.model).where(
            and_(
                self.model.part_number == part_number,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = self.db_session.scalar(stmt)

        if result:
            logger.debug(f"Component found for part number: {part_number}")
        else:
            logger.debug(f"No component found for part number: {part_number}")

        return result

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_preferred_components(
        self, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get preferred components.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of preferred components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving preferred components: skip={skip}, limit={limit}")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_preferred == True,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} preferred components")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def search_components(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Search components by name, description, manufacturer, or part number.

        Args:
            search_term: Search term to match against component fields
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components matching the search term

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching components with term: {search_term}, skip={skip}, limit={limit}"
        )

        # Use ilike for case-insensitive search
        search_pattern = f"%{search_term}%"
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                    or_(
                        self.model.name.ilike(search_pattern),
                        self.model.description.ilike(search_pattern),
                        self.model.manufacturer.ilike(search_pattern),
                        self.model.part_number.ilike(search_pattern),
                    ),
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Found {len(results)} components matching search term: {search_term}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_components_by_specifications(
        self,
        specifications: Dict[str, Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[Component]:
        """Get components matching specific technical specifications.

        Args:
            specifications: Dictionary of specification criteria
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components matching the specifications

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components by specifications: {specifications}")

        # Start with base query for active components
        stmt = select(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )

        # Add JSON specification filters with enhanced support
        for spec_path, spec_value in specifications.items():
            if spec_value is not None:
                # Support nested path queries (e.g., "electrical.voltage_rating")
                path_parts = spec_path.split('.')
                
                if len(path_parts) == 1:
                    # Simple key lookup in specifications root
                    stmt = stmt.where(
                        self.model.specifications.op('->>')(spec_path) == str(spec_value)
                    )
                elif len(path_parts) == 2:
                    # Nested lookup (e.g., electrical.voltage_rating)
                    category, key = path_parts
                    stmt = stmt.where(
                        self.model.specifications.op('->')('electrical').op('->>')(key) == str(spec_value)
                    )
                elif len(path_parts) == 3:
                    # Deep nested lookup (e.g., electrical.voltage_rating.value)
                    category, subcategory, key = path_parts
                    stmt = stmt.where(
                        self.model.specifications.op('->')('electrical').op('->')(subcategory).op('->>')(key) == str(spec_value)
                    )

        stmt = stmt.offset(skip).limit(limit).order_by(self.model.name)
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components matching specifications")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def search_components_advanced(
        self,
        search_filters: Dict[str, Any],
        specification_filters: Optional[Dict[str, Any]] = None,
        price_range: Optional[Dict[str, float]] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Component]:
        """Advanced component search with multiple filter types.

        Args:
            search_filters: Basic search filters (category, type, manufacturer, etc.)
            specification_filters: Technical specification filters
            price_range: Price range filters with min/max values
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components matching all filters

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Advanced component search with filters: {search_filters}")

        # Start with base query for active components
        conditions = [
            self.model.is_active == True,
            self.model.is_deleted == False,
        ]

        # Apply basic search filters
        if search_filters.get("category"):
            conditions.append(self.model.category == search_filters["category"])
        
        if search_filters.get("component_type"):
            conditions.append(self.model.component_type == search_filters["component_type"])
        
        if search_filters.get("manufacturer"):
            conditions.append(self.model.manufacturer.ilike(f"%{search_filters['manufacturer']}%"))
        
        if search_filters.get("is_preferred") is not None:
            conditions.append(self.model.is_preferred == search_filters["is_preferred"])
        
        if search_filters.get("stock_status"):
            conditions.append(self.model.stock_status == search_filters["stock_status"])
        
        if search_filters.get("search_term"):
            search_pattern = f"%{search_filters['search_term']}%"
            conditions.append(
                or_(
                    self.model.name.ilike(search_pattern),
                    self.model.description.ilike(search_pattern),
                    self.model.manufacturer.ilike(search_pattern),
                    self.model.part_number.ilike(search_pattern),
                )
            )

        # Apply price range filters
        if price_range:
            currency = price_range.get("currency", "USD")
            conditions.append(self.model.currency == currency)
            
            if price_range.get("min_price") is not None:
                conditions.append(self.model.unit_price >= price_range["min_price"])
            
            if price_range.get("max_price") is not None:
                conditions.append(self.model.unit_price <= price_range["max_price"])

        # Build base query
        stmt = select(self.model).where(and_(*conditions))

        # Apply specification filters
        if specification_filters:
            for spec_path, spec_value in specification_filters.items():
                if spec_value is not None:
                    path_parts = spec_path.split('.')
                    
                    if len(path_parts) == 1:
                        stmt = stmt.where(
                            self.model.specifications.op('->>')(spec_path) == str(spec_value)
                        )
                    elif len(path_parts) == 2:
                        category, key = path_parts
                        stmt = stmt.where(
                            self.model.specifications.op('->')('electrical').op('->>')(key) == str(spec_value)
                        )

        # Apply pagination and ordering
        stmt = stmt.offset(skip).limit(limit).order_by(self.model.name)
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Advanced search returned {len(results)} components")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_manufacturers_list(self) -> List[str]:
        """Get list of unique manufacturers from active components.

        Returns:
            List[str]: List of unique manufacturer names

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Retrieving unique manufacturers list")

        stmt = (
            select(self.model.manufacturer)
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .distinct()
            .order_by(self.model.manufacturer)
        )
        
        results = list(self.db_session.scalars(stmt).all())
        logger.debug(f"Retrieved {len(results)} unique manufacturers")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def bulk_create_components(self, components_data: List[Dict[str, Any]]) -> List[Component]:
        """Create multiple components in a single transaction.

        Args:
            components_data: List of component data dictionaries

        Returns:
            List[Component]: List of created components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Bulk creating {len(components_data)} components")

        created_components = []
        
        try:
            for component_data in components_data:
                component = self.model(**component_data)
                self.db_session.add(component)
                created_components.append(component)
            
            # Flush to get IDs but don't commit yet
            self.db_session.flush()
            
            logger.debug(f"Bulk created {len(created_components)} components")
            return created_components
            
        except Exception as e:
            logger.error(f"Error in bulk component creation: {e}")
            self.db_session.rollback()
            raise

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def bulk_update_components(
        self,
        component_ids: List[int],
        update_data: Dict[str, Any]
    ) -> int:
        """Update multiple components with the same data.

        Args:
            component_ids: List of component IDs to update
            update_data: Data to update for all components

        Returns:
            int: Number of components updated

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Bulk updating {len(component_ids)} components")

        stmt = (
            update(self.model)
            .where(self.model.id.in_(component_ids))
            .values(**update_data)
        )

        result = self.db_session.execute(stmt)
        updated_count = result.rowcount

        logger.debug(f"Bulk updated {updated_count} components")
        return updated_count

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def bulk_create_with_validation(
        self,
        components_data: List[Dict[str, Any]],
        validate_duplicates: bool = True,
        batch_size: int = 100
    ) -> Tuple[List[Component], List[Dict[str, Any]]]:
        """Create multiple components with validation and duplicate checking.

        Args:
            components_data: List of component data dictionaries
            validate_duplicates: Whether to check for duplicates
            batch_size: Number of components to process in each batch

        Returns:
            Tuple of (created_components, validation_errors)
        """
        logger.debug(f"Bulk creating {len(components_data)} components with validation")

        created_components = []
        validation_errors = []

        try:
            # Process in batches for better performance
            for i in range(0, len(components_data), batch_size):
                batch = components_data[i:i + batch_size]
                batch_components = []

                for idx, component_data in enumerate(batch):
                    try:
                        # Validate required fields
                        if not component_data.get('manufacturer'):
                            validation_errors.append({
                                'index': i + idx,
                                'error': 'Missing required field: manufacturer',
                                'data': component_data
                            })
                            continue

                        if not component_data.get('model_number'):
                            validation_errors.append({
                                'index': i + idx,
                                'error': 'Missing required field: model_number',
                                'data': component_data
                            })
                            continue

                        # Check for duplicates if requested
                        if validate_duplicates:
                            existing = self.db_session.query(self.model).filter(
                                and_(
                                    self.model.manufacturer == component_data['manufacturer'],
                                    self.model.model_number == component_data['model_number'],
                                    self.model.is_deleted == False
                                )
                            ).first()

                            if existing:
                                validation_errors.append({
                                    'index': i + idx,
                                    'error': f'Duplicate component: {component_data["manufacturer"]} {component_data["model_number"]}',
                                    'data': component_data,
                                    'existing_id': existing.id
                                })
                                continue

                        # Create component
                        component = self.model(**component_data)
                        self.db_session.add(component)
                        batch_components.append(component)

                    except Exception as e:
                        validation_errors.append({
                            'index': i + idx,
                            'error': f'Component creation failed: {str(e)}',
                            'data': component_data
                        })

                # Flush batch to get IDs
                if batch_components:
                    self.db_session.flush()
                    created_components.extend(batch_components)

            logger.debug(f"Bulk created {len(created_components)} components with {len(validation_errors)} errors")
            return created_components, validation_errors

        except Exception as e:
            logger.error(f"Error in bulk component creation with validation: {e}")
            self.db_session.rollback()
            raise

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def bulk_update_selective(
        self,
        updates: List[Dict[str, Any]],
        batch_size: int = 100
    ) -> Tuple[int, List[Dict[str, Any]]]:
        """Update multiple components with different data for each.

        Args:
            updates: List of update dictionaries with 'id' and update fields
            batch_size: Number of updates to process in each batch

        Returns:
            Tuple of (updated_count, validation_errors)
        """
        logger.debug(f"Bulk updating {len(updates)} components selectively")

        updated_count = 0
        validation_errors = []

        try:
            # Process in batches
            for i in range(0, len(updates), batch_size):
                batch = updates[i:i + batch_size]

                for idx, update_data in enumerate(batch):
                    try:
                        component_id = update_data.get('id')
                        if not component_id:
                            validation_errors.append({
                                'index': i + idx,
                                'error': 'Missing component ID',
                                'data': update_data
                            })
                            continue

                        # Remove ID from update data
                        update_fields = {k: v for k, v in update_data.items() if k != 'id'}

                        if not update_fields:
                            validation_errors.append({
                                'index': i + idx,
                                'error': 'No fields to update',
                                'data': update_data
                            })
                            continue

                        # Perform update
                        stmt = (
                            update(self.model)
                            .where(self.model.id == component_id)
                            .values(**update_fields)
                        )

                        result = self.db_session.execute(stmt)
                        if result.rowcount > 0:
                            updated_count += result.rowcount
                        else:
                            validation_errors.append({
                                'index': i + idx,
                                'error': f'Component with ID {component_id} not found',
                                'data': update_data
                            })

                    except Exception as e:
                        validation_errors.append({
                            'index': i + idx,
                            'error': f'Update failed: {str(e)}',
                            'data': update_data
                        })

            logger.debug(f"Bulk updated {updated_count} components with {len(validation_errors)} errors")
            return updated_count, validation_errors

        except Exception as e:
            logger.error(f"Error in selective bulk update: {e}")
            self.db_session.rollback()
            raise

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def bulk_delete_components(
        self,
        component_ids: List[int],
        soft_delete: bool = True,
        deleted_by_user_id: Optional[int] = None
    ) -> Tuple[int, List[int]]:
        """Delete multiple components (soft or hard delete).

        Args:
            component_ids: List of component IDs to delete
            soft_delete: Whether to perform soft delete (default) or hard delete
            deleted_by_user_id: ID of user performing deletion

        Returns:
            Tuple of (deleted_count, not_found_ids)
        """
        logger.debug(f"Bulk deleting {len(component_ids)} components (soft={soft_delete})")

        not_found_ids = []

        try:
            if soft_delete:
                # Soft delete - mark as deleted
                from src.core.utils.datetime_utils import utcnow_aware

                update_data = {
                    'is_deleted': True,
                    'deleted_at': utcnow_aware(),
                }

                if deleted_by_user_id:
                    update_data['deleted_by_user_id'] = deleted_by_user_id

                stmt = (
                    update(self.model)
                    .where(
                        and_(
                            self.model.id.in_(component_ids),
                            self.model.is_deleted == False
                        )
                    )
                    .values(**update_data)
                )

                result = self.db_session.execute(stmt)
                deleted_count = result.rowcount

                # Find IDs that weren't found
                if deleted_count < len(component_ids):
                    existing_ids = self.db_session.query(self.model.id).filter(
                        and_(
                            self.model.id.in_(component_ids),
                            self.model.is_deleted == False
                        )
                    ).all()
                    existing_ids_set = {id_tuple[0] for id_tuple in existing_ids}
                    not_found_ids = [id for id in component_ids if id not in existing_ids_set]

            else:
                # Hard delete - actually remove from database
                stmt = self.model.__table__.delete().where(
                    self.model.id.in_(component_ids)
                )

                result = self.db_session.execute(stmt)
                deleted_count = result.rowcount

                # Find IDs that weren't found
                if deleted_count < len(component_ids):
                    not_found_ids = component_ids[deleted_count:]

            logger.debug(f"Bulk deleted {deleted_count} components, {len(not_found_ids)} not found")
            return deleted_count, not_found_ids

        except Exception as e:
            logger.error(f"Error in bulk delete: {e}")
            self.db_session.rollback()
            raise

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_components_in_price_range(
        self, 
        min_price: Optional[float] = None, 
        max_price: Optional[float] = None,
        currency: str = "EUR",
        skip: int = 0, 
        limit: int = 100
    ) -> List[Component]:
        """Get components within a price range.

        Args:
            min_price: Minimum price (optional)
            max_price: Maximum price (optional)
            currency: Currency code (default: EUR)
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Component]: List of components within the price range

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components in price range: {min_price}-{max_price} {currency}")

        conditions = [
            self.model.is_active == True,
            self.model.is_deleted == False,
            self.model.currency == currency,
        ]

        if min_price is not None:
            conditions.append(self.model.unit_price >= min_price)
        if max_price is not None:
            conditions.append(self.model.unit_price <= max_price)

        stmt = (
            select(self.model)
            .where(and_(*conditions))
            .offset(skip)
            .limit(limit)
            .order_by(self.model.unit_price)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} components in price range")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def count_components_by_category(self) -> Dict[ComponentCategoryType, int]:
        """Count components by category.

        Returns:
            Dict[ComponentCategoryType, int]: Dictionary mapping categories to counts

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Counting components by category")

        stmt = (
            select(self.model.category, func.count(self.model.id))
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .group_by(self.model.category)
        )
        
        results = self.db_session.execute(stmt).all()
        category_counts = {category: count for category, count in results}

        logger.debug(f"Component counts by category: {category_counts}")
        return category_counts

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def count_active_components(self) -> int:
        """Count total number of active components.

        Returns:
            int: Total count of active components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Counting active components")

        stmt = select(func.count(self.model.id)).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = self.db_session.scalar(stmt)
        count = result if result is not None else 0

        logger.debug(f"Total active components count: {count}")
        return count

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def update_component_status(self, component_id: int, is_active: bool) -> bool:
        """Update component active status.

        Args:
            component_id: Component ID
            is_active: New active status

        Returns:
            bool: True if status was updated, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating status for component {component_id} to {is_active}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_active=is_active)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Status updated for component {component_id}")
            return True
        logger.debug(f"Component {component_id} not found for status update")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def update_preferred_status(self, component_id: int, is_preferred: bool) -> bool:
        """Update component preferred status.

        Args:
            component_id: Component ID
            is_preferred: New preferred status

        Returns:
            bool: True if status was updated, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating preferred status for component {component_id} to {is_preferred}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_preferred=is_preferred)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Preferred status updated for component {component_id}")
            return True
        logger.debug(f"Component {component_id} not found for preferred status update")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def soft_delete_component(self, component_id: int) -> bool:
        """Soft delete a component.

        Args:
            component_id: Component ID

        Returns:
            bool: True if component was soft deleted, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Soft deleting component {component_id}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_deleted=True, is_active=False)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Component {component_id} soft deleted successfully")
            return True
        logger.debug(f"Component {component_id} not found for soft deletion")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def restore_component(self, component_id: int) -> bool:
        """Restore a soft deleted component.

        Args:
            component_id: Component ID

        Returns:
            bool: True if component was restored, False if component not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Restoring component {component_id}")

        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_deleted=False, is_active=True)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Component {component_id} restored successfully")
            return True
        logger.debug(f"Component {component_id} not found for restoration")
        return False

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_components_paginated_with_filters(
        self,
        pagination_params: PaginationParams,
        category: Optional[ComponentCategoryType] = None,
        component_type: Optional[ComponentType] = None,
        manufacturer: Optional[str] = None,
        is_preferred: Optional[bool] = None,
        search_term: Optional[str] = None,
    ) -> PaginationResult:
        """Get paginated components with advanced filtering.

        Args:
            pagination_params: Pagination parameters
            category: Filter by category (optional)
            component_type: Filter by component type (optional)
            manufacturer: Filter by manufacturer (optional)
            is_preferred: Filter by preferred status (optional)
            search_term: Search term for name/description/part number (optional)

        Returns:
            PaginationResult: Paginated results with metadata

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Getting paginated components with filters")

        # Build filter conditions
        filters: Dict[str, Any] = {
            "is_active": True,
            "is_deleted": False,
        }

        if category is not None:
            filters["category"] = category
        if component_type is not None:
            filters["component_type"] = component_type
        if manufacturer is not None:
            filters["manufacturer"] = manufacturer
        if is_preferred is not None:
            filters["is_preferred"] = is_preferred

        # Use base repository's paginated method with search if needed
        if search_term:
            searchable_fields = ["name", "description", "manufacturer", "part_number"]
            return self.search_paginated(
                search_term=search_term,
                searchable_fields=searchable_fields,
                pagination_params=pagination_params,
                additional_filters=filters,
            )
        else:
            return self.get_paginated(
                pagination_params=pagination_params,
                filters=filters,
            )

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def search_components_with_builder(
        self,
        search_schema: ComponentAdvancedSearchSchema,
        pagination_params: PaginationParams,
    ) -> Tuple[List[Component], int]:
        """Advanced component search using the query builder.

        Args:
            search_schema: Advanced search parameters
            pagination_params: Pagination parameters

        Returns:
            Tuple of (components list, total count)

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Advanced search with builder: {search_schema.model_dump()}")

        # Initialize query builder
        builder = ComponentSearchQueryBuilder(self.model)

        # Build base query
        base_conditions = [
            self.model.is_deleted == False,
        ]

        if not search_schema.include_inactive:
            base_conditions.append(self.model.is_active == True)

        base_query = select(self.model).where(and_(*base_conditions))

        # Add text search if provided
        if search_schema.search_term:
            search_fields = search_schema.search_fields or ["name", "description", "manufacturer", "part_number"]
            builder.add_text_search(
                search_schema.search_term,
                fields=search_fields,
                fuzzy=search_schema.fuzzy_search
            )

        # Add basic filters
        if search_schema.basic_filters:
            for filter_item in search_schema.basic_filters:
                builder.add_basic_filter(
                    field=filter_item.field,
                    operator=FilterOperator(filter_item.operator),
                    value=filter_item.value,
                    logical_operator=LogicalOperator(filter_item.logical_operator)
                )

        # Add specification filters
        if search_schema.specification_filters:
            for spec_filter in search_schema.specification_filters:
                builder.add_specification_filter(
                    path=spec_filter.path,
                    operator=FilterOperator(spec_filter.operator),
                    value=spec_filter.value,
                    data_type=spec_filter.data_type,
                    unit=spec_filter.unit,
                    logical_operator=LogicalOperator(spec_filter.logical_operator)
                )

        # Add range filters
        if search_schema.range_filters:
            for range_filter in search_schema.range_filters:
                builder.add_range_filter(
                    field=range_filter.field,
                    min_value=range_filter.min_value,
                    max_value=range_filter.max_value,
                    include_min=range_filter.include_min,
                    include_max=range_filter.include_max
                )

        # Add price range if provided
        if search_schema.price_range:
            builder.add_price_range(
                min_price=search_schema.price_range.get("min_price"),
                max_price=search_schema.price_range.get("max_price"),
                currency=search_schema.price_range.get("currency", "USD")
            )

        # Build the complete query
        query = builder.build_query(base_query)

        # Add sorting
        sort_field = getattr(self.model, search_schema.sort_by, self.model.name)
        if search_schema.sort_order == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(asc(sort_field))

        # Get total count
        count_query = select(func.count()).select_from(
            builder.build_query(base_query).subquery()
        )
        total_count = self.db_session.scalar(count_query) or 0

        # Apply pagination
        skip = (pagination_params.page - 1) * pagination_params.per_page
        query = query.offset(skip).limit(pagination_params.per_page)

        # Execute query
        results = list(self.db_session.scalars(query).all())

        logger.debug(f"Advanced search returned {len(results)} components out of {total_count} total")
        return results, total_count

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def search_components_with_relevance(
        self,
        search_term: str,
        search_fields: Optional[List[str]] = None,
        fuzzy: bool = False,
        skip: int = 0,
        limit: int = 100
    ) -> List[Tuple[Component, float]]:
        """Search components with relevance scoring.

        Args:
            search_term: Text to search for
            search_fields: Fields to search in
            fuzzy: Whether to use fuzzy matching
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of tuples (component, relevance_score)
        """
        logger.debug(f"Relevance search for: {search_term}")

        search_fields = search_fields or ["name", "description", "manufacturer", "part_number"]
        search_pattern = f"%{search_term}%"

        # Build relevance scoring query
        relevance_conditions = []

        for field in search_fields:
            field_attr = getattr(self.model, field, None)
            if field_attr is not None:
                if fuzzy:
                    # Use PostgreSQL similarity for fuzzy matching
                    relevance_conditions.append(
                        func.similarity(field_attr, search_term).label(f"{field}_similarity")
                    )
                else:
                    # Use position-based scoring for exact matches
                    relevance_conditions.append(
                        func.case(
                            (field_attr.ilike(search_term), 1.0),  # Exact match
                            (field_attr.ilike(f"{search_term}%"), 0.8),  # Starts with
                            (field_attr.ilike(f"%{search_term}"), 0.6),  # Ends with
                            (field_attr.ilike(search_pattern), 0.4),  # Contains
                            else_=0.0
                        ).label(f"{field}_score")
                    )

        # Calculate total relevance score
        if fuzzy:
            total_relevance = func.greatest(*[cond for cond in relevance_conditions])
        else:
            total_relevance = sum(relevance_conditions)

        # Build query with relevance scoring
        query = (
            select(self.model, total_relevance.label("relevance_score"))
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                    or_(*[
                        getattr(self.model, field).ilike(search_pattern)
                        for field in search_fields
                        if hasattr(self.model, field)
                    ])
                )
            )
            .order_by(desc("relevance_score"), self.model.name)
            .offset(skip)
            .limit(limit)
        )

        results = []
        for row in self.db_session.execute(query):
            component = row[0]
            relevance_score = float(row[1]) if row[1] is not None else 0.0
            results.append((component, relevance_score))

        logger.debug(f"Relevance search returned {len(results)} components")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_search_suggestions(
        self,
        query: str,
        field: str = "name",
        limit: int = 10
    ) -> List[str]:
        """Get search suggestions for autocomplete.

        Args:
            query: Partial search query
            field: Field to get suggestions from
            limit: Maximum number of suggestions

        Returns:
            List of suggestion strings
        """
        logger.debug(f"Getting search suggestions for: {query} in field: {field}")

        field_attr = getattr(self.model, field, None)
        if field_attr is None:
            logger.warning(f"Field {field} not found in model")
            return []

        search_pattern = f"{query}%"

        # Get distinct values that start with the query
        stmt = (
            select(field_attr)
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                    field_attr.ilike(search_pattern),
                    field_attr.isnot(None)
                )
            )
            .distinct()
            .order_by(field_attr)
            .limit(limit)
        )

        results = [str(row) for row in self.db_session.scalars(stmt).all()]

        logger.debug(f"Found {len(results)} suggestions for {query}")
        return results

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def get_specification_values(
        self,
        specification_path: str,
        limit: int = 50
    ) -> List[Any]:
        """Get distinct values for a specification path.

        Args:
            specification_path: Dot notation path in specifications JSON
            limit: Maximum number of values to return

        Returns:
            List of distinct specification values
        """
        logger.debug(f"Getting specification values for: {specification_path}")

        path_parts = specification_path.split('.')

        try:
            if len(path_parts) == 1:
                # Simple key lookup
                json_field = self.model.specifications.op('->>')(specification_path)
            elif len(path_parts) == 2:
                # Nested lookup
                category, key = path_parts
                json_field = self.model.specifications.op('->')(category).op('->>')(key)
            elif len(path_parts) == 3:
                # Deep nested lookup
                category, subcategory, key = path_parts
                json_field = (
                    self.model.specifications
                    .op('->')(category)
                    .op('->')(subcategory)
                    .op('->>')(key)
                )
            else:
                logger.warning(f"Specification path too deep: {specification_path}")
                return []

            # Get distinct non-null values
            stmt = (
                select(json_field)
                .where(
                    and_(
                        self.model.is_active == True,
                        self.model.is_deleted == False,
                        json_field.isnot(None),
                        json_field != ""
                    )
                )
                .distinct()
                .order_by(json_field)
                .limit(limit)
            )

            results = [row for row in self.db_session.scalars(stmt).all() if row is not None]

            logger.debug(f"Found {len(results)} distinct values for {specification_path}")
            return results

        except Exception as e:
            logger.error(f"Error getting specification values for {specification_path}: {e}")
            return []

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def export_components_to_format(
        self,
        component_ids: Optional[List[int]] = None,
        filters: Optional[Dict[str, Any]] = None,
        format_type: str = "json",
        include_inactive: bool = False
    ) -> List[Dict[str, Any]]:
        """Export components to various formats.

        Args:
            component_ids: Specific component IDs to export (optional)
            filters: Additional filters to apply (optional)
            format_type: Export format (json, csv, xlsx)
            include_inactive: Whether to include inactive components

        Returns:
            List[Dict[str, Any]]: List of component data dictionaries
        """
        logger.debug(f"Exporting components to {format_type} format")

        # Build base query
        conditions = [self.model.is_deleted == False]

        if not include_inactive:
            conditions.append(self.model.is_active == True)

        if component_ids:
            conditions.append(self.model.id.in_(component_ids))

        # Apply additional filters
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    conditions.append(getattr(self.model, field) == value)

        stmt = select(self.model).where(and_(*conditions)).order_by(self.model.name)
        components = list(self.db_session.scalars(stmt).all())

        # Convert to dictionaries
        export_data = []
        for component in components:
            component_dict = {}

            # Get all column values
            for column in self.model.__table__.columns:
                value = getattr(component, column.name)

                # Handle special data types for export
                if format_type == "csv":
                    # Flatten JSON fields for CSV
                    if column.name in ['specifications', 'dimensions_json', 'metadata_json']:
                        if value:
                            import json
                            component_dict[column.name] = json.dumps(value)
                        else:
                            component_dict[column.name] = ""
                    else:
                        component_dict[column.name] = str(value) if value is not None else ""
                else:
                    # Keep original types for JSON/XLSX
                    component_dict[column.name] = value

            export_data.append(component_dict)

        logger.debug(f"Exported {len(export_data)} components")
        return export_data

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    def import_components_from_data(
        self,
        import_data: List[Dict[str, Any]],
        validate_data: bool = True,
        update_existing: bool = False,
        batch_size: int = 100
    ) -> Dict[str, Any]:
        """Import components from data with validation and conflict resolution.

        Args:
            import_data: List of component data dictionaries
            validate_data: Whether to validate data before import
            update_existing: Whether to update existing components
            batch_size: Number of components to process in each batch

        Returns:
            Dict with import results and statistics
        """
        logger.debug(f"Importing {len(import_data)} components")

        results = {
            'total_processed': len(import_data),
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': [],
            'warnings': []
        }

        try:
            # Process in batches
            for i in range(0, len(import_data), batch_size):
                batch = import_data[i:i + batch_size]

                for idx, component_data in enumerate(batch):
                    try:
                        # Validate required fields
                        if validate_data:
                            if not component_data.get('manufacturer'):
                                results['errors'].append({
                                    'index': i + idx,
                                    'error': 'Missing required field: manufacturer',
                                    'data': component_data
                                })
                                continue

                            if not component_data.get('model_number'):
                                results['errors'].append({
                                    'index': i + idx,
                                    'error': 'Missing required field: model_number',
                                    'data': component_data
                                })
                                continue

                        # Check if component already exists
                        existing = None
                        if 'manufacturer' in component_data and 'model_number' in component_data:
                            existing = self.db_session.query(self.model).filter(
                                and_(
                                    self.model.manufacturer == component_data['manufacturer'],
                                    self.model.model_number == component_data['model_number'],
                                    self.model.is_deleted == False
                                )
                            ).first()

                        if existing:
                            if update_existing:
                                # Update existing component
                                for key, value in component_data.items():
                                    if hasattr(existing, key) and key not in ['id', 'created_at']:
                                        setattr(existing, key, value)

                                results['updated'] += 1
                                results['warnings'].append({
                                    'index': i + idx,
                                    'message': f'Updated existing component: {existing.id}',
                                    'component_id': existing.id
                                })
                            else:
                                # Skip existing component
                                results['skipped'] += 1
                                results['warnings'].append({
                                    'index': i + idx,
                                    'message': f'Skipped duplicate: {component_data.get("manufacturer")} {component_data.get("model_number")}',
                                    'existing_id': existing.id
                                })
                        else:
                            # Create new component
                            # Remove any ID field from import data
                            clean_data = {k: v for k, v in component_data.items() if k != 'id'}

                            component = self.model(**clean_data)
                            self.db_session.add(component)
                            results['created'] += 1

                    except Exception as e:
                        results['errors'].append({
                            'index': i + idx,
                            'error': f'Import failed: {str(e)}',
                            'data': component_data
                        })

                # Flush batch
                self.db_session.flush()

            logger.debug(f"Import completed: {results['created']} created, {results['updated']} updated, {results['skipped']} skipped, {len(results['errors'])} errors")
            return results

        except Exception as e:
            logger.error(f"Error in component import: {e}")
            self.db_session.rollback()
            raise

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    @cached(ttl=1800, key_prefix="comp_search", invalidate_patterns=["comp:*"])
    def get_cached_search_results(
        self,
        search_term: str,
        filters: Optional[Dict[str, Any]] = None,
        pagination_params: Optional[PaginationParams] = None
    ) -> Tuple[List[Component], int]:
        """Get cached search results with performance optimization.

        Args:
            search_term: Search term
            filters: Additional filters
            pagination_params: Pagination parameters

        Returns:
            Tuple of (components, total_count)
        """
        logger.debug(f"Cached search for: {search_term}")

        # Build optimized query
        conditions = [
            self.model.is_active == True,
            self.model.is_deleted == False
        ]

        if search_term:
            search_pattern = f"%{search_term}%"
            search_conditions = [
                self.model.name.ilike(search_pattern),
                self.model.description.ilike(search_pattern),
                self.model.manufacturer.ilike(search_pattern),
                self.model.part_number.ilike(search_pattern)
            ]
            conditions.append(or_(*search_conditions))

        # Add filters
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    conditions.append(getattr(self.model, field) == value)

        # Build query with optimization hints
        base_query = select(self.model).where(and_(*conditions))

        # Get total count
        count_query = select(func.count()).select_from(base_query.subquery())
        total_count = self.db_session.scalar(count_query) or 0

        # Apply pagination
        if pagination_params:
            skip = (pagination_params.page - 1) * pagination_params.per_page
            base_query = base_query.offset(skip).limit(pagination_params.per_page)

        # Execute with optimization
        results = list(self.db_session.scalars(base_query).all())

        return results, total_count

    @handle_repository_errors("component")
    @monitor_query_performance("get_component_statistics")
    def get_performance_optimized_stats(self) -> Dict[str, Any]:
        """Get component statistics with performance optimization.

        Returns:
            Dict with component statistics
        """
        logger.debug("Getting performance optimized component statistics")

        try:
            # Use single query with aggregations for better performance
            stats_query = select(
                func.count(self.model.id).label('total_components'),
                func.count(self.model.id).filter(self.model.is_active == True).label('active_components'),
                func.count(self.model.id).filter(self.model.is_preferred == True).label('preferred_components'),
                func.count(func.distinct(self.model.manufacturer)).label('unique_manufacturers'),
                func.count(func.distinct(self.model.category)).label('unique_categories'),
                func.avg(self.model.unit_price).filter(self.model.unit_price.isnot(None)).label('avg_price'),
                func.min(self.model.unit_price).filter(self.model.unit_price.isnot(None)).label('min_price'),
                func.max(self.model.unit_price).filter(self.model.unit_price.isnot(None)).label('max_price')
            ).where(self.model.is_deleted == False)

            result = self.db_session.execute(stats_query).first()

            stats = {
                'total_components': result.total_components or 0,
                'active_components': result.active_components or 0,
                'preferred_components': result.preferred_components or 0,
                'unique_manufacturers': result.unique_manufacturers or 0,
                'unique_categories': result.unique_categories or 0,
                'price_statistics': {
                    'average': float(result.avg_price) if result.avg_price else 0.0,
                    'minimum': float(result.min_price) if result.min_price else 0.0,
                    'maximum': float(result.max_price) if result.max_price else 0.0
                }
            }

            return stats

        except Exception as e:
            logger.error(f"Error getting optimized component statistics: {e}")
            raise

    @handle_repository_errors("component")
    @monitor_query_performance("batch_get_components")
    def batch_get_components_by_ids(
        self,
        component_ids: List[int],
        batch_size: int = 100
    ) -> List[Component]:
        """Get multiple components by IDs with batch optimization.

        Args:
            component_ids: List of component IDs
            batch_size: Batch size for processing

        Returns:
            List of components
        """
        logger.debug(f"Batch getting {len(component_ids)} components")

        all_components = []

        # Process in batches to avoid large IN clauses
        for i in range(0, len(component_ids), batch_size):
            batch_ids = component_ids[i:i + batch_size]

            stmt = select(self.model).where(
                and_(
                    self.model.id.in_(batch_ids),
                    self.model.is_deleted == False
                )
            ).order_by(self.model.name)

            batch_components = list(self.db_session.scalars(stmt).all())
            all_components.extend(batch_components)

        return all_components

    @handle_repository_errors("component")
    @monitor_query_performance("preload_relationships")
    def get_components_with_preloaded_relationships(
        self,
        component_ids: Optional[List[int]] = None,
        limit: int = 100
    ) -> List[Component]:
        """Get components with preloaded relationships for better performance.

        Args:
            component_ids: Optional list of specific component IDs
            limit: Maximum number of components to return

        Returns:
            List of components with preloaded relationships
        """
        logger.debug("Getting components with preloaded relationships")

        # Build query with eager loading
        query = select(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False
            )
        )

        if component_ids:
            query = query.where(self.model.id.in_(component_ids))

        query = query.order_by(self.model.name).limit(limit)

        # Execute with relationship preloading
        components = list(self.db_session.scalars(query).all())

        return components

    def invalidate_component_cache(self, component_id: Optional[int] = None) -> None:
        """Invalidate component-related cache entries.

        Args:
            component_id: Specific component ID to invalidate (optional)
        """
        if component_id:
            # Invalidate specific component cache
            cache_manager.delete_pattern(f"comp:*:{component_id}:*")
            cache_manager.delete_pattern(f"comp_search:*")
        else:
            # Invalidate all component cache
            cache_manager.delete_pattern("comp:*")
            cache_manager.delete_pattern("comp_search:*")
            cache_manager.delete_pattern("spec:*")

        logger.debug(f"Invalidated component cache for ID: {component_id or 'all'}")

    def warm_cache_for_popular_searches(self) -> None:
        """Warm cache for popular search terms and filters.

        This method pre-loads cache for commonly searched terms to improve
        response times for frequent queries.
        """
        logger.info("Warming cache for popular searches")

        popular_searches = [
            "circuit breaker",
            "transformer",
            "motor",
            "switch",
            "relay"
        ]

        popular_manufacturers = [
            "Schneider Electric",
            "ABB",
            "Siemens",
            "General Electric",
            "Eaton"
        ]

        # Warm search cache
        for search_term in popular_searches:
            try:
                self.get_cached_search_results(
                    search_term=search_term,
                    pagination_params=PaginationParams(page=1, per_page=20)
                )
            except Exception as e:
                logger.warning(f"Error warming cache for search '{search_term}': {e}")

        # Warm manufacturer filter cache
        for manufacturer in popular_manufacturers:
            try:
                self.get_cached_search_results(
                    search_term="",
                    filters={"manufacturer": manufacturer},
                    pagination_params=PaginationParams(page=1, per_page=20)
                )
            except Exception as e:
                logger.warning(f"Error warming cache for manufacturer '{manufacturer}': {e}")

        logger.info("Cache warming completed")