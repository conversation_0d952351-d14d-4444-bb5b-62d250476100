# tests/unit/test_errors/test_error_handling_result.py
"""
Comprehensive tests for unified error handling system.

This module tests the unified error handler including error handling results.
"""

import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager

import pytest
from fastapi import Request, HTTPException

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.core.errors.unified_error_handler import (
    ErrorContext,
    ErrorHandlingResult,
    UnifiedErrorHandler,
    unified_error_handler,
    get_unified_error_handler,
    handle_service_errors,
    handle_repository_errors,
    handle_calculation_errors,
)
from src.core.errors.exceptions import (
    BaseApplicationException,
    NotFoundError,
    DataValidationError,
    InvalidInputError,
    ServiceError,
    DatabaseError,
    CalculationError,
    DuplicateEntryError,
)

pytestmark = [pytest.mark.unit]


class TestErrorHandlingResult:
    """Test suite for ErrorHandlingResult class."""

    def test_error_handling_result_creation_minimal(self):
        """Test ErrorHandlingResult creation with minimal data."""
        error_response = Mock()
        error_response.detail = "Test error"

        result = ErrorHandlingResult(
            error_response=error_response,
            http_status_code=400,
        )

        assert result.error_response == error_response
        assert result.http_status_code == 400
        assert result.should_log is True  # Default
        assert result.log_level == logging.ERROR  # Default
        assert result.context_data == {}  # Default

    def test_error_handling_result_creation_complete(self):
        """Test ErrorHandlingResult creation with complete data."""
        error_response = Mock()
        error_response.detail = "Validation failed"
        context_data = {"user_id": "123", "operation": "create_user"}

        result = ErrorHandlingResult(
            error_response=error_response,
            http_status_code=422,
            should_log=False,
            log_level=logging.WARNING,
            context_data=context_data,
        )

        assert result.error_response == error_response
        assert result.http_status_code == 422
        assert result.should_log is False
        assert result.log_level == logging.WARNING
        assert result.context_data == context_data

    def test_error_handling_result_defaults(self):
        """Test ErrorHandlingResult default values."""
        error_response = Mock()

        result = ErrorHandlingResult(
            error_response=error_response,
            http_status_code=500,
        )

        assert result.should_log is True
        assert result.log_level == logging.ERROR
        assert result.context_data == {}
