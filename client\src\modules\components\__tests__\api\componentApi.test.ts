/**
 * Component API Client Tests
 * Tests the low-level API functions for component management
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { componentApi } from '../../api/componentApi';
import { 
  mockComponent, 
  mockComponentCreate, 
  mockComponentUpdate,
  mockComponentPaginatedResponse,
  mockApiSuccess,
  mockApiError 
} from '@/test/utils';

// Mock the API request function
const mockApiRequest = vi.fn();
vi.mock('@/lib/api/client', () => ({
  apiRequest: mockApiRequest,
  buildUrl: (path: string) => `http://localhost:8000/api/v1${path}`,
}));

describe('componentApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('list', () => {
    it('fetches components with default parameters', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      const result = await componentApi.list();

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/?'
      );
      expect(result).toEqual(mockApiSuccess(mockComponentPaginatedResponse));
    });

    it('fetches components with pagination parameters', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      await componentApi.list({ page: 2, size: 20 });

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/?page=2&size=20'
      );
    });

    it('fetches components with filter parameters', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      await componentApi.list({
        search_term: 'resistor',
        category: 'RESISTOR',
        manufacturer: 'Test Electronics',
        is_preferred: true,
        is_active: true,
      });

      expect(mockApiRequest).toHaveBeenCalledWith(
        expect.stringContaining('search_term=resistor')
      );
      expect(mockApiRequest).toHaveBeenCalledWith(
        expect.stringContaining('category=RESISTOR')
      );
      expect(mockApiRequest).toHaveBeenCalledWith(
        expect.stringContaining('manufacturer=Test+Electronics')
      );
      expect(mockApiRequest).toHaveBeenCalledWith(
        expect.stringContaining('is_preferred=true')
      );
      expect(mockApiRequest).toHaveBeenCalledWith(
        expect.stringContaining('is_active=true')
      );
    });

    it('handles undefined and null parameters', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      await componentApi.list({
        page: undefined,
        search_term: null as any,
        is_preferred: false,
      });

      const calledUrl = mockApiRequest.mock.calls[0][0];
      expect(calledUrl).not.toContain('page=');
      expect(calledUrl).not.toContain('search_term=');
      expect(calledUrl).toContain('is_preferred=false');
    });

    it('handles API errors', async () => {
      const error = mockApiError('Failed to fetch components');
      mockApiRequest.mockResolvedValue(error);

      const result = await componentApi.list();

      expect(result).toEqual(error);
    });
  });

  describe('getById', () => {
    it('fetches component by ID', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponent));

      const result = await componentApi.getById(1);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/1'
      );
      expect(result).toEqual(mockApiSuccess(mockComponent));
    });

    it('handles non-existent component', async () => {
      const error = mockApiError('Component not found', 'COMPONENT_NOT_FOUND');
      mockApiRequest.mockResolvedValue(error);

      const result = await componentApi.getById(999);

      expect(result).toEqual(error);
    });
  });

  describe('create', () => {
    it('creates a new component', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponent));

      const result = await componentApi.create(mockComponentCreate);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/',
        {
          method: 'POST',
          body: JSON.stringify(mockComponentCreate),
        }
      );
      expect(result).toEqual(mockApiSuccess(mockComponent));
    });

    it('handles validation errors', async () => {
      const error = mockApiError('Validation failed', 'VALIDATION_ERROR');
      mockApiRequest.mockResolvedValue(error);

      const result = await componentApi.create(mockComponentCreate);

      expect(result).toEqual(error);
    });

    it('handles network errors', async () => {
      mockApiRequest.mockRejectedValue(new Error('Network error'));

      await expect(componentApi.create(mockComponentCreate)).rejects.toThrow('Network error');
    });
  });

  describe('update', () => {
    it('updates an existing component', async () => {
      const updatedComponent = { ...mockComponent, ...mockComponentUpdate };
      mockApiRequest.mockResolvedValue(mockApiSuccess(updatedComponent));

      const result = await componentApi.update(1, mockComponentUpdate);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/1',
        {
          method: 'PUT',
          body: JSON.stringify(mockComponentUpdate),
        }
      );
      expect(result).toEqual(mockApiSuccess(updatedComponent));
    });

    it('handles partial updates', async () => {
      const partialUpdate = { name: 'Updated Name' };
      const updatedComponent = { ...mockComponent, name: 'Updated Name' };
      mockApiRequest.mockResolvedValue(mockApiSuccess(updatedComponent));

      const result = await componentApi.update(1, partialUpdate);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/1',
        {
          method: 'PUT',
          body: JSON.stringify(partialUpdate),
        }
      );
      expect(result).toEqual(mockApiSuccess(updatedComponent));
    });

    it('handles non-existent component', async () => {
      const error = mockApiError('Component not found', 'COMPONENT_NOT_FOUND');
      mockApiRequest.mockResolvedValue(error);

      const result = await componentApi.update(999, mockComponentUpdate);

      expect(result).toEqual(error);
    });
  });

  describe('delete', () => {
    it('deletes a component', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(undefined));

      const result = await componentApi.delete(1);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/1',
        {
          method: 'DELETE',
        }
      );
      expect(result).toEqual(mockApiSuccess(undefined));
    });

    it('handles non-existent component', async () => {
      const error = mockApiError('Component not found', 'COMPONENT_NOT_FOUND');
      mockApiRequest.mockResolvedValue(error);

      const result = await componentApi.delete(999);

      expect(result).toEqual(error);
    });

    it('handles deletion conflicts', async () => {
      const error = mockApiError('Component is in use', 'COMPONENT_IN_USE');
      mockApiRequest.mockResolvedValue(error);

      const result = await componentApi.delete(1);

      expect(result).toEqual(error);
    });
  });

  describe('search', () => {
    it('searches components with basic parameters', async () => {
      const searchParams = {
        query: 'resistor',
        field: 'name' as const,
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      const result = await componentApi.search(searchParams);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/search?',
        {
          method: 'POST',
          body: JSON.stringify(searchParams),
        }
      );
      expect(result).toEqual(mockApiSuccess(mockComponentPaginatedResponse));
    });

    it('searches components with pagination', async () => {
      const searchParams = {
        query: 'resistor',
        field: 'name' as const,
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      await componentApi.search(searchParams, { page: 2, size: 20 });

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/search?page=2&size=20',
        {
          method: 'POST',
          body: JSON.stringify(searchParams),
        }
      );
    });

    it('handles empty search results', async () => {
      const emptyResponse = { ...mockComponentPaginatedResponse, items: [], total: 0 };
      mockApiRequest.mockResolvedValue(mockApiSuccess(emptyResponse));

      const result = await componentApi.search({ query: 'nonexistent', field: 'name' });

      expect(result).toEqual(mockApiSuccess(emptyResponse));
    });
  });

  describe('advancedSearch', () => {
    it('performs advanced search with filters', async () => {
      const advancedParams = {
        filters: {
          category: 'RESISTOR' as const,
          manufacturer: 'Test Electronics',
          price_range: { min: 0.1, max: 1.0 },
        },
        specifications: {
          resistance: { value: '1000', operator: 'eq' as const },
        },
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      const result = await componentApi.advancedSearch(advancedParams);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/advanced-search?',
        {
          method: 'POST',
          body: JSON.stringify(advancedParams),
        }
      );
      expect(result).toEqual(mockApiSuccess(mockComponentPaginatedResponse));
    });

    it('handles complex specification filters', async () => {
      const complexParams = {
        specifications: {
          resistance: { value: '1000', operator: 'gte' as const },
          tolerance: { value: '1%', operator: 'lte' as const },
          power_rating: { value: '0.25W', operator: 'eq' as const },
        },
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse));

      await componentApi.advancedSearch(complexParams);

      expect(mockApiRequest).toHaveBeenCalledWith(
        expect.any(String),
        {
          method: 'POST',
          body: JSON.stringify(complexParams),
        }
      );
    });
  });

  describe('bulkCreate', () => {
    it('creates multiple components', async () => {
      const bulkData = {
        components: [mockComponentCreate, { ...mockComponentCreate, name: 'Component 2' }],
        validate_only: false,
      };
      const bulkResponse = {
        created: [mockComponent, { ...mockComponent, id: 2, name: 'Component 2' }],
        errors: [],
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(bulkResponse));

      const result = await componentApi.bulkCreate(bulkData);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/bulk',
        {
          method: 'POST',
          body: JSON.stringify(bulkData),
        }
      );
      expect(result).toEqual(mockApiSuccess(bulkResponse));
    });

    it('validates components without creating', async () => {
      const bulkData = {
        components: [mockComponentCreate],
        validate_only: true,
      };
      const validationResponse = {
        valid: [mockComponentCreate],
        errors: [],
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(validationResponse));

      const result = await componentApi.bulkCreate(bulkData);

      expect(result).toEqual(mockApiSuccess(validationResponse));
    });
  });

  describe('bulkUpdate', () => {
    it('updates multiple components', async () => {
      const bulkData = {
        updates: [
          { id: 1, data: { name: 'Updated 1' } },
          { id: 2, data: { name: 'Updated 2' } },
        ],
      };
      const bulkResponse = {
        updated: [
          { ...mockComponent, name: 'Updated 1' },
          { ...mockComponent, id: 2, name: 'Updated 2' },
        ],
        errors: [],
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(bulkResponse));

      const result = await componentApi.bulkUpdate(bulkData);

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/bulk-update',
        {
          method: 'PUT',
          body: JSON.stringify(bulkData),
        }
      );
      expect(result).toEqual(mockApiSuccess(bulkResponse));
    });
  });

  describe('getStats', () => {
    it('fetches component statistics', async () => {
      const stats = {
        total_components: 150,
        active_components: 142,
        categories: { RESISTOR: 45, CAPACITOR: 38 },
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(stats));

      const result = await componentApi.getStats();

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/stats'
      );
      expect(result).toEqual(mockApiSuccess(stats));
    });
  });

  describe('getSummary', () => {
    it('fetches component summary', async () => {
      const summary = {
        recent_components: [mockComponent],
        popular_components: [mockComponent],
        low_stock_components: [],
      };
      mockApiRequest.mockResolvedValue(mockApiSuccess(summary));

      const result = await componentApi.getSummary();

      expect(mockApiRequest).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/components/summary'
      );
      expect(result).toEqual(mockApiSuccess(summary));
    });
  });

  describe('Error Handling', () => {
    it('handles network timeouts', async () => {
      mockApiRequest.mockRejectedValue(new Error('Request timeout'));

      await expect(componentApi.list()).rejects.toThrow('Request timeout');
    });

    it('handles malformed responses', async () => {
      mockApiRequest.mockResolvedValue({ invalid: 'response' });

      const result = await componentApi.list();

      expect(result).toEqual({ invalid: 'response' });
    });

    it('handles server errors', async () => {
      const serverError = mockApiError('Internal server error', 'INTERNAL_ERROR');
      mockApiRequest.mockResolvedValue(serverError);

      const result = await componentApi.list();

      expect(result).toEqual(serverError);
    });
  });

  describe('Request Options', () => {
    it('passes through request options', async () => {
      mockApiRequest.mockResolvedValue(mockApiSuccess(mockComponent));

      await componentApi.create(mockComponentCreate, {
        timeout: 10000,
        headers: { 'Custom-Header': 'value' },
      });

      expect(mockApiRequest).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(mockComponentCreate),
          timeout: 10000,
          headers: { 'Custom-Header': 'value' },
        })
      );
    });
  });
});
