# Agent Implementation Guides

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Framework:** AI Agent Team Framework for Ultimate Electrical Designer  

## Overview

This document provides detailed implementation guides for each AI agent in the Ultimate Electrical Designer team framework. Each guide includes specific responsibilities, implementation patterns, code examples, and integration requirements that ensure engineering-grade standards and unified patterns compliance.

## Table of Contents

- [Agent Implementation Guides](#agent-implementation-guides)
  - [Overview](#overview)
  - [Table of Contents](#table-of-contents)
  - [Project Manager Agent Implementation](#project-manager-agent-implementation)
    - [Core Responsibilities Matrix](#core-responsibilities-matrix)
    - [Implementation Patterns](#implementation-patterns)
      - [Standards Compliance Validation](#standards-compliance-validation)
      - [Team Coordination Workflow](#team-coordination-workflow)
  - [API Layer Agent Implementation](#api-layer-agent-implementation)
    - [FastAPI Mastery Patterns](#fastapi-mastery-patterns)
      - [Standard Endpoint Implementation](#standard-endpoint-implementation)
      - [Authentication and Authorization Patterns](#authentication-and-authorization-patterns)
  - [Service Layer Agent Implementation](#service-layer-agent-implementation)
    - [Business Logic Orchestration Patterns](#business-logic-orchestration-patterns)
      - [Complex Workflow Implementation](#complex-workflow-implementation)
      - [Transaction Management Patterns](#transaction-management-patterns)
  - [Repository Layer Agent Implementation](#repository-layer-agent-implementation)
    - [Data Access Optimization Patterns](#data-access-optimization-patterns)
      - [Advanced Query Implementation](#advanced-query-implementation)
      - [Performance Optimization Patterns](#performance-optimization-patterns)

## Project Manager Agent Implementation

### Core Responsibilities Matrix

```yaml
Standards_Enforcement:
  Zero_Tolerance_Policies:
    - No 'however' scenarios allowed
    - Single source of truth per calculation type
    - Complete implementations only
    - IEEE/IEC/EN standards only
  
  Validation_Procedures:
    - Unified patterns compliance verification
    - Architecture pattern validation
    - Cross-layer integration oversight
    - Documentation consistency checks

Team_Coordination:
  Task_Distribution:
    - Assign tasks based on agent expertise
    - Ensure proper dependency ordering
    - Monitor task completion and quality
    - Coordinate cross-agent collaboration
  
  Integration_Oversight:
    - Validate layer boundary compliance
    - Ensure proper data flow patterns
    - Monitor performance across layers
    - Coordinate complex feature implementations

Quality_Assurance:
  Metrics_Monitoring:
    - Unified patterns compliance: 100%
    - Test coverage: 90%+ critical, 85%+ high priority
    - Performance targets: <200ms API, <500ms calculations
    - Security compliance: 0 critical vulnerabilities
  
  Decision_Authority:
    - Final approval on architecture changes
    - Standards compliance enforcement
    - Conflict resolution between agents
    - Escalation to human oversight
```

### Implementation Patterns

#### Standards Compliance Validation
```python
class ProjectManagerAgent:
    def validate_standards_compliance(self, implementation):
        """Validate implementation against project standards."""
        compliance_results = {
            'unified_patterns': self._check_unified_patterns(implementation),
            'ieee_iec_en_standards': self._check_electrical_standards(implementation),
            'architecture_compliance': self._check_architecture_patterns(implementation),
            'zero_tolerance_policies': self._check_zero_tolerance(implementation)
        }
        
        if not all(compliance_results.values()):
            return self._reject_implementation(implementation, compliance_results)
        
        return self._approve_implementation(implementation)
    
    def _check_unified_patterns(self, implementation):
        """Verify unified patterns usage."""
        required_patterns = [
            '@handle_service_errors',
            '@handle_repository_errors', 
            '@handle_api_errors',
            '@monitor_performance'
        ]
        return all(pattern in implementation.code for pattern in required_patterns)
    
    def _check_electrical_standards(self, implementation):
        """Verify IEEE/IEC/EN standards compliance."""
        forbidden_standards = ['NFPA', 'API']
        approved_standards = ['IEEE', 'IEC', 'EN']
        
        has_forbidden = any(std in implementation.references for std in forbidden_standards)
        has_approved = any(std in implementation.references for std in approved_standards)
        
        return not has_forbidden and (has_approved or not implementation.requires_standards)
```

#### Team Coordination Workflow
```python
def coordinate_feature_implementation(self, feature_request):
    """Coordinate multi-agent feature implementation."""
    # Phase 1: Planning and Design
    design_plan = self._create_implementation_plan(feature_request)
    
    # Phase 2: Agent Assignment
    agent_assignments = {
        'schema_design': 'Schema/Model Layer Agent',
        'data_access': 'Repository Layer Agent', 
        'business_logic': 'Service Layer Agent',
        'api_endpoints': 'API Layer Agent',
        'security_validation': 'Security Agent',
        'performance_optimization': 'Performance Agent',
        'test_implementation': 'Testing Agent',
        'quality_validation': 'Code Quality Agent'
    }
    
    # Phase 3: Implementation Coordination
    for phase, agent in agent_assignments.items():
        result = self._coordinate_with_agent(agent, design_plan[phase])
        if not result.success:
            return self._handle_implementation_failure(phase, result)
    
    # Phase 4: Integration Validation
    return self._validate_complete_implementation(feature_request)
```

## API Layer Agent Implementation

### FastAPI Mastery Patterns

#### Standard Endpoint Implementation
```python
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.security.enhanced_dependencies import require_permissions, get_current_user
from src.core.services.project_service import ProjectService
from src.core.schemas.project_schemas import ProjectCreateSchema, ProjectResponse

router = APIRouter(prefix="/projects", tags=["projects"])

class APILayerAgent:
    """API Layer Agent implementation patterns."""
    
    @router.post("", response_model=ProjectResponse, status_code=status.HTTP_201_CREATED)
    @handle_api_errors("create_project")
    @require_permissions("project:create")
    async def create_project(
        self,
        project_data: ProjectCreateSchema,
        current_user: User = Depends(get_current_user),
        project_service: ProjectService = Depends(get_project_service)
    ) -> ProjectResponse:
        """Create electrical design project with unified patterns."""
        # Validate electrical engineering requirements
        self._validate_electrical_requirements(project_data)
        
        # Create project through service layer
        project = project_service.create_project(
            project_data.model_dump(),
            created_by=current_user.id
        )
        
        # Return validated response
        return ProjectResponse.model_validate(project)
    
    def _validate_electrical_requirements(self, project_data: ProjectCreateSchema):
        """Validate electrical engineering requirements."""
        # Coordinate with Electrical Engineering Agent
        if project_data.environment_type == "hazardous":
            if "IEC-60079" not in project_data.design_standards:
                raise HTTPException(
                    status_code=400,
                    detail="Hazardous environments require IEC-60079 compliance"
                )
        
        # Validate voltage level standards
        if not self._is_standard_voltage(project_data.voltage_level):
            # Log warning but allow non-standard voltages
            logger.warning(f"Non-standard voltage: {project_data.voltage_level}V")
```

#### Authentication and Authorization Patterns
```python
from src.core.security.enhanced_dependencies import require_permissions

class APISecurityPatterns:
    """Security patterns for API Layer Agent."""
    
    @router.get("/projects/{project_id}/sensitive-data")
    @handle_api_errors("get_sensitive_data")
    @require_permissions("project:read", "sensitive_data:access")
    async def get_sensitive_project_data(
        self,
        project_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_user),
        project_service: ProjectService = Depends(get_project_service)
    ):
        """Access sensitive project data with multiple permission requirements."""
        # Additional security validation
        if not self._user_has_project_access(current_user, project_id):
            raise HTTPException(status_code=403, detail="Project access denied")
        
        return project_service.get_sensitive_data(project_id)
    
    def _user_has_project_access(self, user: User, project_id: int) -> bool:
        """Validate user has access to specific project."""
        # Coordinate with Security Agent for access validation
        return user.has_project_permission(project_id)
```

## Service Layer Agent Implementation

### Business Logic Orchestration Patterns

#### Complex Workflow Implementation
```python
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.performance_decorators import monitor_service_performance
from src.core.services.base_service import BaseService

class ServiceLayerAgent:
    """Service Layer Agent implementation patterns."""
    
    @handle_service_errors("heat_tracing_design")
    @monitor_service_performance("heat_tracing_design")
    def design_heat_tracing_system(
        self,
        project_id: int,
        design_parameters: HeatTracingDesignParameters
    ) -> HeatTracingDesignResult:
        """Complete heat tracing system design workflow."""
        
        # Phase 1: Validate project and parameters
        project = self.project_repository.get_by_id(project_id)
        self._validate_design_parameters(design_parameters, project)
        
        # Phase 2: Coordinate with Electrical Engineering Agent
        thermal_analysis = self.electrical_agent.calculate_heat_loss(
            design_parameters.pipe_parameters,
            project.ambient_conditions
        )
        
        # Phase 3: Cable selection and sizing
        cable_selection = self.electrical_agent.select_heat_tracing_cable(
            thermal_analysis.power_requirements,
            design_parameters.installation_conditions
        )
        
        # Phase 4: Control system design
        control_system = self._design_control_system(
            cable_selection,
            design_parameters.control_requirements
        )
        
        # Phase 5: Generate complete design
        complete_design = self._generate_complete_design(
            thermal_analysis,
            cable_selection,
            control_system
        )
        
        # Phase 6: Validate against standards
        self._validate_design_compliance(complete_design, project)
        
        return complete_design
    
    def _validate_design_compliance(self, design, project):
        """Validate design against IEEE/IEC/EN standards."""
        # Coordinate with Electrical Engineering Agent
        compliance_check = self.electrical_agent.validate_standards_compliance(
            design, project.design_standards
        )
        
        if not compliance_check.is_compliant:
            raise BusinessRuleError(
                f"Design violates standards: {compliance_check.violations}"
            )
```

#### Transaction Management Patterns
```python
from contextlib import contextmanager
from sqlalchemy.exc import SQLAlchemyError

class TransactionManagement:
    """Transaction management patterns for Service Layer."""
    
    @contextmanager
    def managed_transaction(self):
        """Context manager for transaction handling."""
        try:
            yield self.db
            self.db.commit()
        except SQLAlchemyError as e:
            self.db.rollback()
            raise ServiceError(f"Transaction failed: {str(e)}")
        except Exception as e:
            self.db.rollback()
            raise ServiceError(f"Unexpected error: {str(e)}")
    
    @handle_service_errors("complex_project_update")
    def update_project_with_dependencies(
        self,
        project_id: int,
        update_data: dict,
        cascade_updates: bool = True
    ):
        """Update project with dependent entity updates."""
        with self.managed_transaction():
            # Update project
            project = self.project_repository.update(project_id, update_data)
            
            if cascade_updates:
                # Update dependent pipes
                self._update_project_pipes(project)
                
                # Update heat tracing circuits
                self._update_heat_tracing_circuits(project)
                
                # Recalculate electrical loads
                self._recalculate_electrical_loads(project)
            
            return project
```

## Repository Layer Agent Implementation

### Data Access Optimization Patterns

#### Advanced Query Implementation
```python
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import and_, or_, func
from src.core.errors.unified_error_handler import handle_repository_errors

class RepositoryLayerAgent:
    """Repository Layer Agent implementation patterns."""
    
    @handle_repository_errors("project_with_full_details")
    def get_project_with_complete_details(self, project_id: int) -> Project:
        """Get project with all related entities optimized loading."""
        try:
            project = self.db.query(Project).options(
                # Eager load pipes with heat tracing circuits
                joinedload(Project.pipes).joinedload(Pipe.ht_circuit),
                
                # Eager load tanks with heat tracing circuits  
                joinedload(Project.tanks).joinedload(Tank.ht_circuit),
                
                # Select in load for electrical nodes (many-to-many optimization)
                selectinload(Project.electrical_nodes),
                
                # Eager load cable routes with from/to nodes
                joinedload(Project.cable_routes).joinedload(CableRoute.from_node),
                joinedload(Project.cable_routes).joinedload(CableRoute.to_node),
                
                # Load audit information
                joinedload(Project.created_by_user),
                joinedload(Project.updated_by_user)
            ).filter(
                Project.id == project_id,
                Project.deleted_at.is_(None)
            ).first()
            
            if not project:
                raise NotFoundError(f"Project {project_id} not found")
            
            return project
            
        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error loading project: {str(e)}")
    
    @handle_repository_errors("complex_project_search")
    def search_projects_with_advanced_filters(
        self,
        search_criteria: ProjectSearchCriteria
    ) -> List[Project]:
        """Advanced project search with multiple filter criteria."""
        try:
            query = self.db.query(Project).filter(Project.deleted_at.is_(None))
            
            # Text search across multiple fields
            if search_criteria.search_text:
                search_term = f"%{search_criteria.search_text}%"
                query = query.filter(
                    or_(
                        Project.name.ilike(search_term),
                        Project.description.ilike(search_term),
                        Project.client_name.ilike(search_term),
                        Project.project_location.ilike(search_term)
                    )
                )
            
            # Voltage range filtering
            if search_criteria.voltage_min:
                query = query.filter(Project.voltage_level >= search_criteria.voltage_min)
            if search_criteria.voltage_max:
                query = query.filter(Project.voltage_level <= search_criteria.voltage_max)
            
            # Environment type filtering
            if search_criteria.environment_types:
                query = query.filter(Project.environment_type.in_(search_criteria.environment_types))
            
            # Standards compliance filtering
            if search_criteria.required_standards:
                for standard in search_criteria.required_standards:
                    query = query.filter(
                        func.json_extract(Project.design_standards, f'$[*]').like(f'%{standard}%')
                    )
            
            # Date range filtering
            if search_criteria.created_after:
                query = query.filter(Project.created_at >= search_criteria.created_after)
            if search_criteria.created_before:
                query = query.filter(Project.created_at <= search_criteria.created_before)
            
            # Apply ordering
            if search_criteria.order_by:
                order_field = getattr(Project, search_criteria.order_by)
                if search_criteria.order_direction == 'desc':
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
            else:
                query = query.order_by(desc(Project.created_at))
            
            # Apply pagination
            if search_criteria.limit:
                query = query.offset(search_criteria.skip).limit(search_criteria.limit)
            
            return query.all()
            
        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during search: {str(e)}")
```

#### Performance Optimization Patterns
```python
class PerformanceOptimizedRepository:
    """Performance optimization patterns for Repository Layer."""
    
    @handle_repository_errors("bulk_operations")
    def bulk_create_components(self, components_data: List[dict]) -> List[Component]:
        """Optimized bulk component creation."""
        try:
            # Use bulk insert for performance
            components = [Component(**data) for data in components_data]
            
            # Batch insert with return of IDs
            self.db.bulk_save_objects(components, return_defaults=True)
            self.db.commit()
            
            # Refresh objects to get generated IDs and relationships
            for component in components:
                self.db.refresh(component)
            
            return components
            
        except SQLAlchemyError as e:
            self.db.rollback()
            raise RepositoryError(f"Bulk creation failed: {str(e)}")
    
    @handle_repository_errors("optimized_aggregation")
    def get_project_statistics_optimized(self, project_id: int) -> dict:
        """Get project statistics with optimized aggregation queries."""
        try:
            # Single query for multiple aggregations
            stats = self.db.query(
                func.count(Pipe.id).label('total_pipes'),
                func.sum(Pipe.length).label('total_pipe_length'),
                func.avg(Pipe.diameter).label('average_diameter'),
                func.count(Tank.id).label('total_tanks'),
                func.count(HeatTracingCircuit.id).label('total_circuits')
            ).select_from(Project).outerjoin(
                Pipe, and_(Pipe.project_id == Project.id, Pipe.deleted_at.is_(None))
            ).outerjoin(
                Tank, and_(Tank.project_id == Project.id, Tank.deleted_at.is_(None))
            ).outerjoin(
                HeatTracingCircuit, or_(
                    HeatTracingCircuit.pipe_id == Pipe.id,
                    HeatTracingCircuit.tank_id == Tank.id
                )
            ).filter(
                Project.id == project_id,
                Project.deleted_at.is_(None)
            ).first()
            
            return {
                'total_pipes': stats.total_pipes or 0,
                'total_pipe_length': float(stats.total_pipe_length or 0),
                'average_diameter': float(stats.average_diameter or 0),
                'total_tanks': stats.total_tanks or 0,
                'total_circuits': stats.total_circuits or 0
            }
            
        except SQLAlchemyError as e:
            raise RepositoryError(f"Statistics calculation failed: {str(e)}")
```

---

**Navigation:**  
← [Coordination Protocols](coordination-protocols.md) | [Next: Quality Assurance](quality-assurance.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Framework Overview](README.md)
- [Unified Patterns Guide](../handbook/04-unified-patterns.md)
- [Backend Development Guide](../handbook/05-backend-development.md)
