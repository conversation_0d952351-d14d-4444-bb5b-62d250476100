#!/usr/bin/env python3
"""Component Service for Ultimate Electrical Designer.

This module provides comprehensive business logic for component management
operations, including CRUD operations, validation, search, and catalog management
for electrical components.

Key Features:
- Complete component lifecycle management (create, read, update, delete)
- Advanced search and filtering with specification-based queries
- Component validation and business rule enforcement
- Bulk operations support with transaction management
- Dependency resolution and conflict detection
- Audit logging and change tracking
- Caching strategies for performance optimization
- Professional electrical design standards compliance
"""

import json
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple, Union

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from src.config.logging_config import logger
from src.core.enums.electrical_enums import (
    COMPONENT_TYPE_TO_CATEGORY_MAPPING,
    ComponentCategoryType,
    ComponentType,
)
from src.core.errors.exceptions import (
    BusinessLogicError,
    InvalidInputError,
    NotFoundError,
    ValidationError,
)
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.models.general.component import Component
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.component_repository import ComponentRepository
from src.core.schemas.base import PaginationSchema
from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchResponseSchema,
    ComponentAdvancedSearchSchema,
    ComponentBulkCreateSchema,
    ComponentBulkUpdateSchema,
    ComponentCreateSchema,
    ComponentPaginatedResponseSchema,
    ComponentReadSchema,
    ComponentSearchResultSchema,
    ComponentSearchSchema,
    ComponentSpecificationSchema,
    ComponentStatsSchema,
    ComponentSummarySchema,
    ComponentUpdateSchema,
    ComponentValidationResultSchema,
)
from src.core.utils.datetime_utils import utcnow_aware
from src.core.utils.pagination_utils import PaginationParams, PaginationResult
from src.core.utils.string_utils import sanitize_text


class ComponentService:
    """Service for component management operations and business logic.

    This service provides high-level business logic for electrical component
    catalog management, including validation, search, and lifecycle operations.
    """

    def __init__(self, component_repo: ComponentRepository):
        """Initialize the component service.

        Args:
            component_repo: Component repository instance
        """
        self.component_repo = component_repo
        self.db_session = component_repo.db_session
        logger.debug("ComponentService initialized")

    # ============================================================================
    # COMPONENT CRUD OPERATIONS
    # ============================================================================

    @handle_service_errors("create_component")
    @monitor_service_performance("create_component")
    def create_component(self, component_data: ComponentCreateSchema) -> ComponentReadSchema:
        """Create a new component with comprehensive validation.

        Args:
            component_data: Component creation data

        Returns:
            ComponentReadSchema: Created component

        Raises:
            ValidationError: If component data is invalid
            BusinessLogicError: If business rules are violated
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating component: {component_data.manufacturer} {component_data.model_number}")

        # Validate component data
        validation_result = self._validate_component_data(component_data)
        if not validation_result.is_valid:
            raise ValidationError(
                f"Component validation failed: {', '.join(validation_result.errors)}"
            )

        # Check for duplicate components
        if self._check_duplicate_component(component_data.manufacturer, component_data.model_number):
            raise BusinessLogicError(
                f"Component already exists: {component_data.manufacturer} {component_data.model_number}"
            )

        # Process component data
        component_dict = component_data.model_dump()
        logger.debug(f"Component data after model_dump: {list(component_dict.keys())}")

        # Handle transition from enum to relational fields
        component_dict = self._handle_component_classification_transition(component_dict)

        # Validate foreign key references
        self._validate_foreign_key_references(component_dict)

        # Process specifications, dimensions, and metadata
        component_dict = self._process_component_specifications(component_dict)
        component_dict = self._process_component_dimensions(component_dict)
        component_dict = self._process_component_metadata(component_dict)

        # Convert string enum values to enum instances
        component_dict = self._convert_enums_to_instances(component_dict)

        # Sanitize text fields
        component_dict = self._sanitize_component_data(component_dict)

        # Remove None values and invalid fields before repository create
        component_dict = self._clean_component_data_for_repository(component_dict)

        logger.debug(f"Component data before repository create: {list(component_dict.keys())}")
        logger.debug(f"Name field value: {component_dict.get('name')}")
        logger.debug(f"Full component_dict: {component_dict}")

        try:
            # Create component
            component = self.component_repo.create(component_dict)
            self.db_session.commit()
            self.db_session.refresh(component)

            logger.info(f"Component created successfully: {component.id}")
            return self._convert_to_read_schema(component)

        except IntegrityError as e:
            self.db_session.rollback()
            logger.error(f"Database integrity error creating component: {e}")
            raise BusinessLogicError("Component creation failed due to data integrity constraints")

    @handle_service_errors("get_component")
    @monitor_service_performance("get_component")
    def get_component(self, component_id: int) -> ComponentReadSchema:
        """Get component by ID.

        Args:
            component_id: Component ID

        Returns:
            ComponentReadSchema: Component data

        Raises:
            NotFoundError: If component not found
        """
        logger.debug(f"Retrieving component: {component_id}")

        component = self.component_repo.get_by_id(component_id)
        if not component or component.is_deleted:
            raise NotFoundError(
                code="COMPONENT_NOT_FOUND",
                detail=f"Component {component_id} not found"
            )

        return self._convert_to_read_schema(component)

    @handle_service_errors("update_component")
    @monitor_service_performance("update_component")
    def update_component(
        self, component_id: int, update_data: ComponentUpdateSchema
    ) -> ComponentReadSchema:
        """Update component with validation and business rules.

        Args:
            component_id: Component ID
            update_data: Update data

        Returns:
            ComponentReadSchema: Updated component

        Raises:
            NotFoundError: If component not found
            ValidationError: If update data is invalid
            BusinessLogicError: If business rules are violated
        """
        logger.info(f"Updating component: {component_id}")

        # Validate component exists
        existing_component = self.component_repo.get_by_id(component_id)
        if not existing_component or existing_component.is_deleted:
            raise NotFoundError(
                code="COMPONENT_NOT_FOUND",
                detail=f"Component {component_id} not found"
            )

        # Validate update data
        if update_data.model_dump(exclude_unset=True):
            validation_result = self._validate_component_update(existing_component, update_data)
            if not validation_result.is_valid:
                raise ValidationError(
                    f"Component update validation failed: {', '.join(validation_result.errors)}"
                )

        # Check for duplicate if manufacturer/model is being updated
        update_dict = update_data.model_dump(exclude_unset=True)
        if "manufacturer" in update_dict or "model_number" in update_dict:
            new_manufacturer = update_dict.get("manufacturer", existing_component.manufacturer)
            new_model = update_dict.get("model_number", existing_component.model_number)
            
            if self._check_duplicate_component(new_manufacturer, new_model, exclude_id=component_id):
                raise BusinessLogicError(
                    f"Component already exists: {new_manufacturer} {new_model}"
                )

        # Process specifications, dimensions, and metadata if provided
        if "specifications" in update_dict:
            update_dict = self._process_component_specifications(update_dict)
        if "dimensions" in update_dict:
            update_dict = self._process_component_dimensions(update_dict)
        if "metadata" in update_dict:
            update_dict = self._process_component_metadata(update_dict)

        # Sanitize text fields
        update_dict = self._sanitize_component_data(update_dict)

        # Auto-assign category if component type is being updated
        if "component_type" in update_dict and "category" not in update_dict:
            update_dict["category"] = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(
                update_dict["component_type"]
            )

        try:
            # Update component
            updated_component = self.component_repo.update(component_id, update_dict)
            self.db_session.commit()

            logger.info(f"Component updated successfully: {component_id}")
            return self._convert_to_read_schema(updated_component)

        except IntegrityError as e:
            self.db_session.rollback()
            logger.error(f"Database integrity error updating component: {e}")
            raise BusinessLogicError("Component update failed due to data integrity constraints")

    @handle_service_errors("delete_component")
    @monitor_service_performance("delete_component")
    def delete_component(self, component_id: int, deleted_by_user_id: Optional[int] = None) -> bool:
        """Soft delete a component with dependency checking.

        Args:
            component_id: Component ID
            deleted_by_user_id: ID of user performing deletion

        Returns:
            bool: True if deleted successfully

        Raises:
            NotFoundError: If component not found
            BusinessLogicError: If component has dependencies
        """
        logger.info(f"Deleting component: {component_id}")

        # Validate component exists
        component = self.component_repo.get_by_id(component_id)
        if not component or component.is_deleted:
            raise NotFoundError(
                code="COMPONENT_NOT_FOUND",
                detail=f"Component {component_id} not found"
            )

        # Check for dependencies (this would be expanded based on actual relationships)
        if self._check_component_dependencies(component_id):
            raise BusinessLogicError(
                f"Cannot delete component {component_id}: component is referenced by other entities"
            )

        # Perform soft delete
        success = self.component_repo.soft_delete_component(component_id)
        if success:
            self.db_session.commit()
            logger.info(f"Component deleted successfully: {component_id}")
        else:
            raise NotFoundError(
                code="COMPONENT_NOT_FOUND",
                detail=f"Component {component_id} not found for deletion"
            )

        return success

    # ============================================================================
    # COMPONENT SEARCH AND FILTERING
    # ============================================================================

    @handle_service_errors("search_components")
    @monitor_service_performance("search_components")
    def search_components(
        self,
        search_params: ComponentSearchSchema,
        pagination_params: PaginationParams,
    ) -> ComponentPaginatedResponseSchema:
        """Search components with advanced filtering and pagination.

        Args:
            search_params: Search and filter parameters
            pagination_params: Pagination parameters

        Returns:
            ComponentPaginatedResponseSchema: Paginated search results
        """
        logger.debug(f"Searching components with params: {search_params.model_dump()}")

        # Build filters from search parameters
        filters = self._build_search_filters(search_params)

        # Perform paginated search
        result = self.component_repo.get_components_paginated_with_filters(
            pagination_params=pagination_params,
            category=search_params.category,
            component_type=search_params.component_type,
            manufacturer=search_params.manufacturer,
            is_preferred=search_params.is_preferred,
            search_term=search_params.search_term,
        )

        # Convert to schemas
        component_schemas = [self._convert_to_read_schema(c) for c in result.items]

        # Create pagination information
        pagination = PaginationSchema(
            page=result.page,
            size=result.per_page,
            total=result.total,
            pages=result.total_pages,
        )

        response = ComponentPaginatedResponseSchema(
            items=component_schemas,
            pagination=pagination
        )

        logger.debug(f"Found {len(component_schemas)} components (total: {result.total})")
        return response

    @handle_service_errors("search_components_advanced")
    @monitor_service_performance("search_components_advanced")
    def search_components_advanced(
        self,
        search_filters: Dict[str, Any],
        specification_filters: Optional[Dict[str, Any]] = None,
        price_range: Optional[Dict[str, float]] = None,
        pagination_params: Optional[PaginationParams] = None,
    ) -> ComponentPaginatedResponseSchema:
        """Advanced component search with specification-based filtering.

        Args:
            search_filters: Basic search filters (category, type, manufacturer, etc.)
            specification_filters: Technical specification filters
            price_range: Price range filters with min/max values
            pagination_params: Pagination parameters

        Returns:
            ComponentPaginatedResponseSchema: Paginated search results
        """
        logger.debug(f"Advanced component search with specification filters: {specification_filters}")

        # Set default pagination if not provided
        if pagination_params is None:
            pagination_params = PaginationParams(page=1, per_page=20)

        # Calculate skip and limit from pagination
        skip = (pagination_params.page - 1) * pagination_params.per_page
        limit = pagination_params.per_page

        # Perform advanced search
        components = self.component_repo.search_components_advanced(
            search_filters=search_filters,
            specification_filters=specification_filters,
            price_range=price_range,
            skip=skip,
            limit=limit
        )

        # Get total count for pagination (simplified - would need separate count query)
        total_count = len(components)  # This is approximate, would need proper count query

        # Convert to schemas
        component_schemas = [self._convert_to_read_schema(c) for c in components]

        # Create pagination information
        pagination = PaginationSchema(
            page=pagination_params.page,
            size=pagination_params.per_page,
            total=total_count,
            pages=(total_count + pagination_params.per_page - 1) // pagination_params.per_page,
        )

        response = ComponentPaginatedResponseSchema(
            items=component_schemas,
            pagination=pagination
        )

        logger.debug(f"Advanced search found {len(component_schemas)} components")
        return response

    @handle_service_errors("search_by_specifications")
    @monitor_service_performance("search_by_specifications")
    def search_by_specifications(
        self,
        specifications: Dict[str, Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[ComponentReadSchema]:
        """Search components by technical specifications.

        Args:
            specifications: Dictionary of specification criteria
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ComponentReadSchema]: List of matching components
        """
        logger.debug(f"Searching components by specifications: {specifications}")

        # Validate specification filters
        validated_specs = self._validate_specification_filters(specifications)

        # Perform specification-based search
        components = self.component_repo.get_components_by_specifications(
            specifications=validated_specs,
            skip=skip,
            limit=limit
        )

        # Convert to schemas
        component_schemas = [self._convert_to_read_schema(c) for c in components]

        logger.debug(f"Specification search found {len(component_schemas)} components")
        return component_schemas

    @handle_service_errors("get_components_by_category")
    @monitor_service_performance("get_components_by_category")
    def get_components_by_category(
        self, category: ComponentCategoryType, skip: int = 0, limit: int = 100
    ) -> List[ComponentSummarySchema]:
        """Get components by category.

        Args:
            category: Component category
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ComponentSummarySchema]: List of components in category
        """
        logger.debug(f"Retrieving components by category: {category}")

        components = self.component_repo.get_by_category(category, skip, limit)
        return [ComponentSummarySchema.model_validate(c) for c in components]

    @handle_service_errors("get_components_by_type")
    @monitor_service_performance("get_components_by_type")
    def get_components_by_type(
        self, component_type: ComponentType, skip: int = 0, limit: int = 100
    ) -> List[ComponentSummarySchema]:
        """Get components by type.

        Args:
            component_type: Component type
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ComponentSummarySchema]: List of components of specified type
        """
        logger.debug(f"Retrieving components by type: {component_type}")

        components = self.component_repo.get_by_type(component_type, skip, limit)
        return [ComponentSummarySchema.model_validate(c) for c in components]

    @handle_service_errors("get_preferred_components")
    @monitor_service_performance("get_preferred_components")
    def get_preferred_components(
        self, skip: int = 0, limit: int = 100
    ) -> List[ComponentSummarySchema]:
        """Get preferred components.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[ComponentSummarySchema]: List of preferred components
        """
        logger.debug("Retrieving preferred components")

        components = self.component_repo.get_preferred_components(skip, limit)
        return [ComponentSummarySchema.model_validate(c) for c in components]

    @handle_service_errors("search_components_with_builder")
    @monitor_service_performance("search_components_with_builder")
    def search_components_with_builder(
        self,
        search_schema: ComponentAdvancedSearchSchema,
        pagination_params: PaginationParams,
    ) -> ComponentAdvancedSearchResponseSchema:
        """Advanced component search using the query builder.

        Args:
            search_schema: Advanced search parameters
            pagination_params: Pagination parameters

        Returns:
            ComponentAdvancedSearchResponseSchema: Advanced search results with metadata
        """
        logger.debug(f"Advanced search with builder: {search_schema.model_dump()}")

        # Perform search using repository
        components, total_count = self.component_repo.search_components_with_builder(
            search_schema, pagination_params
        )

        # Convert to search result schemas with relevance scoring
        search_results = []
        for component in components:
            component_schema = self._convert_to_read_schema(component)

            # Calculate relevance score if text search was performed
            relevance_score = None
            matched_fields = []

            if search_schema.search_term:
                relevance_score = self._calculate_relevance_score(
                    component, search_schema.search_term, search_schema.search_fields
                )
                matched_fields = self._get_matched_fields(
                    component, search_schema.search_term, search_schema.search_fields
                )

            search_result = ComponentSearchResultSchema(
                component=component_schema,
                relevance_score=relevance_score,
                matched_fields=matched_fields
            )
            search_results.append(search_result)

        # Build search metadata
        search_metadata = {
            "query_time": "< 1ms",  # Would be calculated in real implementation
            "total_filters_applied": self._count_applied_filters(search_schema),
            "search_type": "advanced_builder",
            "fuzzy_search_enabled": search_schema.fuzzy_search,
        }

        # Get search suggestions if needed
        suggestions = []
        if search_schema.search_term and len(search_results) < 5:
            suggestions = self._get_search_suggestions(search_schema.search_term)

        # Build pagination info
        pagination = PaginationSchema(
            page=pagination_params.page,
            per_page=pagination_params.per_page,
            total=total_count,
            pages=(total_count + pagination_params.per_page - 1) // pagination_params.per_page
        )

        response = ComponentAdvancedSearchResponseSchema(
            items=search_results,
            pagination=pagination,
            search_metadata=search_metadata,
            suggestions=suggestions
        )

        logger.debug(f"Advanced search completed: {len(search_results)} results")
        return response

    @handle_service_errors("search_components_with_relevance")
    @monitor_service_performance("search_components_with_relevance")
    def search_components_with_relevance(
        self,
        search_term: str,
        search_fields: Optional[List[str]] = None,
        fuzzy: bool = False,
        pagination_params: Optional[PaginationParams] = None,
    ) -> ComponentAdvancedSearchResponseSchema:
        """Search components with relevance scoring.

        Args:
            search_term: Text to search for
            search_fields: Fields to search in
            fuzzy: Whether to use fuzzy matching
            pagination_params: Pagination parameters

        Returns:
            ComponentAdvancedSearchResponseSchema: Search results with relevance scores
        """
        logger.debug(f"Relevance search for: {search_term}")

        if pagination_params is None:
            pagination_params = PaginationParams(page=1, per_page=20)

        skip = (pagination_params.page - 1) * pagination_params.per_page
        limit = pagination_params.per_page

        # Perform relevance search
        component_relevance_pairs = self.component_repo.search_components_with_relevance(
            search_term, search_fields, fuzzy, skip, limit
        )

        # Convert to search result schemas
        search_results = []
        for component, relevance_score in component_relevance_pairs:
            component_schema = self._convert_to_read_schema(component)
            matched_fields = self._get_matched_fields(component, search_term, search_fields)

            search_result = ComponentSearchResultSchema(
                component=component_schema,
                relevance_score=relevance_score,
                matched_fields=matched_fields
            )
            search_results.append(search_result)

        # Get total count for pagination (simplified - would need separate query)
        total_count = len(search_results)  # This is approximate

        # Build response
        pagination = PaginationSchema(
            page=pagination_params.page,
            per_page=pagination_params.per_page,
            total=total_count,
            pages=(total_count + pagination_params.per_page - 1) // pagination_params.per_page
        )

        search_metadata = {
            "query_time": "< 1ms",
            "search_type": "relevance_scored",
            "fuzzy_search_enabled": fuzzy,
        }

        response = ComponentAdvancedSearchResponseSchema(
            items=search_results,
            pagination=pagination,
            search_metadata=search_metadata,
            suggestions=[]
        )

        logger.debug(f"Relevance search completed: {len(search_results)} results")
        return response

    @handle_service_errors("get_search_suggestions")
    @monitor_service_performance("get_search_suggestions")
    def get_search_suggestions(
        self,
        query: str,
        field: str = "name",
        limit: int = 10
    ) -> List[str]:
        """Get search suggestions for autocomplete.

        Args:
            query: Partial search query
            field: Field to get suggestions from
            limit: Maximum number of suggestions

        Returns:
            List of suggestion strings
        """
        logger.debug(f"Getting search suggestions for: {query}")

        return self.component_repo.get_search_suggestions(query, field, limit)

    # ============================================================================
    # BULK OPERATIONS
    # ============================================================================

    @handle_service_errors("bulk_create_components")
    @monitor_service_performance("bulk_create_components")
    def bulk_create_components(
        self, bulk_data: ComponentBulkCreateSchema
    ) -> Tuple[List[ComponentReadSchema], List[ComponentValidationResultSchema]]:
        """Create multiple components in bulk with validation.

        Args:
            bulk_data: Bulk creation data

        Returns:
            Tuple[List[ComponentReadSchema], List[ComponentValidationResultSchema]]:
                Created components and validation results
        """
        logger.info(f"Bulk creating {len(bulk_data.components)} components")

        created_components = []
        validation_results = []

        try:
            for i, component_data in enumerate(bulk_data.components):
                try:
                    # Validate component
                    validation_result = self._validate_component_data(component_data)
                    validation_results.append(validation_result)

                    if not validation_result.is_valid:
                        if not bulk_data.skip_invalid:
                            raise ValidationError(
                                f"Component {i+1} validation failed: {', '.join(validation_result.errors)}"
                            )
                        continue

                    # Check for duplicates if enabled
                    if bulk_data.validate_duplicates:
                        if self._check_duplicate_component(
                            component_data.manufacturer, component_data.model_number
                        ):
                            validation_result.errors.append("Duplicate component")
                            validation_result.is_valid = False
                            if not bulk_data.skip_invalid:
                                raise BusinessLogicError(
                                    f"Duplicate component: {component_data.manufacturer} {component_data.model_number}"
                                )
                            continue

                    # Create component
                    created_component = self.create_component(component_data)
                    created_components.append(created_component)

                except Exception as e:
                    logger.error(f"Error creating component {i+1}: {e}")
                    if not bulk_data.skip_invalid:
                        self.db_session.rollback()
                        raise
                    
                    # Add error to validation results
                    validation_result = ComponentValidationResultSchema(
                        is_valid=False,
                        errors=[str(e)],
                        warnings=[],
                        component_id=None
                    )
                    validation_results.append(validation_result)

            self.db_session.commit()
            logger.info(f"Bulk creation completed: {len(created_components)} components created")

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Bulk creation failed: {e}")
            raise

        return created_components, validation_results

    @handle_service_errors("bulk_update_components")
    @monitor_service_performance("bulk_update_components")
    def bulk_update_components(
        self, bulk_data: ComponentBulkUpdateSchema
    ) -> Tuple[List[ComponentReadSchema], List[ComponentValidationResultSchema]]:
        """Update multiple components in bulk.

        Args:
            bulk_data: Bulk update data

        Returns:
            Tuple[List[ComponentReadSchema], List[ComponentValidationResultSchema]]:
                Updated components and validation results
        """
        logger.info(f"Bulk updating {len(bulk_data.component_ids)} components")

        updated_components = []
        validation_results = []

        try:
            for component_id in bulk_data.component_ids:
                try:
                    # Update component
                    updated_component = self.update_component(component_id, bulk_data.update_data)
                    updated_components.append(updated_component)

                    # Add success validation result
                    validation_result = ComponentValidationResultSchema(
                        is_valid=True,
                        errors=[],
                        warnings=[],
                        component_id=component_id
                    )
                    validation_results.append(validation_result)

                except NotFoundError as e:
                    logger.warning(f"Component {component_id} not found for bulk update")
                    if not bulk_data.skip_missing:
                        self.db_session.rollback()
                        raise

                    # Add error to validation results
                    validation_result = ComponentValidationResultSchema(
                        is_valid=False,
                        errors=[str(e)],
                        warnings=[],
                        component_id=component_id
                    )
                    validation_results.append(validation_result)

                except Exception as e:
                    logger.error(f"Error updating component {component_id}: {e}")
                    self.db_session.rollback()
                    raise

            self.db_session.commit()
            logger.info(f"Bulk update completed: {len(updated_components)} components updated")

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Bulk update failed: {e}")
            raise

        return updated_components, validation_results

    @handle_service_errors("export_components")
    @monitor_service_performance("export_components")
    def export_components(
        self,
        component_ids: Optional[List[int]] = None,
        filters: Optional[Dict[str, Any]] = None,
        export_format: str = "json"
    ) -> Dict[str, Any]:
        """Export components to various formats.

        Args:
            component_ids: Specific component IDs to export (optional)
            filters: Search filters to apply (optional)
            export_format: Export format (json, csv, xlsx)

        Returns:
            Dict[str, Any]: Export data with format and content
        """
        logger.info(f"Exporting components in {export_format} format")

        # Get components to export
        if component_ids:
            components = []
            for component_id in component_ids:
                try:
                    component = self.component_repo.get_by_id(component_id)
                    if component and not component.is_deleted:
                        components.append(component)
                except NotFoundError:
                    logger.warning(f"Component {component_id} not found for export")
        else:
            # Use filters or get all components using search with empty term
            if filters:
                # Use advanced search with filters
                components = self.component_repo.search_components_advanced(
                    search_filters=filters,
                    specification_filters=None,
                    price_range=None,
                    skip=0,
                    limit=10000  # Large limit to get all components
                )
            else:
                # Get all active components using pagination with large limit
                pagination_params = PaginationParams(page=1, per_page=10000)
                result = self.component_repo.get_paginated(
                    pagination_params=pagination_params,
                    filters={"is_active": True, "is_deleted": False}
                )
                components = result.items

        # Convert to schemas
        component_schemas = [self._convert_to_read_schema(c) for c in components]

        # Export based on format
        if export_format.lower() == "json":
            return self._export_to_json(component_schemas)
        elif export_format.lower() == "csv":
            return self._export_to_csv(component_schemas)
        elif export_format.lower() == "xlsx":
            return self._export_to_xlsx(component_schemas)
        else:
            raise InvalidInputError(f"Unsupported export format: {export_format}")

    @handle_service_errors("import_components")
    @monitor_service_performance("import_components")
    def import_components(
        self,
        import_data: Dict[str, Any],
        import_format: str = "json",
        skip_invalid: bool = True,
        validate_duplicates: bool = True
    ) -> Tuple[List[ComponentReadSchema], List[ComponentValidationResultSchema]]:
        """Import components from various formats.

        Args:
            import_data: Import data content
            import_format: Import format (json, csv, xlsx)
            skip_invalid: Skip invalid components during import
            validate_duplicates: Check for duplicate components

        Returns:
            Tuple[List[ComponentReadSchema], List[ComponentValidationResultSchema]]:
                Imported components and validation results
        """
        logger.info(f"Importing components from {import_format} format")

        # Parse import data based on format
        if import_format.lower() == "json":
            component_data_list = self._parse_json_import(import_data)
        elif import_format.lower() == "csv":
            component_data_list = self._parse_csv_import(import_data)
        elif import_format.lower() == "xlsx":
            component_data_list = self._parse_xlsx_import(import_data)
        else:
            raise InvalidInputError(f"Unsupported import format: {import_format}")

        # Create bulk data schema
        bulk_data = ComponentBulkCreateSchema(
            components=component_data_list,
            skip_invalid=skip_invalid,
            validate_duplicates=validate_duplicates
        )

        # Use bulk create functionality
        return self.bulk_create_components(bulk_data)

    # ============================================================================
    # COMPONENT STATISTICS AND ANALYTICS
    # ============================================================================

    @handle_service_errors("get_component_stats")
    @monitor_service_performance("get_component_stats")
    def get_component_stats(self) -> ComponentStatsSchema:
        """Get component statistics and analytics.

        Returns:
            ComponentStatsSchema: Component statistics
        """
        logger.debug("Retrieving component statistics")

        # Get basic counts
        total_components = self.component_repo.count_active_components()
        preferred_count = len(self.component_repo.get_preferred_components(limit=10000))
        
        # Get category counts
        category_counts = self.component_repo.count_components_by_category()
        category_counts_str = {cat.value: count for cat, count in category_counts.items()}

        # Get manufacturer counts (simplified - would need additional repository method)
        manufacturer_counts: Dict[str, int] = {}  # This would be implemented with additional repository method

        # Calculate price statistics (simplified)
        average_price = None
        price_range = None

        stats = ComponentStatsSchema(
            total_components=total_components,
            active_components=total_components,  # Since we only count active components
            preferred_components=preferred_count,
            components_by_category=category_counts_str,
            components_by_manufacturer=manufacturer_counts,
            average_price=average_price,
            price_range=price_range
        )

        logger.debug(f"Component statistics: {total_components} total, {preferred_count} preferred")
        return stats

    @handle_service_errors("bulk_create_with_validation")
    @monitor_service_performance("bulk_create_with_validation")
    def bulk_create_with_validation(
        self,
        components_data: List[Dict[str, Any]],
        validate_duplicates: bool = True,
        batch_size: int = 100
    ) -> Dict[str, Any]:
        """Create multiple components with comprehensive validation.

        Args:
            components_data: List of component data dictionaries
            validate_duplicates: Whether to check for duplicates
            batch_size: Number of components to process in each batch

        Returns:
            Dict with creation results and statistics
        """
        logger.info(f"Bulk creating {len(components_data)} components with validation")

        try:
            # Use repository method for bulk creation with validation
            created_components, validation_errors = self.component_repo.bulk_create_with_validation(
                components_data, validate_duplicates, batch_size
            )

            # Commit the transaction
            self.db_session.commit()

            # Convert to schemas
            created_schemas = [self._convert_to_read_schema(c) for c in created_components]

            # Build result summary
            result = {
                'total_processed': len(components_data),
                'created': len(created_schemas),
                'errors': len(validation_errors),
                'success_rate': len(created_schemas) / len(components_data) if components_data else 0,
                'created_components': created_schemas,
                'validation_errors': validation_errors
            }

            logger.info(f"Bulk creation completed: {result['created']} created, {result['errors']} errors")
            return result

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Bulk creation failed: {e}")
            raise BusinessLogicError(f"Bulk component creation failed: {str(e)}")

    @handle_service_errors("bulk_update_selective")
    @monitor_service_performance("bulk_update_selective")
    def bulk_update_selective(
        self,
        updates: List[Dict[str, Any]],
        batch_size: int = 100
    ) -> Dict[str, Any]:
        """Update multiple components with different data for each.

        Args:
            updates: List of update dictionaries with 'id' and update fields
            batch_size: Number of updates to process in each batch

        Returns:
            Dict with update results and statistics
        """
        logger.info(f"Bulk updating {len(updates)} components selectively")

        try:
            # Use repository method for selective bulk update
            updated_count, validation_errors = self.component_repo.bulk_update_selective(
                updates, batch_size
            )

            # Commit the transaction
            self.db_session.commit()

            # Build result summary
            result = {
                'total_processed': len(updates),
                'updated': updated_count,
                'errors': len(validation_errors),
                'success_rate': updated_count / len(updates) if updates else 0,
                'validation_errors': validation_errors
            }

            logger.info(f"Bulk update completed: {result['updated']} updated, {result['errors']} errors")
            return result

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Bulk update failed: {e}")
            raise BusinessLogicError(f"Bulk component update failed: {str(e)}")

    @handle_service_errors("bulk_delete_components")
    @monitor_service_performance("bulk_delete_components")
    def bulk_delete_components(
        self,
        component_ids: List[int],
        soft_delete: bool = True,
        deleted_by_user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Delete multiple components in bulk.

        Args:
            component_ids: List of component IDs to delete
            soft_delete: Whether to perform soft delete (default) or hard delete
            deleted_by_user_id: ID of user performing deletion

        Returns:
            Dict with deletion results and statistics
        """
        logger.info(f"Bulk deleting {len(component_ids)} components (soft={soft_delete})")

        try:
            # Use repository method for bulk delete
            deleted_count, not_found_ids = self.component_repo.bulk_delete_components(
                component_ids, soft_delete, deleted_by_user_id
            )

            # Commit the transaction
            self.db_session.commit()

            # Build result summary
            result = {
                'total_processed': len(component_ids),
                'deleted': deleted_count,
                'not_found': len(not_found_ids),
                'success_rate': deleted_count / len(component_ids) if component_ids else 0,
                'not_found_ids': not_found_ids,
                'delete_type': 'soft' if soft_delete else 'hard'
            }

            logger.info(f"Bulk deletion completed: {result['deleted']} deleted, {result['not_found']} not found")
            return result

        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Bulk deletion failed: {e}")
            raise BusinessLogicError(f"Bulk component deletion failed: {str(e)}")

    @handle_service_errors("enhanced_export_components")
    @monitor_service_performance("enhanced_export_components")
    def enhanced_export_components(
        self,
        component_ids: Optional[List[int]] = None,
        filters: Optional[Dict[str, Any]] = None,
        format_type: str = "json",
        include_inactive: bool = False,
        custom_fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Enhanced component export with flexible filtering and formatting.

        Args:
            component_ids: Specific component IDs to export
            filters: Additional filters to apply
            format_type: Export format (json, csv, xlsx)
            include_inactive: Whether to include inactive components
            custom_fields: Specific fields to include in export

        Returns:
            Dict with export data and metadata
        """
        logger.info(f"Enhanced export to {format_type} format")

        try:
            # Use repository method for export
            export_data = self.component_repo.export_components_to_format(
                component_ids, filters, format_type, include_inactive
            )

            # Filter fields if custom_fields specified
            if custom_fields:
                filtered_data = []
                for item in export_data:
                    filtered_item = {field: item.get(field) for field in custom_fields if field in item}
                    filtered_data.append(filtered_item)
                export_data = filtered_data

            # Build export metadata
            metadata = {
                'export_format': format_type,
                'total_components': len(export_data),
                'include_inactive': include_inactive,
                'custom_fields': custom_fields,
                'exported_at': utcnow_aware().isoformat(),
                'filters_applied': filters or {}
            }

            result = {
                'data': export_data,
                'metadata': metadata
            }

            logger.info(f"Enhanced export completed: {len(export_data)} components")
            return result

        except Exception as e:
            logger.error(f"Enhanced export failed: {e}")
            raise BusinessLogicError(f"Component export failed: {str(e)}")

    # ============================================================================
    # COMPONENT VALIDATION AND BUSINESS RULES
    # ============================================================================

    def _validate_component_data(self, component_data: ComponentCreateSchema) -> ComponentValidationResultSchema:
        """Validate component data against business rules.

        Args:
            component_data: Component data to validate

        Returns:
            ComponentValidationResultSchema: Validation result
        """
        errors = []
        warnings = []

        # Validate component type and category consistency
        if component_data.category:
            # Convert string values back to enums for lookup
            component_type_enum = component_data.component_type
            if isinstance(component_type_enum, str):
                # Find the enum by value
                try:
                    component_type_enum = next(
                        ct for ct in ComponentType if ct.value == component_data.component_type
                    )
                except StopIteration:
                    pass  # Keep the original string value
            
            category_enum = component_data.category
            if isinstance(category_enum, str):
                # Find the enum by value
                try:
                    category_enum = next(
                        cat for cat in ComponentCategoryType if cat.value == component_data.category
                    )
                except StopIteration:
                    pass  # Keep the original string value
            
            if component_type_enum:
                expected_category = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(component_type_enum)
                if expected_category and expected_category != category_enum:
                    errors.append(
                        f"Category {component_data.category} does not match expected category "
                        f"{expected_category.value} for component type {component_data.component_type}"
                    )

        # Validate specifications based on component type
        if component_data.specifications:
            # Convert ComponentSpecificationSchema to dict if needed
            specs = component_data.specifications
            if isinstance(specs, ComponentSpecificationSchema):
                specs = specs.model_dump(exclude_none=True)
            
            # Only validate if component_type is provided (legacy validation)
            if component_data.component_type:
                spec_validation = self._validate_component_specifications(
                    component_data.component_type, specs
                )
                errors.extend(spec_validation.get("errors", []))
                warnings.extend(spec_validation.get("warnings", []))

        # Validate pricing information
        if component_data.unit_price and component_data.unit_price <= 0:
            errors.append("Unit price must be greater than zero")

        # Validate part number format (basic validation)
        if component_data.part_number and len(component_data.part_number.strip()) == 0:
            warnings.append("Part number is empty")

        return ComponentValidationResultSchema(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            component_id=None
        )

    def _validate_component_update(
        self, existing_component: Component, update_data: ComponentUpdateSchema
    ) -> ComponentValidationResultSchema:
        """Validate component update data.

        Args:
            existing_component: Existing component
            update_data: Update data

        Returns:
            ComponentValidationResultSchema: Validation result
        """
        errors = []
        warnings = []

        update_dict = update_data.model_dump(exclude_unset=True)

        # Validate component type and category consistency if being updated
        if "component_type" in update_dict or "category" in update_dict:
            new_type = update_dict.get("component_type", existing_component.component_type)
            new_category = update_dict.get("category", existing_component.category)
            
            # Convert string values back to enums for lookup
            new_type_enum = new_type
            if isinstance(new_type_enum, str):
                try:
                    new_type_enum = next(
                        ct for ct in ComponentType if ct.value == new_type
                    )
                except StopIteration:
                    new_type_enum = None
            
            new_category_enum = new_category
            if isinstance(new_category_enum, str):
                try:
                    new_category_enum = next(
                        cat for cat in ComponentCategoryType if cat.value == new_category
                    )
                except StopIteration:
                    new_category_enum = None
            
            if new_type_enum:
                expected_category = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(new_type_enum)
                if expected_category and expected_category != new_category_enum:
                    errors.append(
                        f"Category {new_category} does not match expected category "
                        f"{expected_category.value} for component type {new_type}"
                    )

        # Validate specifications if being updated
        if "specifications" in update_dict:
            component_type = update_dict.get("component_type", existing_component.component_type)
            spec_validation = self._validate_component_specifications(
                component_type, update_dict["specifications"]
            )
            errors.extend(spec_validation.get("errors", []))
            warnings.extend(spec_validation.get("warnings", []))

        return ComponentValidationResultSchema(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            component_id=None
        )

    def _validate_component_specifications(
        self, component_type: ComponentType, specifications: Union[Dict[str, Any], str]
    ) -> Dict[str, List[str]]:
        """Validate component specifications based on type.

        Args:
            component_type: Component type
            specifications: Specifications to validate

        Returns:
            Dict[str, List[str]]: Validation errors and warnings
        """
        errors = []
        warnings = []

        try:
            if isinstance(specifications, str):
                specs = json.loads(specifications)
            else:
                specs = specifications

            # Type-specific validation
            if component_type in [ComponentType.CIRCUIT_BREAKER, ComponentType.FUSE]:
                electrical = specs.get("electrical", {})
                if not electrical.get("current_rating") and not electrical.get("voltage_rating"):
                    warnings.append("Protection devices should specify current or voltage rating")

            elif component_type == ComponentType.POWER_CABLE:
                electrical = specs.get("electrical", {})
                if not electrical.get("conductor_material"):
                    warnings.append("Power cables should specify conductor material")
                if not electrical.get("insulation_type"):
                    warnings.append("Power cables should specify insulation type")

            # Validate standards compliance format
            standards = specs.get("standards_compliance", [])
            if standards:
                valid_prefixes = ['IEC', 'IEEE', 'EN', 'UL', 'NEMA', 'ANSI', 'ISO', 'ASTM']
                invalid_standards = [
                    f"Standard '{standard}' may not follow recognized format"
                    for standard in standards
                    if not any(standard.upper().startswith(prefix) for prefix in valid_prefixes)
                ]
                warnings.extend(invalid_standards)

        except (json.JSONDecodeError, TypeError) as e:
            errors.append(f"Invalid specifications format: {e}")

        return {"errors": errors, "warnings": warnings}

    def _validate_specification_filters(self, specifications: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize specification filters for search.

        Args:
            specifications: Specification filters to validate

        Returns:
            Dict[str, Any]: Validated and normalized specification filters
        """
        validated_specs: Dict[str, Any] = {}
        
        for key, value in specifications.items():
            # Normalize key format (convert to lowercase, replace spaces with underscores)
            normalized_key = key.lower().replace(' ', '_').replace('-', '_')
            
            # Validate value types and ranges
            if isinstance(value, dict):
                # Handle range queries (e.g., {"min": 10, "max": 100})
                if "min" in value or "max" in value:
                    validated_specs[normalized_key] = value
                else:
                    # Handle nested specification objects
                    validated_specs[normalized_key] = self._validate_specification_filters(value)
            elif isinstance(value, (str, int, float, bool)):
                validated_specs[normalized_key] = value
            elif isinstance(value, list):
                # Handle array values (e.g., standards compliance)
                validated_specs[normalized_key] = value
            else:
                logger.warning(f"Unsupported specification filter type for {key}: {type(value)}")
        
        return validated_specs

    def _check_duplicate_component(
        self, manufacturer: str, model_number: str, exclude_id: Optional[int] = None
    ) -> bool:
        """Check if component with same manufacturer and model already exists.

        Args:
            manufacturer: Manufacturer name
            model_number: Model number
            exclude_id: Component ID to exclude from check

        Returns:
            bool: True if duplicate exists
        """
        # This would use a repository method to check for duplicates
        # For now, simplified implementation
        return False

    def _check_component_dependencies(self, component_id: int) -> bool:
        """Check if component has dependencies that prevent deletion.

        Args:
            component_id: Component ID

        Returns:
            bool: True if component has dependencies
        """
        # This would check for references in projects, BOMs, etc.
        # For now, simplified implementation
        return False

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def _build_search_filters(self, search_params: ComponentSearchSchema) -> Dict[str, Any]:
        """Build search filters from search parameters.

        Args:
            search_params: Search parameters

        Returns:
            Dict[str, Any]: Filter dictionary
        """
        filters: Dict[str, Any] = {}

        if search_params.is_active is not None:
            filters["is_active"] = search_params.is_active
        if search_params.is_preferred is not None:
            filters["is_preferred"] = search_params.is_preferred
        if search_params.category:
            filters["category"] = search_params.category
        if search_params.component_type:
            filters["component_type"] = search_params.component_type
        if search_params.manufacturer:
            filters["manufacturer"] = search_params.manufacturer
        if search_params.stock_status:
            filters["stock_status"] = search_params.stock_status

        return filters

    def _process_component_specifications(self, component_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Process and normalize component specifications.

        Args:
            component_dict: Component data dictionary

        Returns:
            Dict[str, Any]: Processed component data
        """
        if "specifications" in component_dict and component_dict["specifications"]:
            specs = component_dict["specifications"]
            if isinstance(specs, dict):
                component_dict["specifications"] = json.dumps(specs)

        return component_dict

    def _process_component_dimensions(self, component_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Process and normalize component dimensions.

        Args:
            component_dict: Component data dictionary

        Returns:
            Dict[str, Any]: Processed component data
        """
        if "dimensions" in component_dict and component_dict["dimensions"]:
            dims = component_dict["dimensions"]
            if isinstance(dims, dict):
                component_dict["dimensions_json"] = json.dumps(dims)
            # Remove the original dimensions key as the model expects dimensions_json
            component_dict.pop("dimensions", None)

        return component_dict

    def _process_component_metadata(self, component_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Process and normalize component metadata.

        Args:
            component_dict: Component data dictionary

        Returns:
            Dict[str, Any]: Processed component data
        """
        if "metadata" in component_dict and component_dict["metadata"]:
            metadata = component_dict["metadata"]
            if isinstance(metadata, dict):
                component_dict["metadata_json"] = json.dumps(metadata)
            # Remove the original metadata key as the model expects metadata_json
            component_dict.pop("metadata", None)

        return component_dict

    def _validate_foreign_key_references(self, component_dict: Dict[str, Any]) -> None:
        """Validate that foreign key references exist.

        Args:
            component_dict: Component data dictionary

        Raises:
            ValidationError: If foreign key references don't exist
        """
        from src.core.models.general.component_category import ComponentCategory
        from src.core.models.general.component_type import ComponentType

        # Validate component_type_id
        if component_dict.get('component_type_id') is not None:
            type_id = component_dict['component_type_id']
            component_type = self.db_session.query(ComponentType).filter(ComponentType.id == type_id).first()
            if not component_type:
                raise ValidationError(f"ComponentType with id {type_id} does not exist")

        # Validate component_category_id
        if component_dict.get('component_category_id') is not None:
            category_id = component_dict['component_category_id']
            component_category = self.db_session.query(ComponentCategory).filter(ComponentCategory.id == category_id).first()
            if not component_category:
                raise ValidationError(f"ComponentCategory with id {category_id} does not exist")

    def _clean_component_data_for_repository(self, component_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Clean component data before passing to repository.

        Removes None values and invalid fields that shouldn't be passed to the Component model.

        Args:
            component_dict: Component data dictionary

        Returns:
            Dict[str, Any]: Cleaned component data
        """
        # List of fields that should be removed if None or invalid
        fields_to_remove_if_none = [
            'dimensions',  # This field doesn't exist in the model
            'weight_kg',   # Remove if None to avoid unnecessary kwargs
            'metadata',    # Remove if None to avoid unnecessary kwargs
        ]

        # Remove None values for specific fields
        for field in fields_to_remove_if_none:
            if field in component_dict and component_dict[field] is None:
                component_dict.pop(field, None)

        # Remove the 'dimensions' field entirely as it doesn't exist in the model
        component_dict.pop('dimensions', None)

        return component_dict

    def _handle_component_classification_transition(self, component_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Handle transition from enum-based to relational classification."""
        # If new relational fields are provided, use them
        if component_dict.get("component_type_id") or component_dict.get("component_category_id"):
            logger.debug("Using new relational classification fields")
            return component_dict

        # If legacy enum fields are provided, convert them and auto-assign category
        if component_dict.get("component_type"):
            logger.debug("Converting legacy enum fields to relational")

            # Auto-assign category if not provided
            if not component_dict.get("category"):
                component_dict["category"] = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(
                    component_dict["component_type"]
                )

        return component_dict

    def _convert_enums_to_instances(self, component_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Convert string enum values to enum instances.
        
        Args:
            component_dict: Component data dictionary
            
        Returns:
            Dict[str, Any]: Component data with enum instances
        """
        # Convert component_type string to enum instance
        if "component_type" in component_dict and isinstance(component_dict["component_type"], str):
            try:
                component_dict["component_type"] = ComponentType(component_dict["component_type"])
            except ValueError:
                # Try to find by value
                for enum_item in ComponentType:
                    if enum_item.value == component_dict["component_type"]:
                        component_dict["component_type"] = enum_item
                        break
        
        # Convert category string to enum instance
        if "category" in component_dict and isinstance(component_dict["category"], str):
            try:
                component_dict["category"] = ComponentCategoryType(component_dict["category"])
            except ValueError:
                # Try to find by value
                for category_item in ComponentCategoryType:
                    if category_item.value == component_dict["category"]:
                        component_dict["category"] = category_item
                        break
        
        return component_dict

    def _sanitize_component_data(self, component_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize component data for security.

        Args:
            component_dict: Component data dictionary

        Returns:
            Dict[str, Any]: Sanitized component data
        """
        text_fields = ["name", "manufacturer", "model_number", "description", "supplier", "part_number"]
        
        for field in text_fields:
            if field in component_dict and component_dict[field]:
                component_dict[field] = sanitize_text(component_dict[field])

        return component_dict

    def _convert_to_read_schema(self, component: Component) -> ComponentReadSchema:
        """Convert database component model to read schema with proper JSON parsing.
        
        Args:
            component: Component database model
            
        Returns:
            ComponentReadSchema: Component read schema with parsed JSON fields
        """
        # Get the fields that are expected by ComponentReadSchema
        schema_fields = set(ComponentReadSchema.model_fields.keys())
        
        # Convert the component to a dictionary, only including schema fields
        component_dict = {}
        
        # Copy only the attributes that are defined in the schema
        for column in component.__table__.columns:
            if column.name in schema_fields:
                value = getattr(component, column.name)
                component_dict[column.name] = value
        
        # Parse JSON string fields back to dictionaries for the schema
        if component_dict.get('specifications') and isinstance(component_dict['specifications'], str):
            try:
                component_dict['specifications'] = json.loads(component_dict['specifications'])
            except (json.JSONDecodeError, TypeError):
                component_dict['specifications'] = None
        
        if component_dict.get('dimensions_json') and isinstance(component_dict['dimensions_json'], str):
            try:
                component_dict['dimensions_json'] = json.loads(component_dict['dimensions_json'])
            except (json.JSONDecodeError, TypeError):
                component_dict['dimensions_json'] = None
        
        if component_dict.get('metadata_json') and isinstance(component_dict['metadata_json'], str):
            try:
                component_dict['metadata_json'] = json.loads(component_dict['metadata_json'])
            except (json.JSONDecodeError, TypeError):
                component_dict['metadata_json'] = None
        
        # Create the schema from the processed dictionary
        return ComponentReadSchema.model_validate(component_dict)

    def _calculate_relevance_score(
        self,
        component: Component,
        search_term: str,
        search_fields: Optional[List[str]] = None
    ) -> float:
        """Calculate relevance score for a component based on search term.

        Args:
            component: Component to score
            search_term: Search term to match against
            search_fields: Fields to consider for scoring

        Returns:
            float: Relevance score between 0.0 and 1.0
        """
        if not search_term:
            return 0.0

        search_fields = search_fields or ["name", "description", "manufacturer", "part_number"]
        search_term_lower = search_term.lower()
        total_score = 0.0
        field_count = 0

        for field in search_fields:
            field_value = getattr(component, field, None)
            if field_value:
                field_value_lower = str(field_value).lower()

                # Exact match gets highest score
                if search_term_lower == field_value_lower:
                    total_score += 1.0
                # Starts with gets high score
                elif field_value_lower.startswith(search_term_lower):
                    total_score += 0.8
                # Ends with gets medium score
                elif field_value_lower.endswith(search_term_lower):
                    total_score += 0.6
                # Contains gets lower score
                elif search_term_lower in field_value_lower:
                    total_score += 0.4

                field_count += 1

        return total_score / max(field_count, 1)

    def _get_matched_fields(
        self,
        component: Component,
        search_term: str,
        search_fields: Optional[List[str]] = None
    ) -> List[str]:
        """Get list of fields that matched the search term.

        Args:
            component: Component to check
            search_term: Search term to match against
            search_fields: Fields to check for matches

        Returns:
            List[str]: List of field names that matched
        """
        if not search_term:
            return []

        search_fields = search_fields or ["name", "description", "manufacturer", "part_number"]
        search_term_lower = search_term.lower()
        matched_fields = []

        for field in search_fields:
            field_value = getattr(component, field, None)
            if field_value and search_term_lower in str(field_value).lower():
                matched_fields.append(field)

        return matched_fields

    def _count_applied_filters(self, search_schema: ComponentAdvancedSearchSchema) -> int:
        """Count the number of filters applied in the search schema.

        Args:
            search_schema: Advanced search schema

        Returns:
            int: Number of filters applied
        """
        filter_count = 0

        if search_schema.search_term:
            filter_count += 1
        if search_schema.basic_filters:
            filter_count += len(search_schema.basic_filters)
        if search_schema.specification_filters:
            filter_count += len(search_schema.specification_filters)
        if search_schema.range_filters:
            filter_count += len(search_schema.range_filters)
        if search_schema.price_range:
            filter_count += 1

        return filter_count

    def _get_search_suggestions(self, search_term: str, limit: int = 5) -> List[str]:
        """Get search suggestions based on the search term.

        Args:
            search_term: Current search term
            limit: Maximum number of suggestions

        Returns:
            List[str]: List of search suggestions
        """
        suggestions = []

        # Get suggestions from different fields
        for field in ["name", "manufacturer", "part_number"]:
            field_suggestions = self.component_repo.get_search_suggestions(
                search_term, field, limit // 3 + 1
            )
            suggestions.extend(field_suggestions)

        # Remove duplicates and limit results
        unique_suggestions = list(dict.fromkeys(suggestions))
        return unique_suggestions[:limit]

    @handle_service_errors("get_performance_metrics")
    @monitor_service_performance("get_performance_metrics")
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics for the component system.

        Returns:
            Dict with performance metrics and statistics
        """
        logger.debug("Getting component system performance metrics")

        try:
            # Get repository statistics
            repo_stats = self.component_repo.get_performance_optimized_stats()

            # Get cache statistics
            from src.core.utils.advanced_cache_manager import cache_manager
            cache_stats = cache_manager.get_cache_stats()

            # Get query optimizer statistics
            from src.core.utils.query_optimizer import query_optimizer
            query_stats = {}
            if query_optimizer:
                slow_queries = query_optimizer.get_slow_queries(5)
                index_stats = query_optimizer.get_index_usage_stats()

                query_stats = {
                    "slow_queries_count": len(slow_queries),
                    "slowest_query_time": slow_queries[0].execution_time if slow_queries else 0,
                    "index_usage": index_stats
                }

            # Combine all metrics
            metrics = {
                "component_statistics": repo_stats,
                "cache_performance": cache_stats,
                "query_performance": query_stats,
                "system_health": {
                    "database_connected": True,  # Would check actual connection
                    "cache_connected": cache_stats.get("redis_connected", False),
                    "performance_monitoring_active": query_optimizer is not None
                }
            }

            return metrics

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            raise BusinessLogicError(f"Failed to get performance metrics: {str(e)}")

    @handle_service_errors("optimize_system_performance")
    @monitor_service_performance("optimize_system_performance")
    def optimize_system_performance(self) -> Dict[str, Any]:
        """Perform system performance optimization tasks.

        Returns:
            Dict with optimization results
        """
        logger.info("Starting system performance optimization")

        optimization_results = {
            "cache_warming": False,
            "cache_cleanup": False,
            "index_analysis": False,
            "query_optimization": False,
            "errors": []
        }

        try:
            # Warm cache for popular searches
            self.component_repo.warm_cache_for_popular_searches()
            optimization_results["cache_warming"] = True

            # Clean up expired cache entries
            from src.core.utils.advanced_cache_manager import cache_manager
            cache_manager.delete_pattern("*:expired:*")
            optimization_results["cache_cleanup"] = True

            # Analyze index usage and suggest optimizations
            from src.core.utils.query_optimizer import query_optimizer
            if query_optimizer:
                index_suggestions = query_optimizer.suggest_indexes("components")
                optimization_results["index_suggestions"] = index_suggestions
                optimization_results["index_analysis"] = True

            optimization_results["query_optimization"] = True

            logger.info("System performance optimization completed successfully")

        except Exception as e:
            error_msg = f"Performance optimization error: {str(e)}"
            logger.error(error_msg)
            optimization_results["errors"].append(error_msg)

        return optimization_results

    @handle_service_errors("batch_get_components_optimized")
    @monitor_service_performance("batch_get_components_optimized")
    def batch_get_components_optimized(
        self,
        component_ids: List[int],
        include_relationships: bool = False
    ) -> List[ComponentReadSchema]:
        """Get multiple components with performance optimization.

        Args:
            component_ids: List of component IDs to retrieve
            include_relationships: Whether to preload relationships

        Returns:
            List of component schemas
        """
        logger.debug(f"Batch getting {len(component_ids)} components (optimized)")

        try:
            if include_relationships:
                components = self.component_repo.get_components_with_preloaded_relationships(
                    component_ids=component_ids
                )
            else:
                components = self.component_repo.batch_get_components_by_ids(component_ids)

            # Convert to schemas
            component_schemas = [self._convert_to_read_schema(c) for c in components]

            logger.debug(f"Batch retrieval completed: {len(component_schemas)} components")
            return component_schemas

        except Exception as e:
            logger.error(f"Error in batch component retrieval: {e}")
            raise BusinessLogicError(f"Batch component retrieval failed: {str(e)}")

    @handle_service_errors("invalidate_component_cache")
    @monitor_service_performance("invalidate_component_cache")
    def invalidate_component_cache(self, component_id: Optional[int] = None) -> Dict[str, Any]:
        """Invalidate component cache entries.

        Args:
            component_id: Specific component ID to invalidate (optional)

        Returns:
            Dict with invalidation results
        """
        logger.debug(f"Invalidating component cache for ID: {component_id or 'all'}")

        try:
            self.component_repo.invalidate_component_cache(component_id)

            result = {
                "success": True,
                "component_id": component_id,
                "scope": "specific" if component_id else "all",
                "timestamp": utcnow_aware().isoformat()
            }

            logger.debug("Component cache invalidation completed")
            return result

        except Exception as e:
            logger.error(f"Error invalidating component cache: {e}")
            raise BusinessLogicError(f"Cache invalidation failed: {str(e)}")

    # ============================================================================
    # IMPORT/EXPORT UTILITY METHODS
    # ============================================================================

    def _export_to_json(self, components: List[ComponentReadSchema]) -> Dict[str, Any]:
        """Export components to JSON format.

        Args:
            components: List of component schemas to export

        Returns:
            Dict[str, Any]: JSON export data
        """
        export_data = {
            "format": "json",
            "version": "1.0",
            "exported_at": utcnow_aware().isoformat(),
            "total_components": len(components),
            "components": [component.model_dump() for component in components]
        }
        return export_data

    def _export_to_csv(self, components: List[ComponentReadSchema]) -> Dict[str, Any]:
        """Export components to CSV format.

        Args:
            components: List of component schemas to export

        Returns:
            Dict[str, Any]: CSV export data
        """
        import csv
        import io
        
        # Create CSV content
        output = io.StringIO()
        if components:
            # Get field names from the first component
            fieldnames = list(components[0].model_dump().keys())
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            
            for component in components:
                # Flatten complex fields for CSV
                row_data = component.model_dump()
                for key, value in row_data.items():
                    if isinstance(value, (dict, list)):
                        row_data[key] = json.dumps(value) if value else ""
                writer.writerow(row_data)
        
        csv_content = output.getvalue()
        output.close()
        
        return {
            "format": "csv",
            "content": csv_content,
            "total_components": len(components)
        }

    def _export_to_xlsx(self, components: List[ComponentReadSchema]) -> Dict[str, Any]:
        """Export components to XLSX format.

        Args:
            components: List of component schemas to export

        Returns:
            Dict[str, Any]: XLSX export data
        """
        try:
            import io

            import pandas as pd
            
            # Convert components to DataFrame
            if components:
                data = []
                for component in components:
                    row_data = component.model_dump()
                    # Flatten complex fields for Excel
                    for key, value in row_data.items():
                        if isinstance(value, (dict, list)):
                            row_data[key] = json.dumps(value) if value else ""
                    data.append(row_data)
                
                df = pd.DataFrame(data)
                
                # Create Excel file in memory
                output = io.BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='Components', index=False)
                
                excel_content = output.getvalue()
                output.close()
                
                return {
                    "format": "xlsx",
                    "content": excel_content,
                    "total_components": len(components)
                }
            else:
                return {
                    "format": "xlsx",
                    "content": b"",
                    "total_components": 0
                }
                
        except ImportError:
            logger.error("pandas and openpyxl are required for XLSX export")
            raise InvalidInputError("XLSX export requires pandas and openpyxl packages")

    def _parse_json_import(self, import_data: Dict[str, Any]) -> List[ComponentCreateSchema]:
        """Parse JSON import data into component creation schemas.

        Args:
            import_data: JSON import data

        Returns:
            List[ComponentCreateSchema]: List of component creation schemas
        """
        components_data = import_data.get("components", [])
        if not isinstance(components_data, list):
            raise InvalidInputError("JSON import data must contain a 'components' array")
        
        component_schemas = []
        for i, component_data in enumerate(components_data):
            try:
                # Remove fields that shouldn't be in create schema
                create_data = {k: v for k, v in component_data.items()
                             if k not in ['id', 'created_at', 'updated_at', 'is_deleted']}
                
                component_schema = ComponentCreateSchema.model_validate(create_data)
                component_schemas.append(component_schema)
            except Exception as e:
                logger.error(f"Error parsing component {i+1} from JSON import: {e}")
                raise ValidationError(f"Invalid component data at index {i+1}: {e}")
        
        return component_schemas

    def _parse_csv_import(self, import_data: Dict[str, Any]) -> List[ComponentCreateSchema]:
        """Parse CSV import data into component creation schemas.

        Args:
            import_data: CSV import data

        Returns:
            List[ComponentCreateSchema]: List of component creation schemas
        """
        import csv
        import io
        
        csv_content = import_data.get("content", "")
        if not csv_content:
            raise InvalidInputError("CSV import data must contain 'content' field")
        
        # Parse CSV content
        csv_reader = csv.DictReader(io.StringIO(csv_content))
        component_schemas = []
        
        for i, row in enumerate(csv_reader):
            try:
                # Process row data
                component_data = {}
                for key, value in row.items():
                    if value:
                        # Try to parse JSON fields
                        if key in ['specifications', 'dimensions_json', 'metadata_json']:
                            try:
                                component_data[key] = json.loads(value) if value.strip() else None
                            except json.JSONDecodeError:
                                component_data[key] = value
                        else:
                            component_data[key] = value
                
                # Remove fields that shouldn't be in create schema
                create_data = {k: v for k, v in component_data.items()
                             if k not in ['id', 'created_at', 'updated_at', 'is_deleted']}
                
                component_schema = ComponentCreateSchema.model_validate(create_data)
                component_schemas.append(component_schema)
            except Exception as e:
                logger.error(f"Error parsing component {i+1} from CSV import: {e}")
                raise ValidationError(f"Invalid component data at row {i+1}: {e}")
        
        return component_schemas

    def _parse_xlsx_import(self, import_data: Dict[str, Any]) -> List[ComponentCreateSchema]:
        """Parse XLSX import data into component creation schemas.

        Args:
            import_data: XLSX import data

        Returns:
            List[ComponentCreateSchema]: List of component creation schemas
        """
        try:
            import io

            import pandas as pd
            
            excel_content = import_data.get("content")
            if not excel_content:
                raise InvalidInputError("XLSX import data must contain 'content' field")
            
            # Read Excel file from bytes
            df = pd.read_excel(io.BytesIO(excel_content), sheet_name=0)
            
            component_schemas = []
            for idx, row in df.iterrows():
                try:
                    # Convert row to dictionary
                    component_data = row.to_dict()
                    
                    # Process data
                    for key, value in component_data.items():
                        if pd.isna(value):
                            component_data[key] = None
                        elif key in ['specifications', 'dimensions_json', 'metadata_json'] and value:
                            try:
                                component_data[key] = json.loads(str(value)) if str(value).strip() else None
                            except json.JSONDecodeError:
                                component_data[key] = str(value)
                        else:
                            component_data[key] = value
                    
                    # Remove fields that shouldn't be in create schema
                    create_data = {k: v for k, v in component_data.items()
                                 if k not in ['id', 'created_at', 'updated_at', 'is_deleted'] and v is not None}
                    
                    component_schema = ComponentCreateSchema.model_validate(create_data)
                    component_schemas.append(component_schema)
                except Exception as e:
                    row_num = int(idx) + 1 if isinstance(idx, (int, float)) else idx
                    logger.error(f"Error parsing component {row_num} from XLSX import: {e}")
                    raise ValidationError(f"Invalid component data at row {row_num}: {e}")
            
            return component_schemas
            
        except ImportError:
            logger.error("pandas and openpyxl are required for XLSX import")
            raise InvalidInputError("XLSX import requires pandas and openpyxl packages")


__all__ = ["ComponentService"]