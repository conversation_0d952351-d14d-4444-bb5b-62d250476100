#!/bin/bash

# Component Management Testing Suite Runner
# Runs all tests for the component management module with coverage and quality checks

set -e

echo "🧪 Component Management Testing Suite"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the client directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the client directory"
    exit 1
fi

# Initialize counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
COVERAGE_THRESHOLD=95

print_status "Starting Component Management Test Suite..."

# 1. Type checking
print_status "Running TypeScript type checking..."
if npm run type-check; then
    print_success "TypeScript type checking passed"
else
    print_error "TypeScript type checking failed"
    exit 1
fi

# 2. Linting
print_status "Running ESLint checks..."
if npm run lint; then
    print_success "ESLint checks passed"
else
    print_error "ESLint checks failed"
    exit 1
fi

# 3. Code formatting check
print_status "Checking code formatting with Prettier..."
if npm run format:check; then
    print_success "Code formatting is correct"
else
    print_warning "Code formatting issues found. Run 'npm run format' to fix."
fi

# 4. Unit Tests
print_status "Running unit tests for component management..."
echo "Testing components, hooks, API clients, and utilities..."

# Run unit tests with coverage
if npm run test -- --coverage --reporter=verbose src/modules/components/__tests__/; then
    print_success "Unit tests passed"
    
    # Extract coverage information
    COVERAGE_OUTPUT=$(npm run test:coverage -- --reporter=json src/modules/components/__tests__/ 2>/dev/null | tail -1)
    
    # Check coverage thresholds
    print_status "Checking coverage thresholds..."
    if npm run test:coverage -- --coverage.thresholds.global.lines=$COVERAGE_THRESHOLD src/modules/components/__tests__/; then
        print_success "Coverage thresholds met (>= ${COVERAGE_THRESHOLD}%)"
    else
        print_warning "Coverage below threshold of ${COVERAGE_THRESHOLD}%"
    fi
else
    print_error "Unit tests failed"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# 5. Integration Tests
print_status "Running integration tests..."
if npm run test -- src/test/integration/component-management-integration.test.tsx; then
    print_success "Integration tests passed"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_error "Integration tests failed"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# 6. Component-specific unit tests
print_status "Running individual component tests..."

COMPONENT_TESTS=(
    "src/modules/components/__tests__/components/ComponentCard.test.tsx"
    "src/modules/components/__tests__/components/ComponentList.test.tsx"
    "src/modules/components/__tests__/components/ComponentSearch.test.tsx"
    "src/modules/components/__tests__/components/ComponentFilters.test.tsx"
    "src/modules/components/__tests__/hooks/useComponentStore.test.tsx"
    "src/modules/components/__tests__/hooks/useComponentForm.test.tsx"
    "src/modules/components/__tests__/api/componentApi.test.ts"
    "src/modules/components/__tests__/api/componentQueries.test.tsx"
    "src/modules/components/__tests__/utils.test.ts"
)

for test_file in "${COMPONENT_TESTS[@]}"; do
    test_name=$(basename "$test_file" .test.tsx)
    test_name=$(basename "$test_name" .test.ts)
    
    print_status "Testing $test_name..."
    
    if npm run test -- "$test_file"; then
        print_success "$test_name tests passed"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "$test_name tests failed"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
done

# 7. E2E Tests (optional, requires server to be running)
print_status "Checking if E2E tests can be run..."
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    print_status "Development server detected, running E2E tests..."
    
    if npm run test:e2e -- tests/e2e/components/component-management.spec.ts; then
        print_success "E2E tests passed"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "E2E tests failed"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    print_warning "Development server not running, skipping E2E tests"
    print_warning "To run E2E tests: npm run dev (in another terminal) then run this script again"
fi

# 8. Test Coverage Report
print_status "Generating detailed coverage report..."
npm run test:coverage -- src/modules/components/__tests__/ --reporter=html

if [ -d "coverage" ]; then
    print_success "Coverage report generated in coverage/ directory"
    print_status "Open coverage/index.html in your browser to view detailed coverage"
fi

# 9. Performance Tests (if any)
print_status "Running performance tests..."
if [ -f "src/modules/components/__tests__/performance/component-performance.test.ts" ]; then
    if npm run test -- src/modules/components/__tests__/performance/; then
        print_success "Performance tests passed"
    else
        print_warning "Performance tests failed or had warnings"
    fi
else
    print_warning "No performance tests found"
fi

# 10. Accessibility Tests
print_status "Running accessibility tests..."
if npm run test -- --testNamePattern="accessibility|a11y" src/modules/components/__tests__/; then
    print_success "Accessibility tests passed"
else
    print_warning "Accessibility tests failed or not found"
fi

# Summary
echo ""
echo "🏁 Test Suite Summary"
echo "===================="
echo "Total test suites: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    print_success "All tests passed! ✨"
    echo ""
    echo "✅ TypeScript compliance"
    echo "✅ ESLint compliance"
    echo "✅ Unit tests (100% pass rate)"
    echo "✅ Integration tests"
    echo "✅ Coverage targets met"
    
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ E2E tests"
    else
        echo "⚠️  E2E tests (skipped - server not running)"
    fi
    
    echo ""
    print_success "Component Management module is ready for production! 🚀"
    exit 0
else
    print_error "Some tests failed. Please fix the issues before proceeding."
    echo ""
    echo "Failed test suites: $FAILED_TESTS"
    echo ""
    print_error "Component Management module is NOT ready for production."
    exit 1
fi
