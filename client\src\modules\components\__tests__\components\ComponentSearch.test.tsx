/**
 * ComponentSearch Unit Tests
 * Tests the ComponentSearch component with debouncing, suggestions, and keyboard navigation
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ComponentSearch } from '../../components/ComponentSearch';
import { renderWithProviders } from '@/test/utils';

// Mock the useComponentSuggestions hook
const mockSuggestions = ['resistor 1k', 'resistor 2k', 'resistor 10k'];
vi.mock('../../api/componentQueries', () => ({
  useComponentSuggestions: vi.fn(() => ({
    data: mockSuggestions,
    isLoading: false,
    isError: false,
  })),
}));

describe('ComponentSearch', () => {
  const mockHandlers = {
    onSearch: vi.fn(),
    onChange: vi.fn(),
    onClear: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Rendering', () => {
    it('renders search input with placeholder', () => {
      renderWithProviders(
        <ComponentSearch 
          placeholder="Search components..."
          {...mockHandlers}
        />
      );

      const input = screen.getByPlaceholderText('Search components...');
      expect(input).toBeInTheDocument();
    });

    it('renders with initial value', () => {
      renderWithProviders(
        <ComponentSearch 
          value="initial search"
          {...mockHandlers}
        />
      );

      const input = screen.getByDisplayValue('initial search');
      expect(input).toBeInTheDocument();
    });

    it('renders search icon', () => {
      renderWithProviders(
        <ComponentSearch {...mockHandlers} />
      );

      expect(screen.getByTestId('search-icon')).toBeInTheDocument();
    });

    it('renders clear button when there is text', () => {
      renderWithProviders(
        <ComponentSearch 
          value="search text"
          {...mockHandlers}
        />
      );

      expect(screen.getByRole('button', { name: /clear search/i })).toBeInTheDocument();
    });

    it('does not render clear button when input is empty', () => {
      renderWithProviders(
        <ComponentSearch 
          value=""
          {...mockHandlers}
        />
      );

      expect(screen.queryByRole('button', { name: /clear search/i })).not.toBeInTheDocument();
    });
  });

  describe('Input Interactions', () => {
    it('calls onChange when user types', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch {...mockHandlers} />
      );

      const input = screen.getByRole('textbox');
      await user.type(input, 'resistor');

      expect(mockHandlers.onChange).toHaveBeenCalledWith('resistor');
    });

    it('debounces search calls', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch {...mockHandlers} />
      );

      const input = screen.getByRole('textbox');
      await user.type(input, 'res');

      // Should not call onSearch immediately
      expect(mockHandlers.onSearch).not.toHaveBeenCalled();

      // Advance timers to trigger debounced search
      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(mockHandlers.onSearch).toHaveBeenCalledWith('res');
      });
    });

    it('calls onClear when clear button is clicked', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(
        <ComponentSearch 
          value="search text"
          {...mockHandlers}
        />
      );

      const clearButton = screen.getByRole('button', { name: /clear search/i });
      await user.click(clearButton);

      expect(mockHandlers.onClear).toHaveBeenCalled();
    });

    it('triggers search on Enter key press', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(
        <ComponentSearch 
          value="resistor"
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.type(input, '{enter}');

      expect(mockHandlers.onSearch).toHaveBeenCalledWith('resistor');
    });
  });

  describe('Suggestions Dropdown', () => {
    it('shows suggestions when showSuggestions is true and input has focus', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(screen.getByText('resistor 1k')).toBeInTheDocument();
        expect(screen.getByText('resistor 2k')).toBeInTheDocument();
        expect(screen.getByText('resistor 10k')).toBeInTheDocument();
      });
    });

    it('hides suggestions when showSuggestions is false', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={false}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      expect(screen.queryByText('resistor 1k')).not.toBeInTheDocument();
    });

    it('shows recent searches when input is empty and focused', async () => {
      const user = userEvent.setup();
      const recentSearches = ['capacitor', 'diode', 'transistor'];
      
      renderWithProviders(
        <ComponentSearch 
          recentSearches={recentSearches}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);

      expect(screen.getByText('Recent Searches')).toBeInTheDocument();
      expect(screen.getByText('capacitor')).toBeInTheDocument();
      expect(screen.getByText('diode')).toBeInTheDocument();
      expect(screen.getByText('transistor')).toBeInTheDocument();
    });

    it('selects suggestion on click', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(screen.getByText('resistor 1k')).toBeInTheDocument();
      });

      await user.click(screen.getByText('resistor 1k'));

      expect(mockHandlers.onChange).toHaveBeenCalledWith('resistor 1k');
      expect(mockHandlers.onSearch).toHaveBeenCalledWith('resistor 1k');
    });
  });

  describe('Keyboard Navigation', () => {
    it('navigates suggestions with arrow keys', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(screen.getByText('resistor 1k')).toBeInTheDocument();
      });

      // Navigate down
      await user.keyboard('{ArrowDown}');
      expect(screen.getByText('resistor 1k')).toHaveClass('bg-blue-50');

      // Navigate down again
      await user.keyboard('{ArrowDown}');
      expect(screen.getByText('resistor 2k')).toHaveClass('bg-blue-50');

      // Navigate up
      await user.keyboard('{ArrowUp}');
      expect(screen.getByText('resistor 1k')).toHaveClass('bg-blue-50');
    });

    it('selects highlighted suggestion with Enter', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(screen.getByText('resistor 1k')).toBeInTheDocument();
      });

      // Navigate to first suggestion and select
      await user.keyboard('{ArrowDown}');
      await user.keyboard('{Enter}');

      expect(mockHandlers.onChange).toHaveBeenCalledWith('resistor 1k');
      expect(mockHandlers.onSearch).toHaveBeenCalledWith('resistor 1k');
    });

    it('closes dropdown with Escape key', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(screen.getByText('resistor 1k')).toBeInTheDocument();
      });

      await user.keyboard('{Escape}');

      expect(screen.queryByText('resistor 1k')).not.toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('shows loading indicator when suggestions are loading', async () => {
      const { useComponentSuggestions } = await import('../../api/componentQueries');
      vi.mocked(useComponentSuggestions).mockReturnValue({
        data: undefined,
        isLoading: true,
        isError: false,
      } as any);

      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(
        <ComponentSearch {...mockHandlers} />
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-label', 'Search components');
      expect(input).toHaveAttribute('aria-autocomplete', 'list');
    });

    it('associates dropdown with input using ARIA', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        const dropdown = screen.getByRole('listbox');
        expect(dropdown).toBeInTheDocument();
        expect(input).toHaveAttribute('aria-expanded', 'true');
        expect(input).toHaveAttribute('aria-owns', dropdown.id);
      });
    });

    it('has proper roles for suggestions', async () => {
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'res');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        const suggestions = screen.getAllByRole('option');
        expect(suggestions).toHaveLength(3);
        suggestions.forEach(suggestion => {
          expect(suggestion).toHaveAttribute('role', 'option');
        });
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles empty suggestions gracefully', async () => {
      const { useComponentSuggestions } = await import('../../api/componentQueries');
      vi.mocked(useComponentSuggestions).mockReturnValue({
        data: [],
        isLoading: false,
        isError: false,
      } as any);

      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithProviders(
        <ComponentSearch 
          showSuggestions={true}
          {...mockHandlers}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);
      await user.type(input, 'xyz');

      vi.advanceTimersByTime(300);

      await waitFor(() => {
        expect(screen.getByText('No suggestions found')).toBeInTheDocument();
      });
    });

    it('handles missing handlers gracefully', () => {
      renderWithProviders(
        <ComponentSearch />
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });

    it('handles very long search terms', async () => {
      const user = userEvent.setup();
      const longTerm = 'a'.repeat(1000);
      
      renderWithProviders(
        <ComponentSearch {...mockHandlers} />
      );

      const input = screen.getByRole('textbox');
      await user.type(input, longTerm);

      expect(mockHandlers.onChange).toHaveBeenCalledWith(longTerm);
    });
  });
});
