# **Typing for Testing (Vitest, React Testing Library, MSW)**

This guide focuses on how to leverage TypeScript to write robust and type-safe tests for our Heat Tracing Design Application, using Vitest, React Testing Library, and MSW (Mock Service Worker).

## **1. Typing Test Data and Fixtures**

Consistent and type-safe test data is fundamental for reliable tests.

- **What it is:** Define interfaces or types for your test data that mirror your application's data models. Create test data factories or fixtures that generate this typed data.  
  // src/types/test-data.ts (Example)  
  import { ProjectReadSchema, UserReadSchema } from './api'; // Import generated API types  
    
  // Define a type for a Project in a test context (might extend or be a subset of API schema)  
  type TestProject = Pick\<ProjectReadSchema, 'id' \| 'name' \| 'status'\> & {  
  engineers: string\[\]; // Add test-specific properties  
  };  
    
  // A simple factory function for generating test projects  
  export const createTestProject = (overrides?: Partial\<TestProject\>): TestProject =\> {  
  const id = overrides?.id \|\| \`proj-\${Math.random().toString(36).substring(7)}\`;  
  return {  
  id,  
  name: overrides?.name \|\| \`Test Project \${id.substring(5, 9)}\`,  
  status: overrides?.status \|\| 'Active',  
  engineers: overrides?.engineers \|\| \['Test Engineer'\],  
  ...overrides,  
  };  
  };  
    
  // Example test user  
  export const testUser: UserReadSchema = {  
  id: 'user-test-admin',  
  username: '<EMAIL>',  
  roles: \['admin', 'engineer'\],  
  // ... other properties from UserReadSchema  
  };

- **Why it helps:** Ensures that your test data matches the expected data structures throughout your application, preventing subtle bugs caused by type mismatches in tests. Factories make it easy to generate diverse test cases.

## **2. Typing Mocks and Spies with Vitest**

Vitest (and Jest, which it's compatible with) provides powerful mocking capabilities. TypeScript helps ensure mocks behave as expected.

- **What it is:** Using vi.fn\<Args, Return\>() or vi.spyOn() to create typed mock functions.  
  // src/services/authService.ts (Example service to be mocked)  
  interface AuthService {  
  login: (username: string, password: string) =\> Promise\<{ token: string }\>;  
  logout: () =\> void;  
  getCurrentUser: () =\> { id: string; name: string } \| null;  
  }  
  export const authService: AuthService = { /\* ... implementation ... \*/ };  
    
    
  // In your test file (e.g., tests/unit/components/LoginForm.test.tsx)  
  import { render, screen, fireEvent, waitFor } from '@testing-library/react';  
  import { describe, it, expect, vi } from 'vitest';  
  import LoginForm from '@/components/auth/LoginForm'; // The component to test  
  import { authService } from '@/services/authService'; // The service to mock  
    
  describe('LoginForm', () =\> {  
  it('should call login service with correct credentials', async () =\> {  
  // Mock the login function with a specific return type  
  const mockLogin = vi.fn\<\[string, string\], Promise\<{ token: string }\>\>(() =\>  
  Promise.resolve({ token: 'mock-token' })  
  );  
  vi.spyOn(authService, 'login').mockImplementation(mockLogin);  
    
  render(\<LoginForm /\>);  
    
  fireEvent.change(screen.getByLabelText(/username/i), { target: { value: '<EMAIL>' } });  
  fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'password123' } });  
  fireEvent.click(screen.getByRole('button', { name: /login/i }));  
    
  await waitFor(() =\> {  
  expect(mockLogin).toHaveBeenCalledTimes(1);  
  expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');  
  });  
  });  
    
  it('should show an error message on failed login', async () =\> {  
  // Mock the login function to reject with an error  
  const mockLoginError = vi.fn\<\[string, string\], Promise\<{ token: string }\>\>(() =\>  
  Promise.reject(new Error('Invalid credentials'))  
  );  
  vi.spyOn(authService, 'login').mockImplementation(mockLoginError);  
    
  render(\<LoginForm /\>);  
  fireEvent.click(screen.getByRole('button', { name: /login/i }));  
    
  await waitFor(() =\> {  
  expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();  
  });  
  });  
  });

- **Why it helps:** Ensures that your mock functions have the correct arguments and return types, preventing type mismatches in your tests and providing confidence that your mocks accurately simulate the real behavior of dependencies.

## **3. Typing MSW (Mock Service Worker) Handlers and Responses**

MSW allows you to mock network requests. TypeScript integration provides type safety for mock API responses.

- **What it is:** MSW's http (or rest) handlers allow you to define mock responses. TypeScript ensures these responses conform to your API schemas.  
  // src/mocks/handlers.ts (Example MSW handlers)  
  import { http, HttpResponse } from 'msw';  
  import { ProjectReadSchema, ProjectCreateSchema, ErrorResponseSchema } from '@/types/api';  
  import { createTestProject } from '@/types/test-data';  
    
  // Mock data storage (for stateful mocks if needed)  
  let mockProjects: ProjectReadSchema\[\] = \[  
  createTestProject({ id: 'proj-001', name: 'Alpha Project' }),  
  createTestProject({ id: 'proj-002', name: 'Beta Project' }),  
  \];  
    
  export const handlers = \[  
  // GET /api/v1/projects/:projectId  
  http.get('http://localhost:8000/api/v1/projects/:projectId', ({ params }) =\> {  
  const { projectId } = params;  
  const project = mockProjects.find(p =\> p.id === projectId);  
    
  if (!project) {  
  // Ensure error response matches ErrorResponseSchema  
  return HttpResponse.json\<ErrorResponseSchema\>(  
  { detail: \`Project with ID \${projectId} not found.\` },  
  { status: 404 }  
  );  
  }  
  // Ensure successful response matches ProjectReadSchema  
  return HttpResponse.json\<ProjectReadSchema\>(project);  
  }),  
    
  // POST /api/v1/projects  
  http.post('http://localhost:8000/api/v1/projects', async ({ request }) =\> {  
  const newProjectData = await request.json() as ProjectCreateSchema; // Assert incoming request body  
  const newProject: ProjectReadSchema = {  
  id: \`proj-\${Math.random().toString(36).substring(7)}\`,  
  createdAt: new Date().toISOString(),  
  updatedAt: new Date().toISOString(),  
  status: 'Active', // Default status  
  ...newProjectData,  
  };  
  mockProjects.push(newProject);  
  // Ensure response matches ProjectReadSchema  
  return HttpResponse.json\<ProjectReadSchema\>(newProject, { status: 201 });  
  }),  
  \];

- **Why it helps:** Ensures that your mocked API responses conform to the actual backend API schemas, providing a realistic and type-safe testing environment. This allows frontend developers to build and test features confidently without needing a live backend, and ensures that when the real backend is integrated, fewer unexpected type mismatches occur.
