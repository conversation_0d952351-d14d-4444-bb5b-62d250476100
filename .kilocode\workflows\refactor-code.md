### 6. Workflow: Refactoring Code

This workflow defines the process for improving the internal structure of code without changing its external behavior, directly supporting the "Zero Tolerance Policies" for technical debt, applicable to both backend and frontend.

* **6.1. Discovery & Analysis:**
    * **Task:** Identify specific areas of the codebase that require refactoring (e.g., complex functions, low cohesion, high coupling, code smells) in either backend Python or frontend TypeScript/React code. This can be triggered by "Analyze the Codebase" workflow.
    * **Guidance for AI:** Review code complexity metrics, linting reports, and design patterns to pinpoint refactoring candidates across the entire codebase.
* **6.2. Task Planning:**
    * **Task:** Plan the refactoring process into small, isolated, and verifiable steps to minimize risk and ensure stability for both backend and frontend components.
    * **Guidance for AI:** Create a granular plan, prioritizing changes that yield the most significant improvements in maintainability or performance without introducing regressions, considering impact on both sides.
* **6.3. Implementation:**
    * **Task:** Apply refactoring techniques (e.g., extract method, rename variable, introduce parameter object, re-structure components, extract hooks), ensuring existing functionality is maintained and adhering to project coding standards like "SOLID" principles, "Unified Patterns", and **Component-Based Architecture**.
    * **Guidance for AI:** Generate modified code for either backend (Python) or frontend (TypeScript/React). Ensure type safety is preserved and no new warnings/errors are introduced by linting tools (**Ruff, ESLint**).
* **6.4. Verification:**
    * **Task:** Conduct comprehensive testing (unit, integration) for the refactored parts, specific to their domain (backend or frontend), to ensure no regressions are introduced and that quality metrics (e.g., linting, type coverage, code coverage) are improved or maintained.
    * **Guidance for AI:** Execute all relevant tests (**pytest** for backend, **Vitest/React Testing Library/Playwright** for frontend) before and after refactoring to confirm behavior remains unchanged. Verify adherence to "100% test pass rates".
* **6.5. Documentation & Handover:**
    * **Task:** Document the changes made during refactoring and provide the rationale behind them.
    * **Guidance for AI:** Update relevant sections of the "Developer Handbook", "Frontend Specification", or inline code comments, explaining the refactored design for both backend and frontend components.