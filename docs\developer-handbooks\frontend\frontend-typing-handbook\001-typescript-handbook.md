# **Frontend TypeScript Handbook: Your Guide to Type-Safe Development**

Welcome to the Frontend TypeScript Handbook for the Heat Tracing Design Application!

This comprehensive guide is designed to empower you with the knowledge and best practices for writing robust, maintainable, and highly efficient frontend code using TypeScript, React, and Next.js.

## **1. Why TypeScript for Our Application?**

In our Heat Tracing Design Application, TypeScript is not just a language choice; it's a fundamental architectural principle. Here's why it's indispensable:

- **Early Error Detection:** Catches common programming mistakes (like typos, incorrect function arguments, or invalid data shapes) during development, *before* your code even runs in the browser. This dramatically reduces runtime errors and debugging time.

- **Enhanced Developer Experience (DX):** Provides powerful IDE support, including auto-completion, intelligent refactoring, and real-time type checking, leading to faster and more confident coding.

- **Improved Code Quality & Readability:** Explicit types serve as living documentation, making it easier for developers (including your future self!) to understand what data a function expects, what a component's props are, or what an API call returns.

- **Safer Refactoring:** When you change the shape of data or a function signature, TypeScript immediately highlights all affected areas, allowing you to refactor with confidence.

- **Robust Backend Integration:** Our backend provides OpenAPI specifications, which we use to generate type-safe API clients. TypeScript ensures a seamless and validated communication contract between our frontend and backend.

- **Scalability & Maintainability:** As the application grows in complexity and team size, TypeScript's strict type checking prevents common pitfalls associated with large codebases, ensuring long-term maintainability.

## **2. How to Use This Handbook**

This handbook is structured to guide you from foundational TypeScript concepts to advanced, application-specific best practices.

- **Start with the Core:** If you're new to TypeScript or need a refresher, begin with the "Core TypeScript Concepts" guide.

- **Dive into React/Next.js Specifics:** Progress through the guides on typing React components, hooks, and Next.js features.

- **Master Backend Integration:** Pay close attention to the "Typing Data Fetching & API Interaction" guide, as this is critical for seamless backend integration.

- **Explore Advanced Topics:** Refer to specialized guides for forms, utilities, and testing as needed.

- **Use Examples:** All guides include examples directly relevant to our Heat Tracing Design Application.

## **3. Handbook Structure & Quick Links**

Click on the links below to navigate to detailed guides for each topic:

### **Foundational TypeScript**

- [**<u>Core TypeScript Concepts for React/Next.js</u>**](#sdnrou3ackl0)

  - Understanding Basic Types (string, number, boolean, any, unknown, void)

  - Arrays, Objects, Tuples

  - Type Aliases vs. Interfaces: When to Use Which

  - Enums for Domain-Specific Values

  - Union and Intersection Types

  - Type Inference and Type Assertions (as Type)

  - Generics: Writing Reusable Code

### **Typing React Application Elements**

- [**<u>Typing React Components & Props</u>**](#3sbdymvgxlwn)

  - Typing Functional Components (React.FC\<Props\>)

  - Defining Props Interfaces: Required, Optional, Default Props

  - Typing Children (React.ReactNode)

  - Event Handlers (e.g., React.MouseEvent, React.ChangeEvent)

  - Leveraging shadcn/ui Component Types

- [**<u>Typing React Hooks (useState, useEffect, Custom Hooks)</u>**](#xuov53mo0un2)

  - Type Inference with useState and Explicit Types

  - Typing useEffect Cleanup Functions

  - Typing useRef for DOM Elements and Mutable Values

  - Typing useContext for Global State

  - **Best Practices for Typing Custom Hooks (e.g., useAuth, useDebounce)**

- [**<u>Typing State Management with Zustand</u>**](#8ft2jmql7vvy)

  - Defining Zustand Store Interfaces

  - Typing State Properties and Actions

  - Using create\<MyState\>() for Store Creation

  - Typing Selectors for Derived State

### **Seamless Backend Integration**

- [**<u>Typing Data Fetching & API Interaction</u>**](#buh5qucgdu5x)

  - **Consuming Generated API Client Types (src/types/api.d.ts)**

  - Mapping Backend Pydantic Schemas to Frontend Types

  - **Typing React Query Hooks (useQuery, useMutation)**:

    - TData, TError, TVariables Parameters

    - Typing Query Keys

  - Handling Backend Error Responses with Types (ErrorResponseSchema)

  - Type Assertions for API Responses (When and When Not To)

### **Next.js Specific Typing**

- [**<u>Typing Next.js Specifics (App Router, Routing)</u>**](#lk9b9icko54d)

  - Typing App Router layout.tsx and page.tsx Props

  - Typing Route Parameters (params, searchParams)

  - Typing Next.js API Routes (if applicable for frontend-defined routes)

### **Practical Application & Advanced Concepts**

- [**<u>Typing Forms & Validation</u>**](#egrr5m2pavtr)

  - Defining Form State Interfaces

  - Typing Input Change Events

  - Structuring and Typing Validation Errors

  - Integration with Zod for Frontend Validation Schemas

- [**<u>Typing Utility Functions & Advanced Patterns</u>**](#mapl93adzl1s)

  - Writing Generic Utility Functions (e.g., for array manipulation, data transformations)

  - Implementing Custom Type Guards

  - Leveraging TypeScript Utility Types (Partial, Required, Pick, Omit, Exclude, Extract)

  - Conditional Types for Dynamic Typing

  - Typing lodash-es Functions (e.g., \_.get, \_.map)

- [**<u>Typing for Testing (Vitest, React Testing Library, MSW)</u>**](#qmtt28ifb64q)

  - Typing Test Data and Fixtures

  - Typing Mocks and Spies with Vitest (vi.fn\<Args, Return\>())

  - Typing MSW Handlers and Responses

## **4. Best Practices & Tooling**

- **Strict Mode:** Our project is configured with TypeScript's strict mode ("strict": true in tsconfig.json). Embrace it! It's your most powerful ally for catching errors.

- **ESLint & Prettier:** Trust our configured ESLint and Prettier setup to enforce coding standards and formatting. Let them auto-fix issues.

- **IDE Integration:** Ensure your IDE (e.g., VS Code) has the necessary TypeScript extensions enabled for real-time feedback.

- **Don't any Everything:** Avoid using any unless absolutely necessary and as a temporary measure with a // TODO comment. It defeats the purpose of TypeScript.

- **Consistent Naming:** Follow established naming conventions for types (PascalCase for Interfaces/Types, camelCase for variables).

- **Code Reviews:** Type annotations should be part of code review discussions, ensuring clarity and correctness.
