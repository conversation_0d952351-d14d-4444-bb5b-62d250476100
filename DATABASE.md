# Database Schema Documentation
## Ultimate Electrical Designer Application

### Document Information
- **Version**: 1.0
- **Date**: July 2025
- **Database**: PostgreSQL 15+ (Production), SQLite (Development)
- **ORM**: SQLAlchemy 2.0+ with Alembic migrations
- **Standards**: Professional electrical engineering data modeling

---

## 1. Executive Summary

This document provides comprehensive database schema documentation for the Ultimate Electrical Designer application. The database is designed to support professional electrical engineering workflows with complete entity modeling, calculation audit trails, and standards compliance tracking.

### 1.1 Database Architecture
- **Production Database**: PostgreSQL 15+ with advanced indexing and partitioning
- **Development Database**: SQLite for local development and testing
- **ORM Framework**: SQLAlchemy 2.0+ with declarative base models
- **Migration System**: Alembic for version-controlled schema changes
- **Type Safety**: 100% type annotations with strict validation

### 1.2 Data Modeling Principles
- **Normalization**: 3NF compliance with selective denormalization for performance
- **Relationships**: Foreign key constraints with cascading deletes
- **Indexing**: Strategic indexing for query performance optimization
- **Validation**: Database-level constraints with ORM validation layers
- **Security**: Role-based access control with audit trails

### 1.3 Current Implementation Status
- **Core Tables**: User management, component management, authentication
- **Relationships**: Fully implemented with proper foreign key constraints
- **Indexing**: Optimized for current query patterns
- **Migration Status**: 3 migrations applied, all tables created
- **Test Coverage**: 100% database operations tested

---

## 2. Database Configuration

### 2.1 Connection Configuration

#### Production Configuration (PostgreSQL)
```python
# server/src/config/database.py
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "ultimate_electrical_designer",
    "username": "app_user",
    "password": "${DATABASE_PASSWORD}",
    "pool_size": 10,
    "max_overflow": 20,
    "pool_pre_ping": True,
    "echo": False
}

# Connection URL
DATABASE_URL = "postgresql://app_user:${PASSWORD}@localhost:5432/ultimate_electrical_designer"
```

#### Development Configuration (SQLite)
```python
# server/src/config/database.py
DEVELOPMENT_CONFIG = {
    "database_path": "data/app_dev.db",
    "echo": True,
    "check_same_thread": False
}

# Connection URL
DATABASE_URL = "sqlite:///data/app_dev.db"
```

### 2.2 SQLAlchemy Configuration

#### Database Engine Setup
```python
# server/src/config/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Base class for all models
Base = declarative_base()

# Engine configuration
engine = create_engine(
    DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_size=10,
    max_overflow=20
)

# Session configuration
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

### 2.3 Alembic Migration Configuration

#### Alembic Configuration
```python
# server/src/alembic/alembic.ini
[alembic]
script_location = alembic
sqlalchemy.url = ${DATABASE_URL}
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s
```

#### Migration Environment
```python
# server/src/alembic/env.py
from alembic import context
from sqlalchemy import engine_from_config, pool
from src.core.models import Base

# Target metadata for autogenerate
target_metadata = Base.metadata

def run_migrations_online():
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    
    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True
        )
        
        with context.begin_transaction():
            context.run_migrations()
```

---

## 3. Core Database Schema

### 3.1 User Management Schema

#### 3.1.1 Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    
    -- Professional information
    professional_title VARCHAR(100),
    license_number VARCHAR(50),
    license_state VARCHAR(10),
    company_name VARCHAR(200),
    
    -- Settings
    preferences JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT chk_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_username_length CHECK (length(username) >= 3)
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 3.1.2 User Roles Table
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_role_name CHECK (name IN ('admin', 'engineer', 'viewer', 'manager'))
);

-- Default roles
INSERT INTO user_roles (name, description, permissions) VALUES
('admin', 'System administrator with full access', '{"all": true}'),
('engineer', 'Professional engineer with calculation access', '{"calculate": true, "design": true}'),
('viewer', 'Read-only access to designs and calculations', '{"view": true}'),
('manager', 'Project management with team oversight', '{"manage": true, "view": true}');
```

#### 3.1.3 User Role Assignments
```sql
CREATE TABLE user_role_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    UNIQUE(user_id, role_id)
);

-- Indexes
CREATE INDEX idx_user_role_assignments_user_id ON user_role_assignments(user_id);
CREATE INDEX idx_user_role_assignments_role_id ON user_role_assignments(role_id);
```

### 3.2 Component Management Schema

#### 3.2.1 Component Categories Table
```sql
CREATE TABLE component_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Self-referential foreign key
    FOREIGN KEY (parent_id) REFERENCES component_categories(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_category_name_length CHECK (length(name) >= 2)
);

-- Indexes
CREATE INDEX idx_component_categories_parent_id ON component_categories(parent_id);
CREATE INDEX idx_component_categories_sort_order ON component_categories(sort_order);

-- Sample categories
INSERT INTO component_categories (name, description) VALUES
('Electrical', 'Electrical components and devices'),
('Mechanical', 'Mechanical components and hardware'),
('Control', 'Control systems and automation'),
('Safety', 'Safety devices and systems'),
('Instrumentation', 'Measurement and monitoring devices');
```

#### 3.2.2 Component Types Table
```sql
CREATE TABLE component_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category_id UUID NOT NULL,
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key
    FOREIGN KEY (category_id) REFERENCES component_categories(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_type_name_length CHECK (length(name) >= 2)
);

-- Indexes
CREATE INDEX idx_component_types_category_id ON component_types(category_id);
CREATE INDEX idx_component_types_name ON component_types(name);

-- Sample types
INSERT INTO component_types (name, description, category_id) VALUES
('Circuit Breaker', 'Overcurrent protection device', 
 (SELECT id FROM component_categories WHERE name = 'Electrical')),
('Motor', 'Electric motor for mechanical drive', 
 (SELECT id FROM component_categories WHERE name = 'Electrical')),
('Transformer', 'Voltage transformation device', 
 (SELECT id FROM component_categories WHERE name = 'Electrical'));
```

#### 3.2.3 Components Table
```sql
CREATE TABLE components (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    manufacturer VARCHAR(100),
    model_number VARCHAR(100),
    part_number VARCHAR(100),
    type_id UUID NOT NULL,
    
    -- Technical specifications
    specifications JSONB DEFAULT '{}',
    electrical_ratings JSONB DEFAULT '{}',
    physical_dimensions JSONB DEFAULT '{}',
    environmental_ratings JSONB DEFAULT '{}',
    
    -- Commercial information
    cost_per_unit DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'USD',
    supplier VARCHAR(100),
    lead_time_days INTEGER,
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    
    -- Foreign keys
    FOREIGN KEY (type_id) REFERENCES component_types(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_component_name_length CHECK (length(name) >= 2),
    CONSTRAINT chk_component_status CHECK (status IN ('active', 'inactive', 'deprecated', 'obsolete')),
    CONSTRAINT chk_positive_cost CHECK (cost_per_unit >= 0),
    CONSTRAINT chk_positive_lead_time CHECK (lead_time_days >= 0)
);

-- Indexes
CREATE INDEX idx_components_type_id ON components(type_id);
CREATE INDEX idx_components_manufacturer ON components(manufacturer);
CREATE INDEX idx_components_model_number ON components(model_number);
CREATE INDEX idx_components_part_number ON components(part_number);
CREATE INDEX idx_components_status ON components(status);
CREATE INDEX idx_components_created_at ON components(created_at);
```

---

## 4. Electrical Engineering Schema

### 4.1 Project Management Schema

#### 4.1.1 Projects Table
```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    project_number VARCHAR(50) UNIQUE,
    client_name VARCHAR(200),
    location VARCHAR(200),
    
    -- Project details
    project_type VARCHAR(50),
    phase VARCHAR(50) DEFAULT 'planning',
    priority VARCHAR(20) DEFAULT 'medium',
    
    -- Professional information
    engineer_id UUID,
    pe_stamp_required BOOLEAN DEFAULT FALSE,
    standards_compliance JSONB DEFAULT '{}',
    
    -- Dates
    start_date DATE,
    target_completion_date DATE,
    actual_completion_date DATE,
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    
    -- Foreign keys
    FOREIGN KEY (engineer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_project_name_length CHECK (length(name) >= 2),
    CONSTRAINT chk_project_phase CHECK (phase IN ('planning', 'design', 'review', 'construction', 'completed')),
    CONSTRAINT chk_project_priority CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    CONSTRAINT chk_project_status CHECK (status IN ('active', 'inactive', 'completed', 'cancelled', 'on_hold')),
    CONSTRAINT chk_project_dates CHECK (start_date <= target_completion_date)
);

-- Indexes
CREATE INDEX idx_projects_engineer_id ON projects(engineer_id);
CREATE INDEX idx_projects_project_number ON projects(project_number);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_phase ON projects(phase);
CREATE INDEX idx_projects_created_at ON projects(created_at);
```

#### 4.1.2 Electrical Systems Table
```sql
CREATE TABLE electrical_systems (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    system_type VARCHAR(50) NOT NULL,
    
    -- System specifications
    voltage_level VARCHAR(20),
    frequency DECIMAL(5, 2),
    phases INTEGER,
    grounding_type VARCHAR(50),
    
    -- System ratings
    rated_current DECIMAL(10, 2),
    rated_power DECIMAL(12, 2),
    power_factor DECIMAL(4, 3),
    
    -- Standards compliance
    applicable_standards JSONB DEFAULT '{}',
    design_criteria JSONB DEFAULT '{}',
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    
    -- Foreign keys
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_system_name_length CHECK (length(name) >= 2),
    CONSTRAINT chk_system_type CHECK (system_type IN ('power', 'lighting', 'control', 'communication', 'fire_alarm', 'security')),
    CONSTRAINT chk_voltage_level CHECK (voltage_level IN ('120V', '240V', '480V', '600V', '4160V', '13.8kV', '69kV', '138kV')),
    CONSTRAINT chk_phases CHECK (phases IN (1, 3)),
    CONSTRAINT chk_frequency CHECK (frequency IN (50.0, 60.0)),
    CONSTRAINT chk_positive_ratings CHECK (rated_current >= 0 AND rated_power >= 0),
    CONSTRAINT chk_power_factor CHECK (power_factor BETWEEN 0.0 AND 1.0)
);

-- Indexes
CREATE INDEX idx_electrical_systems_project_id ON electrical_systems(project_id);
CREATE INDEX idx_electrical_systems_system_type ON electrical_systems(system_type);
CREATE INDEX idx_electrical_systems_voltage_level ON electrical_systems(voltage_level);
CREATE INDEX idx_electrical_systems_status ON electrical_systems(status);
```

### 4.2 Circuit and Load Analysis Schema

#### 4.2.1 Circuits Table
```sql
CREATE TABLE circuits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    system_id UUID NOT NULL,
    circuit_number VARCHAR(50) NOT NULL,
    circuit_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Circuit specifications
    circuit_type VARCHAR(50) NOT NULL,
    voltage DECIMAL(8, 2) NOT NULL,
    phases INTEGER NOT NULL,
    
    -- Protection and control
    protection_device_id UUID,
    control_device_id UUID,
    
    -- Circuit ratings
    rated_current DECIMAL(10, 2),
    continuous_current DECIMAL(10, 2),
    fault_current DECIMAL(12, 2),
    
    -- Load information
    connected_load DECIMAL(12, 2),
    demand_load DECIMAL(12, 2),
    demand_factor DECIMAL(4, 3),
    
    -- Cable and routing
    cable_type VARCHAR(100),
    cable_length DECIMAL(8, 2),
    conduit_type VARCHAR(100),
    routing_path TEXT,
    
    -- Calculations
    voltage_drop DECIMAL(6, 4),
    power_loss DECIMAL(10, 2),
    efficiency DECIMAL(5, 4),
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    
    -- Foreign keys
    FOREIGN KEY (system_id) REFERENCES electrical_systems(id) ON DELETE CASCADE,
    FOREIGN KEY (protection_device_id) REFERENCES components(id) ON DELETE SET NULL,
    FOREIGN KEY (control_device_id) REFERENCES components(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_circuit_number_length CHECK (length(circuit_number) >= 1),
    CONSTRAINT chk_circuit_name_length CHECK (length(circuit_name) >= 2),
    CONSTRAINT chk_circuit_type CHECK (circuit_type IN ('branch', 'feeder', 'main', 'sub_main', 'control', 'emergency')),
    CONSTRAINT chk_circuit_phases CHECK (phases IN (1, 3)),
    CONSTRAINT chk_positive_voltage CHECK (voltage > 0),
    CONSTRAINT chk_positive_current CHECK (rated_current >= 0 AND continuous_current >= 0),
    CONSTRAINT chk_positive_load CHECK (connected_load >= 0 AND demand_load >= 0),
    CONSTRAINT chk_demand_factor CHECK (demand_factor BETWEEN 0.0 AND 1.0),
    CONSTRAINT chk_efficiency CHECK (efficiency BETWEEN 0.0 AND 1.0),
    CONSTRAINT chk_voltage_drop CHECK (voltage_drop BETWEEN 0.0 AND 1.0),
    
    -- Unique constraint
    UNIQUE(system_id, circuit_number)
);

-- Indexes
CREATE INDEX idx_circuits_system_id ON circuits(system_id);
CREATE INDEX idx_circuits_circuit_number ON circuits(circuit_number);
CREATE INDEX idx_circuits_circuit_type ON circuits(circuit_type);
CREATE INDEX idx_circuits_protection_device_id ON circuits(protection_device_id);
CREATE INDEX idx_circuits_status ON circuits(status);
```

#### 4.2.2 Loads Table
```sql
CREATE TABLE loads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    circuit_id UUID NOT NULL,
    load_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Load characteristics
    load_type VARCHAR(50) NOT NULL,
    load_category VARCHAR(50) NOT NULL,
    
    -- Power ratings
    rated_power DECIMAL(12, 2) NOT NULL,
    rated_voltage DECIMAL(8, 2) NOT NULL,
    rated_current DECIMAL(10, 2),
    power_factor DECIMAL(4, 3),
    
    -- Operating characteristics
    duty_cycle DECIMAL(4, 3) DEFAULT 1.0,
    diversity_factor DECIMAL(4, 3) DEFAULT 1.0,
    demand_factor DECIMAL(4, 3) DEFAULT 1.0,
    
    -- Motor-specific data (if applicable)
    motor_data JSONB DEFAULT '{}',
    
    -- Lighting-specific data (if applicable)
    lighting_data JSONB DEFAULT '{}',
    
    -- HVAC-specific data (if applicable)
    hvac_data JSONB DEFAULT '{}',
    
    -- Location and installation
    location VARCHAR(200),
    installation_details TEXT,
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    
    -- Foreign keys
    FOREIGN KEY (circuit_id) REFERENCES circuits(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_load_name_length CHECK (length(load_name) >= 2),
    CONSTRAINT chk_load_type CHECK (load_type IN ('motor', 'lighting', 'hvac', 'receptacle', 'equipment', 'other')),
    CONSTRAINT chk_load_category CHECK (load_category IN ('continuous', 'non_continuous', 'intermittent', 'variable')),
    CONSTRAINT chk_positive_power CHECK (rated_power > 0),
    CONSTRAINT chk_positive_voltage CHECK (rated_voltage > 0),
    CONSTRAINT chk_positive_current CHECK (rated_current >= 0),
    CONSTRAINT chk_power_factor CHECK (power_factor BETWEEN 0.0 AND 1.0),
    CONSTRAINT chk_duty_cycle CHECK (duty_cycle BETWEEN 0.0 AND 1.0),
    CONSTRAINT chk_diversity_factor CHECK (diversity_factor BETWEEN 0.0 AND 1.0),
    CONSTRAINT chk_demand_factor CHECK (demand_factor BETWEEN 0.0 AND 1.0)
);

-- Indexes
CREATE INDEX idx_loads_circuit_id ON loads(circuit_id);
CREATE INDEX idx_loads_load_type ON loads(load_type);
CREATE INDEX idx_loads_load_category ON loads(load_category);
CREATE INDEX idx_loads_status ON loads(status);
CREATE INDEX idx_loads_location ON loads(location);
```

### 4.3 Cable and Conduit Management Schema

#### 4.3.1 Cable Types Table
```sql
CREATE TABLE cable_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Cable construction
    conductor_material VARCHAR(50) NOT NULL,
    insulation_type VARCHAR(50) NOT NULL,
    jacket_material VARCHAR(50),
    shielding_type VARCHAR(50),
    
    -- Physical properties
    conductor_size_range JSONB DEFAULT '{}',
    temperature_rating INTEGER NOT NULL,
    voltage_rating DECIMAL(8, 2) NOT NULL,
    
    -- Installation characteristics
    installation_type VARCHAR(50),
    suitable_locations JSONB DEFAULT '{}',
    
    -- Derating factors
    temperature_derating JSONB DEFAULT '{}',
    bundling_derating JSONB DEFAULT '{}',
    
    -- Standards compliance
    applicable_standards JSONB DEFAULT '{}',
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_cable_name_length CHECK (length(name) >= 2),
    CONSTRAINT chk_conductor_material CHECK (conductor_material IN ('copper', 'aluminum')),
    CONSTRAINT chk_insulation_type CHECK (insulation_type IN ('THHN', 'THWN', 'XHHW', 'USE', 'RHW', 'EPR', 'XLPE')),
    CONSTRAINT chk_temperature_rating CHECK (temperature_rating BETWEEN 60 AND 250),
    CONSTRAINT chk_positive_voltage_rating CHECK (voltage_rating > 0)
);

-- Sample cable types
INSERT INTO cable_types (name, description, conductor_material, insulation_type, temperature_rating, voltage_rating) VALUES
('THHN/THWN', 'Thermoplastic High Heat-resistant Nylon-coated', 'copper', 'THHN', 90, 600),
('XHHW', 'Cross-linked High Heat-resistant Water-resistant', 'copper', 'XHHW', 90, 600),
('USE', 'Underground Service Entrance', 'copper', 'USE', 75, 600);
```

#### 4.3.2 Conduit Types Table
```sql
CREATE TABLE conduit_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Conduit characteristics
    material VARCHAR(50) NOT NULL,
    construction_type VARCHAR(50) NOT NULL,
    
    -- Physical properties
    size_range JSONB DEFAULT '{}',
    wall_thickness JSONB DEFAULT '{}',
    
    -- Installation characteristics
    installation_method VARCHAR(50),
    suitable_locations JSONB DEFAULT '{}',
    
    -- Fill factors
    fill_factors JSONB DEFAULT '{}',
    
    -- Standards compliance
    applicable_standards JSONB DEFAULT '{}',
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_conduit_name_length CHECK (length(name) >= 2),
    CONSTRAINT chk_conduit_material CHECK (material IN ('steel', 'aluminum', 'pvc', 'hdpe', 'fiberglass')),
    CONSTRAINT chk_construction_type CHECK (construction_type IN ('rigid', 'flexible', 'liquidtight', 'emt', 'imc', 'rmc'))
);

-- Sample conduit types
INSERT INTO conduit_types (name, description, material, construction_type) VALUES
('EMT', 'Electrical Metallic Tubing', 'steel', 'emt'),
('RMC', 'Rigid Metal Conduit', 'steel', 'rigid'),
('PVC', 'Polyvinyl Chloride Conduit', 'pvc', 'rigid');
```

### 4.4 Calculation and Analysis Schema

#### 4.4.1 Calculation Templates Table
```sql
CREATE TABLE calculation_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    calculation_type VARCHAR(50) NOT NULL,
    
    -- Template definition
    input_parameters JSONB DEFAULT '{}',
    calculation_formula TEXT,
    validation_rules JSONB DEFAULT '{}',
    
    -- Standards compliance
    applicable_standards JSONB DEFAULT '{}',
    reference_documents JSONB DEFAULT '{}',
    
    -- Template metadata
    version VARCHAR(20) DEFAULT '1.0',
    created_by UUID,
    is_system_template BOOLEAN DEFAULT FALSE,
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_template_name_length CHECK (length(name) >= 2),
    CONSTRAINT chk_calculation_type CHECK (calculation_type IN ('voltage_drop', 'short_circuit', 'load_flow', 'arc_flash', 'thermal', 'mechanical')),
    CONSTRAINT chk_template_version CHECK (version ~ '^[0-9]+\.[0-9]+(\.[0-9]+)?$')
);

-- Sample calculation templates
INSERT INTO calculation_templates (name, description, calculation_type, input_parameters, is_system_template) VALUES
('AC Voltage Drop', 'Voltage drop calculation for AC circuits', 'voltage_drop', 
 '{"voltage": "number", "current": "number", "length": "number", "conductor_resistance": "number", "conductor_reactance": "number", "power_factor": "number"}', 
 TRUE),
('DC Voltage Drop', 'Voltage drop calculation for DC circuits', 'voltage_drop',
 '{"voltage": "number", "current": "number", "length": "number", "conductor_resistance": "number"}',
 TRUE);
```

#### 4.4.2 Calculations Table
```sql
CREATE TABLE calculations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id UUID NOT NULL,
    project_id UUID,
    circuit_id UUID,
    
    -- Calculation details
    calculation_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Input data
    input_values JSONB DEFAULT '{}',
    
    -- Results
    calculated_results JSONB DEFAULT '{}',
    
    -- Calculation metadata
    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    calculated_by UUID,
    reviewed_by UUID,
    review_date TIMESTAMP,
    
    -- Professional validation
    pe_approved BOOLEAN DEFAULT FALSE,
    pe_approved_by UUID,
    pe_approval_date TIMESTAMP,
    pe_stamp_number VARCHAR(50),
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (template_id) REFERENCES calculation_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (circuit_id) REFERENCES circuits(id) ON DELETE CASCADE,
    FOREIGN KEY (calculated_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (pe_approved_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_calculation_name_length CHECK (length(calculation_name) >= 2),
    CONSTRAINT chk_calculation_status CHECK (status IN ('draft', 'calculated', 'reviewed', 'approved', 'rejected')),
    CONSTRAINT chk_pe_approval CHECK (NOT pe_approved OR (pe_approved_by IS NOT NULL AND pe_approval_date IS NOT NULL))
);

-- Indexes
CREATE INDEX idx_calculations_template_id ON calculations(template_id);
CREATE INDEX idx_calculations_project_id ON calculations(project_id);
CREATE INDEX idx_calculations_circuit_id ON calculations(circuit_id);
CREATE INDEX idx_calculations_status ON calculations(status);
CREATE INDEX idx_calculations_pe_approved ON calculations(pe_approved);
CREATE INDEX idx_calculations_calculation_date ON calculations(calculation_date);
```

---

## 5. Activity Logging and Audit Schema

### 5.1 Activity Logs Table
```sql
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Activity identification
    activity_type VARCHAR(50) NOT NULL,
    activity_category VARCHAR(50) NOT NULL,
    
    -- User and session information
    user_id UUID,
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    
    -- Resource information
    resource_type VARCHAR(50),
    resource_id UUID,
    resource_name VARCHAR(200),
    
    -- Activity details
    activity_description TEXT,
    activity_data JSONB DEFAULT '{}',
    
    -- Changes tracking
    old_values JSONB DEFAULT '{}',
    new_values JSONB DEFAULT '{}',
    
    -- Context information
    project_id UUID,
    system_id UUID,
    
    -- Timing
    activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    duration_ms INTEGER,
    
    -- Status and results
    status VARCHAR(20) DEFAULT 'success',
    error_message TEXT,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (system_id) REFERENCES electrical_systems(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_activity_type CHECK (activity_type IN ('create', 'read', 'update', 'delete', 'calculate', 'approve', 'login', 'logout')),
    CONSTRAINT chk_activity_category CHECK (activity_category IN ('user', 'project', 'circuit', 'load', 'calculation', 'component', 'system', 'security')),
    CONSTRAINT chk_activity_status CHECK (status IN ('success', 'failure', 'warning', 'info')),
    CONSTRAINT chk_positive_duration CHECK (duration_ms >= 0)
);

-- Indexes
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_activity_type ON activity_logs(activity_type);
CREATE INDEX idx_activity_logs_activity_category ON activity_logs(activity_category);
CREATE INDEX idx_activity_logs_resource_type ON activity_logs(resource_type);
CREATE INDEX idx_activity_logs_project_id ON activity_logs(project_id);
CREATE INDEX idx_activity_logs_timestamp ON activity_logs(activity_timestamp);
CREATE INDEX idx_activity_logs_status ON activity_logs(status);

-- Partitioning by month for performance
CREATE TABLE activity_logs_y2025m07 PARTITION OF activity_logs
FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
```

### 5.2 Audit Trail Table
```sql
CREATE TABLE audit_trail (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Audit identification
    audit_type VARCHAR(50) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    
    -- Operation details
    operation VARCHAR(20) NOT NULL,
    old_data JSONB,
    new_data JSONB,
    changed_fields JSONB,
    
    -- User and context
    user_id UUID,
    session_id VARCHAR(100),
    ip_address INET,
    
    -- Professional validation
    requires_pe_review BOOLEAN DEFAULT FALSE,
    pe_reviewed BOOLEAN DEFAULT FALSE,
    pe_reviewed_by UUID,
    pe_review_date TIMESTAMP,
    
    -- Timing
    audit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (pe_reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_audit_type CHECK (audit_type IN ('data_change', 'calculation', 'approval', 'security', 'system')),
    CONSTRAINT chk_operation CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE', 'CALCULATE', 'APPROVE')),
    CONSTRAINT chk_pe_review CHECK (NOT pe_reviewed OR (pe_reviewed_by IS NOT NULL AND pe_review_date IS NOT NULL))
);

-- Indexes
CREATE INDEX idx_audit_trail_table_name ON audit_trail(table_name);
CREATE INDEX idx_audit_trail_record_id ON audit_trail(record_id);
CREATE INDEX idx_audit_trail_operation ON audit_trail(operation);
CREATE INDEX idx_audit_trail_user_id ON audit_trail(user_id);
CREATE INDEX idx_audit_trail_timestamp ON audit_trail(audit_timestamp);
CREATE INDEX idx_audit_trail_pe_reviewed ON audit_trail(pe_reviewed);
```

---

## 6. Performance Optimization

### 6.1 Indexing Strategy

#### 6.1.1 Primary Indexes
```sql
-- Core performance indexes
CREATE INDEX CONCURRENTLY idx_users_active_email ON users(email) WHERE is_active = TRUE;
CREATE INDEX CONCURRENTLY idx_components_active_type ON components(type_id) WHERE status = 'active';
CREATE INDEX CONCURRENTLY idx_circuits_active_system ON circuits(system_id) WHERE status = 'active';
CREATE INDEX CONCURRENTLY idx_calculations_active_project ON calculations(project_id) WHERE status IN ('approved', 'reviewed');
```

#### 6.1.2 Composite Indexes
```sql
-- Multi-column indexes for common queries
CREATE INDEX CONCURRENTLY idx_projects_engineer_status ON projects(engineer_id, status);
CREATE INDEX CONCURRENTLY idx_circuits_system_type ON circuits(system_id, circuit_type);
CREATE INDEX CONCURRENTLY idx_loads_circuit_type ON loads(circuit_id, load_type);
CREATE INDEX CONCURRENTLY idx_calculations_project_status ON calculations(project_id, status);
```

#### 6.1.3 Partial Indexes
```sql
-- Partial indexes for specific use cases
CREATE INDEX CONCURRENTLY idx_users_verified_active ON users(email) WHERE is_verified = TRUE AND is_active = TRUE;
CREATE INDEX CONCURRENTLY idx_projects_active_engineer ON projects(engineer_id) WHERE status = 'active';
CREATE INDEX CONCURRENTLY idx_calculations_pending_approval ON calculations(calculated_by) WHERE status = 'reviewed' AND pe_approved = FALSE;
```

### 6.2 Query Optimization

#### 6.2.1 Common Query Patterns
```sql
-- Optimized project dashboard query
SELECT p.id, p.name, p.status, p.phase, e.first_name, e.last_name,
       COUNT(s.id) as system_count,
       COUNT(c.id) as circuit_count
FROM projects p
LEFT JOIN users e ON p.engineer_id = e.id
LEFT JOIN electrical_systems s ON p.id = s.project_id
LEFT JOIN circuits c ON s.id = c.system_id
WHERE p.status = 'active'
GROUP BY p.id, p.name, p.status, p.phase, e.first_name, e.last_name
ORDER BY p.updated_at DESC;

-- Optimized circuit load summary
SELECT c.id, c.circuit_number, c.circuit_name,
       SUM(l.rated_power) as total_connected_load,
       SUM(l.rated_power * l.demand_factor) as total_demand_load,
       COUNT(l.id) as load_count
FROM circuits c
LEFT JOIN loads l ON c.id = l.circuit_id
WHERE c.system_id = $1 AND c.status = 'active'
GROUP BY c.id, c.circuit_number, c.circuit_name
ORDER BY c.circuit_number;
```

#### 6.2.2 View Definitions
```sql
-- Project summary view
CREATE VIEW project_summary AS
SELECT p.id, p.name, p.project_number, p.status, p.phase,
       e.first_name || ' ' || e.last_name as engineer_name,
       COUNT(DISTINCT s.id) as system_count,
       COUNT(DISTINCT c.id) as circuit_count,
       COUNT(DISTINCT l.id) as load_count,
       SUM(l.rated_power) as total_connected_load,
       p.created_at, p.updated_at
FROM projects p
LEFT JOIN users e ON p.engineer_id = e.id
LEFT JOIN electrical_systems s ON p.id = s.project_id
LEFT JOIN circuits c ON s.id = c.system_id
LEFT JOIN loads l ON c.id = l.circuit_id
GROUP BY p.id, p.name, p.project_number, p.status, p.phase, e.first_name, e.last_name, p.created_at, p.updated_at;

-- Circuit load summary view
CREATE VIEW circuit_load_summary AS
SELECT c.id, c.system_id, c.circuit_number, c.circuit_name,
       c.rated_current, c.voltage,
       COUNT(l.id) as load_count,
       SUM(l.rated_power) as connected_load,
       SUM(l.rated_power * l.demand_factor) as demand_load,
       AVG(l.power_factor) as avg_power_factor,
       c.voltage_drop, c.power_loss
FROM circuits c
LEFT JOIN loads l ON c.id = l.circuit_id
WHERE c.status = 'active'
GROUP BY c.id, c.system_id, c.circuit_number, c.circuit_name, c.rated_current, c.voltage, c.voltage_drop, c.power_loss;
```

### 6.3 Database Maintenance

#### 6.3.1 Routine Maintenance Tasks
```sql
-- Analyze table statistics (run weekly)
ANALYZE users;
ANALYZE projects;
ANALYZE electrical_systems;
ANALYZE circuits;
ANALYZE loads;
ANALYZE calculations;
ANALYZE activity_logs;

-- Vacuum tables (run monthly)
VACUUM ANALYZE users;
VACUUM ANALYZE projects;
VACUUM ANALYZE electrical_systems;
VACUUM ANALYZE circuits;
VACUUM ANALYZE loads;
VACUUM ANALYZE calculations;

-- Reindex tables (run quarterly)
REINDEX TABLE users;
REINDEX TABLE projects;
REINDEX TABLE electrical_systems;
REINDEX TABLE circuits;
REINDEX TABLE loads;
REINDEX TABLE calculations;
```

#### 6.3.2 Monitoring Queries
```sql
-- Monitor database size
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Monitor index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- Monitor slow queries
SELECT query, calls, total_time, mean_time, rows
FROM pg_stat_statements
WHERE mean_time > 1000
ORDER BY mean_time DESC;
```

---

## 7. Security and Access Control

### 7.1 Row-Level Security

#### 7.1.1 User Data Access
```sql
-- Enable row-level security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE electrical_systems ENABLE ROW LEVEL SECURITY;
ALTER TABLE circuits ENABLE ROW LEVEL SECURITY;
ALTER TABLE calculations ENABLE ROW LEVEL SECURITY;

-- Project access policy
CREATE POLICY project_access_policy ON projects
    FOR ALL TO app_user
    USING (
        engineer_id = current_user_id() OR
        created_by = current_user_id() OR
        current_user_has_role('admin')
    );

-- System access policy
CREATE POLICY system_access_policy ON electrical_systems
    FOR ALL TO app_user
    USING (
        project_id IN (
            SELECT id FROM projects WHERE 
            engineer_id = current_user_id() OR
            created_by = current_user_id() OR
            current_user_has_role('admin')
        )
    );
```

#### 7.1.2 Security Functions
```sql
-- Current user ID function
CREATE OR REPLACE FUNCTION current_user_id()
RETURNS UUID AS $$
BEGIN
    RETURN (SELECT id FROM users WHERE username = current_user);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- User role check function
CREATE OR REPLACE FUNCTION current_user_has_role(role_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_role_assignments ura
        JOIN user_roles ur ON ura.role_id = ur.id
        WHERE ura.user_id = current_user_id() AND ur.name = role_name
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 7.2 Data Encryption

#### 7.2.1 Sensitive Data Fields
```sql
-- Encrypted columns for sensitive data
ALTER TABLE users ADD COLUMN ssn_encrypted BYTEA;
ALTER TABLE projects ADD COLUMN client_notes_encrypted BYTEA;
ALTER TABLE calculations ADD COLUMN proprietary_data_encrypted BYTEA;

-- Encryption functions
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(data TEXT, key_id TEXT)
RETURNS BYTEA AS $$
BEGIN
    RETURN pgp_sym_encrypt(data, key_id);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION decrypt_sensitive_data(encrypted_data BYTEA, key_id TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN pgp_sym_decrypt(encrypted_data, key_id);
END;
$$ LANGUAGE plpgsql;
```

---

## 8. Backup and Recovery

### 8.1 Backup Strategy

#### 8.1.1 Full Database Backup
```bash
#!/bin/bash
# Full database backup script
BACKUP_DIR="/var/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
DATABASE="ultimate_electrical_designer"

# Create backup directory
mkdir -p $BACKUP_DIR

# Full backup
pg_dump -h localhost -U postgres -d $DATABASE -f $BACKUP_DIR/full_backup_$DATE.sql

# Compressed backup
pg_dump -h localhost -U postgres -d $DATABASE | gzip > $BACKUP_DIR/full_backup_$DATE.sql.gz

# Verify backup
echo "Backup created: $BACKUP_DIR/full_backup_$DATE.sql.gz"
echo "Backup size: $(ls -lh $BACKUP_DIR/full_backup_$DATE.sql.gz | awk '{print $5}')"
```

#### 8.1.2 Incremental Backup
```bash
#!/bin/bash
# Incremental backup using WAL archiving
BACKUP_DIR="/var/backups/postgresql/wal"
DATE=$(date +%Y%m%d_%H%M%S)

# Archive WAL files
pg_basebackup -h localhost -U postgres -D $BACKUP_DIR/base_backup_$DATE -P -v -W

# Archive configuration
echo "archive_mode = on" >> /etc/postgresql/15/main/postgresql.conf
echo "archive_command = 'cp %p $BACKUP_DIR/wal_archive/%f'" >> /etc/postgresql/15/main/postgresql.conf
```

### 8.2 Recovery Procedures

#### 8.2.1 Point-in-Time Recovery
```bash
#!/bin/bash
# Point-in-time recovery script
BACKUP_DIR="/var/backups/postgresql"
RECOVERY_TARGET="2025-07-16 14:30:00"

# Stop PostgreSQL
sudo systemctl stop postgresql

# Restore base backup
sudo rm -rf /var/lib/postgresql/15/main/*
sudo tar -xzf $BACKUP_DIR/base_backup_latest.tar.gz -C /var/lib/postgresql/15/main/

# Create recovery configuration
sudo tee /var/lib/postgresql/15/main/recovery.conf <<EOF
restore_command = 'cp $BACKUP_DIR/wal_archive/%f %p'
recovery_target_time = '$RECOVERY_TARGET'
recovery_target_action = 'promote'
EOF

# Start PostgreSQL
sudo systemctl start postgresql
```

---

## 9. Monitoring and Maintenance

### 9.1 Performance Monitoring

#### 9.1.1 Database Statistics
```sql
-- Database connection monitoring
SELECT datname, numbackends, xact_commit, xact_rollback, 
       blks_read, blks_hit, tup_returned, tup_fetched, tup_inserted, tup_updated, tup_deleted
FROM pg_stat_database
WHERE datname = 'ultimate_electrical_designer';

-- Table statistics
SELECT schemaname, tablename, seq_scan, seq_tup_read, idx_scan, idx_tup_fetch,
       n_tup_ins, n_tup_upd, n_tup_del, n_live_tup, n_dead_tup
FROM pg_stat_user_tables
ORDER BY seq_scan DESC;

-- Index usage statistics
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
WHERE idx_scan > 0
ORDER BY idx_scan DESC;
```

#### 9.1.2 Query Performance
```sql
-- Top 10 slowest queries
SELECT query, calls, total_time, mean_time, rows, 
       stddev_time, min_time, max_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Queries with highest I/O
SELECT query, calls, total_time, mean_time,
       shared_blks_hit, shared_blks_read, shared_blks_written
FROM pg_stat_statements
ORDER BY (shared_blks_read + shared_blks_written) DESC
LIMIT 10;
```

### 9.2 Maintenance Scripts

#### 9.2.1 Daily Maintenance
```sql
-- Daily maintenance script
DO $$
BEGIN
    -- Update table statistics
    ANALYZE users;
    ANALYZE projects;
    ANALYZE electrical_systems;
    ANALYZE circuits;
    ANALYZE loads;
    ANALYZE calculations;
    
    -- Log maintenance completion
    INSERT INTO activity_logs (activity_type, activity_category, activity_description)
    VALUES ('maintenance', 'system', 'Daily maintenance completed');
END $$;
```

#### 9.2.2 Weekly Maintenance
```sql
-- Weekly maintenance script
DO $$
BEGIN
    -- Vacuum tables
    VACUUM ANALYZE users;
    VACUUM ANALYZE projects;
    VACUUM ANALYZE electrical_systems;
    VACUUM ANALYZE circuits;
    VACUUM ANALYZE loads;
    VACUUM ANALYZE calculations;
    
    -- Clean old activity logs (older than 1 year)
    DELETE FROM activity_logs 
    WHERE activity_timestamp < NOW() - INTERVAL '1 year';
    
    -- Log maintenance completion
    INSERT INTO activity_logs (activity_type, activity_category, activity_description)
    VALUES ('maintenance', 'system', 'Weekly maintenance completed');
END $$;
```

---

## 10. Troubleshooting Guide

### 10.1 Common Issues

#### 10.1.1 Connection Issues
```sql
-- Check active connections
SELECT datname, usename, client_addr, state, query_start, query
FROM pg_stat_activity
WHERE datname = 'ultimate_electrical_designer';

-- Check connection limits
SELECT name, setting FROM pg_settings WHERE name = 'max_connections';
SELECT count(*) FROM pg_stat_activity;

-- Kill long-running queries
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'active' AND query_start < NOW() - INTERVAL '5 minutes';
```

#### 10.1.2 Performance Issues
```sql
-- Check for blocking queries
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS blocking_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- Check table sizes
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
       pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 10.2 Emergency Procedures

#### 10.2.1 Database Recovery
```bash
#!/bin/bash
# Emergency database recovery
echo "Starting emergency database recovery..."

# Stop application
sudo systemctl stop electrical-designer-api

# Stop database
sudo systemctl stop postgresql

# Restore from latest backup
sudo -u postgres pg_restore -d ultimate_electrical_designer /var/backups/postgresql/latest_backup.sql

# Start database
sudo systemctl start postgresql

# Verify database integrity
sudo -u postgres psql -d ultimate_electrical_designer -c "SELECT COUNT(*) FROM users;"

# Start application
sudo systemctl start electrical-designer-api

echo "Emergency recovery completed"
```

---

## 11. Conclusion

This database schema documentation provides a comprehensive foundation for the Ultimate Electrical Designer application. The schema is designed to support professional electrical engineering workflows with complete data integrity, performance optimization, and security measures.

The database structure supports:
- Complete project lifecycle management
- Professional electrical engineering calculations
- Standards compliance tracking
- Comprehensive audit trails
- Performance-optimized query patterns
- Security and access control
- Backup and recovery procedures

All database operations are designed to maintain the highest standards of data integrity and professional engineering requirements.

---

**Document Control**
- **Document Owner**: Database Administrator
- **Review Authority**: Technical Lead, Database Administrator
- **Approval Authority**: Technical Lead
- **Review Frequency**: Monthly
- **Next Review Date**: [Month + 1]

**Version History**
- **v1.0** (July 2025): Initial database schema documentation
- **[Future versions will be tracked here]**