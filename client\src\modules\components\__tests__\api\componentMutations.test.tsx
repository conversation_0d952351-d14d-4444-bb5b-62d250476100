/**
 * Component React Query Mutation Hooks Tests
 * Tests the React Query mutation hooks including CRUD operations, optimistic updates,
 * cache invalidation, error handling, and integration with global error handling
 */

import {
    createMockComponent,
    mockApiError,
    mockApiSuccess,
    mockComponent,
    mockComponentCreate
} from '@/test/factories/componentFactories';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
    useBulkCreateComponents,
    useBulkDeleteComponents,
    useBulkUpdateComponents,
    useCreateComponent,
    useDeleteComponent,
    useOptimisticComponentUpdate,
    usePrefetchComponent,
    useUpdateComponent
} from '../../api/componentMutations';

// Mock the API client
vi.mock('../../api/componentApi', () => ({
  componentApi: {
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    bulkCreate: vi.fn(),
    bulkUpdate: vi.fn(),
    bulkDelete: vi.fn(),
    getById: vi.fn(),
    list: vi.fn(),
  },
}));

// Create test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const createWrapper = () => {
  const queryClient = createTestQueryClient();
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Component Mutation Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useCreateComponent', () => {
    it('creates component successfully', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiSuccess(mockComponent));

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockComponent);
      expect(componentApi.create).toHaveBeenCalledWith(mockComponentCreate);
    });

    it('handles create errors', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiError('Creation failed'));

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Creation failed');
    });

    it('invalidates cache on successful creation', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiSuccess(mockComponent));

      const queryClient = createTestQueryClient();
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');
      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });
      expect(setQueryDataSpy).toHaveBeenCalledWith(['component', mockComponent.id], mockComponent);
    });

    it('calls custom onSuccess callback', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiSuccess(mockComponent));

      const onSuccess = vi.fn();
      const { result } = renderHook(
        () => useCreateComponent({ onSuccess }),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(onSuccess).toHaveBeenCalledWith(mockComponent, mockComponentCreate, expect.any(Object));
    });

    it('calls custom onError callback', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiError('Creation failed'));

      const onError = vi.fn();
      const { result } = renderHook(
        () => useCreateComponent({ onError }),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Creation failed' }),
        mockComponentCreate,
        expect.any(Object)
      );
    });
  });

  describe('useUpdateComponent', () => {
    it('updates component successfully', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const updatedComponent = createMockComponent({ name: 'Updated Component' });
      vi.mocked(componentApi.update).mockResolvedValue(mockApiSuccess(updatedComponent));

      const { result } = renderHook(
        () => useUpdateComponent(),
        { wrapper: createWrapper() }
      );

      const updateData = { id: 1, component: { name: 'Updated Component' } };
      result.current.mutate(updateData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(updatedComponent);
      expect(componentApi.update).toHaveBeenCalledWith(1, { name: 'Updated Component' });
    });

    it('handles update errors', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.update).mockResolvedValue(mockApiError('Update failed'));

      const { result } = renderHook(
        () => useUpdateComponent(),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ id: 1, component: { name: 'Updated' } });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Update failed');
    });

    it('updates cache on successful update', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const updatedComponent = createMockComponent({ name: 'Updated Component' });
      vi.mocked(componentApi.update).mockResolvedValue(mockApiSuccess(updatedComponent));

      const queryClient = createTestQueryClient();
      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useUpdateComponent(),
        { wrapper }
      );

      result.current.mutate({ id: 1, component: { name: 'Updated Component' } });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(setQueryDataSpy).toHaveBeenCalledWith(['component', 1], updatedComponent);
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });
    });
  });

  describe('useDeleteComponent', () => {
    it('deletes component successfully', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.delete).mockResolvedValue(mockApiSuccess(undefined));

      const { result } = renderHook(
        () => useDeleteComponent(),
        { wrapper: createWrapper() }
      );

      result.current.mutate(1);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(componentApi.delete).toHaveBeenCalledWith(1);
    });

    it('handles delete errors', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.delete).mockResolvedValue(mockApiError('Delete failed'));

      const { result } = renderHook(
        () => useDeleteComponent(),
        { wrapper: createWrapper() }
      );

      result.current.mutate(1);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Delete failed');
    });

    it('removes component from cache on successful delete', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.delete).mockResolvedValue(mockApiSuccess(undefined));

      const queryClient = createTestQueryClient();
      const removeQueriesSpy = vi.spyOn(queryClient, 'removeQueries');
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useDeleteComponent(),
        { wrapper }
      );

      result.current.mutate(1);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(removeQueriesSpy).toHaveBeenCalledWith({ queryKey: ['component', 1] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });
    });
  });

  describe('useBulkCreateComponents', () => {
    it('creates multiple components successfully', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const bulkResults = [
        { is_valid: true, component: mockComponent },
        { is_valid: true, component: createMockComponent({ id: 2 }) },
      ];
      vi.mocked(componentApi.bulkCreate).mockResolvedValue(mockApiSuccess(bulkResults));

      const { result } = renderHook(
        () => useBulkCreateComponents(),
        { wrapper: createWrapper() }
      );

      const bulkData = {
        components: [mockComponentCreate, mockComponentCreate],
        validate_duplicates: true,
      };

      result.current.mutate(bulkData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(bulkResults);
      expect(componentApi.bulkCreate).toHaveBeenCalledWith(bulkData);
    });

    it('handles bulk create errors', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.bulkCreate).mockResolvedValue(mockApiError('Bulk create failed'));

      const { result } = renderHook(
        () => useBulkCreateComponents(),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ components: [mockComponentCreate] });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Bulk create failed');
    });

    it('invalidates cache on successful bulk create', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const bulkResults = [{ is_valid: true, component: mockComponent }];
      vi.mocked(componentApi.bulkCreate).mockResolvedValue(mockApiSuccess(bulkResults));

      const queryClient = createTestQueryClient();
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useBulkCreateComponents(),
        { wrapper }
      );

      result.current.mutate({ components: [mockComponentCreate] });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });
    });
  });

  describe('useBulkUpdateComponents', () => {
    it('updates multiple components successfully', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const bulkResults = [
        { is_valid: true, component: mockComponent },
        { is_valid: true, component: createMockComponent({ id: 2 }) },
      ];
      vi.mocked(componentApi.bulkUpdate).mockResolvedValue(mockApiSuccess(bulkResults));

      const { result } = renderHook(
        () => useBulkUpdateComponents(),
        { wrapper: createWrapper() }
      );

      const bulkData = {
        component_ids: [1, 2],
        update_data: { is_preferred: true },
      };

      result.current.mutate(bulkData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(bulkResults);
      expect(componentApi.bulkUpdate).toHaveBeenCalledWith(bulkData);
    });

    it('handles bulk update errors', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.bulkUpdate).mockResolvedValue(mockApiError('Bulk update failed'));

      const { result } = renderHook(
        () => useBulkUpdateComponents(),
        { wrapper: createWrapper() }
      );

      result.current.mutate({
        component_ids: [1, 2],
        update_data: { is_preferred: true },
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Bulk update failed');
    });

    it('invalidates specific components and lists on successful bulk update', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const bulkResults = [{ is_valid: true, component: mockComponent }];
      vi.mocked(componentApi.bulkUpdate).mockResolvedValue(mockApiSuccess(bulkResults));

      const queryClient = createTestQueryClient();
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useBulkUpdateComponents(),
        { wrapper }
      );

      result.current.mutate({
        component_ids: [1, 2],
        update_data: { is_preferred: true },
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 1] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 2] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });
    });
  });

  describe('useBulkDeleteComponents', () => {
    it('deletes multiple components successfully', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const deleteResult = { deleted_count: 2, errors: [] };
      vi.mocked(componentApi.bulkDelete).mockResolvedValue(mockApiSuccess(deleteResult));

      const { result } = renderHook(
        () => useBulkDeleteComponents(),
        { wrapper: createWrapper() }
      );

      const deleteData = {
        componentIds: [1, 2],
        soft_delete: false,
      };

      result.current.mutate(deleteData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(deleteResult);
      expect(componentApi.bulkDelete).toHaveBeenCalledWith([1, 2], { soft_delete: false });
    });

    it('handles bulk delete errors', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.bulkDelete).mockResolvedValue(mockApiError('Bulk delete failed'));

      const { result } = renderHook(
        () => useBulkDeleteComponents(),
        { wrapper: createWrapper() }
      );

      result.current.mutate({
        componentIds: [1, 2],
        soft_delete: true,
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Bulk delete failed');
    });

    it('removes components from cache on successful bulk delete', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const deleteResult = { deleted_count: 2, errors: [] };
      vi.mocked(componentApi.bulkDelete).mockResolvedValue(mockApiSuccess(deleteResult));

      const queryClient = createTestQueryClient();
      const removeQueriesSpy = vi.spyOn(queryClient, 'removeQueries');
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useBulkDeleteComponents(),
        { wrapper }
      );

      result.current.mutate({
        componentIds: [1, 2],
        soft_delete: false,
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(removeQueriesSpy).toHaveBeenCalledWith({ queryKey: ['component', 1] });
      expect(removeQueriesSpy).toHaveBeenCalledWith({ queryKey: ['component', 2] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });
    });
  });

  describe('useOptimisticComponentUpdate', () => {
    it('updates component optimistically', () => {
      const queryClient = createTestQueryClient();
      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useOptimisticComponentUpdate(),
        { wrapper }
      );

      const updates = { name: 'Optimistically Updated' };
      result.current.updateComponent(1, updates);

      expect(setQueryDataSpy).toHaveBeenCalledWith(
        ['component', 1],
        expect.any(Function)
      );
    });

    it('reverts component on error', () => {
      const queryClient = createTestQueryClient();
      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useOptimisticComponentUpdate(),
        { wrapper }
      );

      result.current.revertComponent(1, mockComponent);

      expect(setQueryDataSpy).toHaveBeenCalledWith(['component', 1], mockComponent);
    });

    it('handles missing component data gracefully', () => {
      const queryClient = createTestQueryClient();
      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useOptimisticComponentUpdate(),
        { wrapper }
      );

      // Mock the updater function to simulate missing data
      setQueryDataSpy.mockImplementation((key, updater) => {
        if (typeof updater === 'function') {
          return updater(undefined);
        }
        return updater;
      });

      const updates = { name: 'Updated' };
      result.current.updateComponent(1, updates);

      expect(setQueryDataSpy).toHaveBeenCalled();
    });
  });

  describe('usePrefetchComponent', () => {
    it('prefetches component data', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.getById).mockResolvedValue(mockApiSuccess(mockComponent));

      const queryClient = createTestQueryClient();
      const prefetchQuerySpy = vi.spyOn(queryClient, 'prefetchQuery');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => usePrefetchComponent(),
        { wrapper }
      );

      result.current.prefetchComponent(1);

      expect(prefetchQuerySpy).toHaveBeenCalledWith({
        queryKey: ['component', 1],
        queryFn: expect.any(Function),
        staleTime: 10 * 60 * 1000,
      });
    });

    it('prefetches components list', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const mockPaginatedResponse = {
        items: [mockComponent],
        pagination: { page: 1, size: 20, total: 1, pages: 1 },
      };
      vi.mocked(componentApi.list).mockResolvedValue(mockApiSuccess(mockPaginatedResponse));

      const queryClient = createTestQueryClient();
      const prefetchQuerySpy = vi.spyOn(queryClient, 'prefetchQuery');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => usePrefetchComponent(),
        { wrapper }
      );

      const params = { page: 1, size: 20 };
      result.current.prefetchComponents(params);

      expect(prefetchQuerySpy).toHaveBeenCalledWith({
        queryKey: ['components', 'list', params],
        queryFn: expect.any(Function),
        staleTime: 5 * 60 * 1000,
      });
    });

    it('handles prefetch errors gracefully', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.getById).mockResolvedValue(mockApiError('Prefetch failed'));

      const queryClient = createTestQueryClient();
      const prefetchQuerySpy = vi.spyOn(queryClient, 'prefetchQuery');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => usePrefetchComponent(),
        { wrapper }
      );

      result.current.prefetchComponent(1);

      expect(prefetchQuerySpy).toHaveBeenCalled();
    });
  });

  describe('Retry Logic and Error Handling', () => {
    it('respects custom retry configuration', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create)
        .mockResolvedValueOnce(mockApiError('Network error'))
        .mockResolvedValueOnce(mockApiSuccess(mockComponent));

      const { result } = renderHook(
        () => useCreateComponent({ retry: 1 }),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(componentApi.create).toHaveBeenCalledTimes(2);
    });

    it('handles network errors appropriately', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Network error');
    });

    it('handles API response errors correctly', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue({
        data: null,
        error: { detail: 'Validation failed', code: 'VALIDATION_ERROR' },
      });

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Validation failed');
    });
  });

  describe('Integration with Global Error Handling', () => {
    it('propagates errors to global error handlers', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiError('Global error'));

      const globalErrorHandler = vi.fn();
      const { result } = renderHook(
        () => useCreateComponent({ onError: globalErrorHandler }),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(globalErrorHandler).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Global error' }),
        mockComponentCreate,
        expect.any(Object)
      );
    });

    it('allows error handling to be overridden per mutation', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiError('Custom error'));

      const globalErrorHandler = vi.fn();
      const customErrorHandler = vi.fn();

      const { result } = renderHook(
        () => useCreateComponent({ onError: globalErrorHandler }),
        { wrapper: createWrapper() }
      );

      result.current.mutate(mockComponentCreate, {
        onError: customErrorHandler,
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(customErrorHandler).toHaveBeenCalled();
      expect(globalErrorHandler).not.toHaveBeenCalled();
    });
  });

  describe('Cache Invalidation Patterns', () => {
    it('invalidates related queries on component creation', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockResolvedValue(mockApiSuccess(mockComponent));

      const queryClient = createTestQueryClient();
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');
      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper }
      );

      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Should invalidate lists and stats
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });

      // Should set the new component in cache
      expect(setQueryDataSpy).toHaveBeenCalledWith(['component', mockComponent.id], mockComponent);
    });

    it('properly manages cache for bulk operations', async () => {
      const { componentApi } = await import('../../api/componentApi');
      const bulkResults = [
        { is_valid: true, component: mockComponent },
        { is_valid: true, component: createMockComponent({ id: 2 }) },
      ];
      vi.mocked(componentApi.bulkUpdate).mockResolvedValue(mockApiSuccess(bulkResults));

      const queryClient = createTestQueryClient();
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useBulkUpdateComponents(),
        { wrapper }
      );

      result.current.mutate({
        component_ids: [1, 2],
        update_data: { is_preferred: true },
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Should invalidate each component individually
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 1] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 2] });

      // Should invalidate lists and stats
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components'] });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['components', 'stats'] });
    });
  });

  describe('Mutation State Management', () => {
    it('tracks mutation loading states correctly', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockApiSuccess(mockComponent)), 100))
      );

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper: createWrapper() }
      );

      expect(result.current.isPending).toBe(false);

      result.current.mutate(mockComponentCreate);

      expect(result.current.isPending).toBe(true);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.isPending).toBe(false);
    });

    it('resets mutation state between calls', async () => {
      const { componentApi } = await import('../../api/componentApi');
      vi.mocked(componentApi.create)
        .mockResolvedValueOnce(mockApiError('First error'))
        .mockResolvedValueOnce(mockApiSuccess(mockComponent));

      const { result } = renderHook(
        () => useCreateComponent(),
        { wrapper: createWrapper() }
      );

      // First mutation - should fail
      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      // Reset and try again
      result.current.reset();
      expect(result.current.isError).toBe(false);
      expect(result.current.error).toBe(null);

      // Second mutation - should succeed
      result.current.mutate(mockComponentCreate);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });
    });
  });
});
