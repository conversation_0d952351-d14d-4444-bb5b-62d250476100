### 3. Workflow: Updating an Existing Module

This workflow focuses on modifying or enhancing an already implemented module.

* **3.1. Discovery & Analysis:**
    * **Task:** Analyze the current functionality of the module and identify specific areas that require updates or refinement. Consider any internal or external dependencies affected by the update.
    * **Guidance for AI:** Review existing module documentation and code to understand the scope of the update. Identify potential impacts on other system components.
* **3.2. Task Planning:**
    * **Task:** Break down the update tasks into smaller units, prioritizing non-breaking changes, performance improvements, or bug fixes. Utilize the "Task Planning Template".
    * **Guidance for AI:** Create a detailed plan for modifying the module, ensuring adherence to the "5-Phase Methodology".
* **3.3. Implementation:**
    * **Task:** Apply the necessary updates to the module, ensuring continued adherence to the 5-layer architecture and "unified patterns". Address any identified technical debt.
    * **Guidance for AI:** Generate code modifications. Ensure all "Zero Tolerance Policies" for warnings, errors, and technical debt are adhered to.
* **3.4. Verification:**
    * **Task:** Rerun all relevant tests for the module and add new tests for updated functionalities. Ensure zero warnings/errors are introduced. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Execute unit and integration tests for the module. Verify "100% test pass rates" and analyze code quality metrics to ensure no regressions.
* **3.5. Documentation & Handover:**
    * **Task:** Update the module documentation and any relevant calculation reports.
    * **Guidance for AI:** Generate or update documentation reflecting the changes and improvements made to the module.