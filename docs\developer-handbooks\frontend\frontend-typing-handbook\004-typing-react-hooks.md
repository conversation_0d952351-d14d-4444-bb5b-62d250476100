# **Typing React Hooks (useState, useEffect, Custom Hooks)**

This guide focuses on how to effectively apply TypeScript to React's built-in hooks (useState, useEffect, useRef, useContext) and, most importantly, to your application's custom hooks.

## **1. Typing useState**

useState is usually good at inferring types, but explicit typing is useful for initial null states or complex objects.

- **Type Inference:** TypeScript often infers the type correctly from the initial value.  
  // Type is inferred as string  
  const \[projectName, setProjectName\] = useState('New Project');  
    
  // Type is inferred as number  
  const \[cableLength, setCableLength\] = useState(0);  
    
  // Type is inferred as boolean  
  const \[isSaving, setIsSaving\] = useState(false);

- **Explicit Type for Initial null or undefined:** When the initial state might be null or undefined, but the actual state will be a specific type, explicitly define the union type.  
  interface ProjectDetails {  
  id: string;  
  name: string;  
  status: 'Active' \| 'Completed';  
  }  
    
  // State can be ProjectDetails or null  
  const \[currentProject, setCurrentProject\] = useState\<ProjectDetails \| null\>(null);  
    
  // State can be a string or undefined  
  const \[selectedComponentId, setSelectedComponentId\] = useState\<string \| undefined\>(undefined);

- **Typing Complex Objects:** When the initial state is an empty or partial object, explicitly type it to ensure all properties are covered.  
  interface CircuitConfig {  
  name: string;  
  voltage: number;  
  isMain: boolean;  
  }  
    
  const \[circuit, setCircuit\] = useState\<CircuitConfig\>({  
  name: '',  
  voltage: 400,  
  isMain: false,  
  });

- **Why:** Ensures that the state variable and its setter function are type-safe throughout the component's lifecycle, preventing runtime errors related to incorrect data access or assignments.

## **2. Typing useEffect**

useEffect itself doesn't typically require explicit type annotations, but ensure its dependencies and cleanup functions are correctly typed.

- **Dependencies Array:** Ensure all dependencies are correctly listed and typed. TypeScript will often warn if a dependency is missing.

- **Cleanup Function:** The function returned by useEffect for cleanup is implicitly typed as void or () =\> void.  
  import React, { useState, useEffect } from 'react';  
    
  interface ProjectComponent {  
  id: string;  
  status: 'online' \| 'offline';  
  }  
    
  interface ProjectMonitorProps {  
  projectId: string;  
  }  
    
  const ProjectMonitor: React.FC\<ProjectMonitorProps\> = ({ projectId }) =\> {  
  const \[components, setComponents\] = useState\<ProjectComponent\[\]\>(\[\]);  
  const \[error, setError\] = useState\<string \| null\>(null);  
    
  useEffect(() =\> {  
  let isMounted = true; // Flag for cleanup  
    
  async function fetchProjectComponents() {  
  try {  
  // Simulate API call  
  const response = await new Promise\<ProjectComponent\[\]\>(resolve =\>  
  setTimeout(() =\> resolve(\[  
  { id: 'heater-001', status: 'online' },  
  { id: 'sensor-002', status: 'offline' }  
  \]), 500)  
  );  
  if (isMounted) {  
  setComponents(response);  
  }  
  } catch (err) {  
  if (isMounted) {  
  setError("Failed to load components.");  
  }  
  }  
  }  
    
  fetchProjectComponents();  
    
  // Cleanup function  
  return () =\> {  
  isMounted = false; // Prevent state updates on unmounted component  
  console.log('Cleanup for ProjectMonitor');  
  };  
  }, \[projectId\]); // Dependency on projectId  
  // ... rest of component  
  return (  
  \<div\>  
  \<h2\>Monitoring Project: {projectId}\</h2\>  
  {error && \<p className="text-red-500"\>{error}\</p\>}  
  \<ul\>  
  {components.map(comp =\> (  
  \<li key={comp.id}\>{comp.id} - {comp.status}\</li\>  
  ))}  
  \</ul\>  
  \</div\>  
  );  
  };

- **Why:** Ensures side effects are correctly managed, and cleanup logic is type-safe, preventing memory leaks and unexpected behavior.

## **3. Typing useRef**

useRef is used for direct DOM manipulation or storing mutable values that don't trigger re-renders.

- **For DOM Elements:** Provide the HTML element type.  
  import React, { useRef, useEffect } from 'react';  
    
  const ProjectNameInput: React.FC = () =\> {  
  const inputRef = useRef\<HTMLInputElement\>(null); // Type HTMLInputElement, initial value is null  
    
  useEffect(() =\> {  
  if (inputRef.current) {  
  inputRef.current.focus(); // Type-safe access to DOM methods  
  }  
  }, \[\]);  
    
  return (  
  \<input ref={inputRef} type="text" placeholder="Enter project name" /\>  
  );  
  };

- **For Mutable Values:** Provide the type of the value you want to store.  
  const counterRef = useRef\<number\>(0);  
  // counterRef.current can now only be a number

- **Why:** Provides type safety when interacting with DOM elements and ensures mutable values are used consistently.

## **4. Typing useContext**

useContext allows components to consume values from a React Context.

- **Defining Context Type:** First, define the type of the value that the context will provide.  
  // src/contexts/AuthContext.ts (Example)  
  import React, { createContext, useContext, useState, ReactNode } from 'react';  
    
  interface AuthState {  
  user: { id: string; name: string; roles: string\[\] } \| null;  
  isAuthenticated: boolean;  
  isLoading: boolean;  
  }  
    
  interface AuthContextType {  
  authState: AuthState;  
  login: (username: string, password: string) =\> Promise\<void\>;  
  logout: () =\> void;  
  // ... any other auth actions  
  }  
    
  // Create context with a default value (important for type inference)  
  // Providing 'undefined' and checking for it is a common pattern for contexts  
  const AuthContext = createContext\<AuthContextType \| undefined\>(undefined);  
    
  interface AuthProviderProps {  
  children: ReactNode;  
  }  
    
  export const AuthProvider: React.FC\<AuthProviderProps\> = ({ children }) =\> {  
  const \[authState, setAuthState\] = useState\<AuthState\>({  
  user: null,  
  isAuthenticated: false,  
  isLoading: false,  
  });  
    
  const login = async (username: string, password: string) =\> { /\* ... login logic ... \*/ };  
  const logout = () =\> { /\* ... logout logic ... \*/ };  
    
  const value: AuthContextType = { authState, login, logout };  
    
  return (  
  \<AuthContext.Provider value={value}\>  
  {children}  
  \</AuthContext.Provider\>  
  );  
  };  
    
  // Custom hook to consume the context, ensuring it's not undefined  
  export const useAuth = () =\> {  
  const context = useContext(AuthContext);  
  if (context === undefined) {  
  throw new Error('useAuth must be used within an AuthProvider');  
  }  
  return context;  
  };

- **Why:** Ensures that any component consuming the context receives the correct type-safe data and functions, preventing runtime errors due to incorrect context usage.

## **5. Best Practices for Typing Custom Hooks (src/hooks/)**

Custom hooks are a primary mechanism for code reuse. Type them rigorously.

- **Explicit Inputs and Outputs:** Always type the parameters your custom hook accepts and the values it returns.

- **Leverage Generics:** For hooks that operate on various data types (e.g., a generic data fetching hook), use generics.

- **Return Tuples or Objects:** Return values as a tuple (\[value, setter\]) for simple state-like hooks, or an object ({ value, isLoading }) for hooks with multiple related return values, for better readability.  
  // src/hooks/useFetch.ts (A more generic fetch hook)  
  import { useState, useEffect } from 'react';  
    
  interface UseFetchResult\<T\> {  
  data: T \| null;  
  isLoading: boolean;  
  error: Error \| null;  
  }  
    
  // T is the type of the data expected from the API  
  function useFetch\<T\>(url: string): UseFetchResult\<T\> {  
  const \[data, setData\] = useState\<T \| null\>(null);  
  const \[isLoading, setIsLoading\] = useState\<boolean\>(true);  
  const \[error, setError\] = useState\<Error \| null\>(null);  
    
  useEffect(() =\> {  
  const fetchData = async () =\> {  
  setIsLoading(true);  
  setError(null);  
  try {  
  const response = await fetch(url);  
  if (!response.ok) {  
  throw new Error(\`HTTP error! status: \${response.status}\`);  
  }  
  const result = await response.json();  
  setData(result);  
  } catch (err: any) { // Use 'any' for unknown error type, then narrow  
  setError(err);  
  } finally {  
  setIsLoading(false);  
  }  
  };  
    
  fetchData();  
  }, \[url\]); // Rerun when URL changes  
    
  return { data, isLoading, error };  
  }  
    
  // Usage in application:  
  interface CableSpec { id: string; name: string; crossSection: number; }  
  const { data: cables, isLoading: cablesLoading, error: cablesError } = useFetch\<CableSpec\[\]\>('/api/v1/cables');  
    
  // src/modules/projects/hooks/useProjectCalculations.ts (Domain-specific hook)  
  import { useMutation } from '@tanstack/react-query';  
  import { projectCalculationsApi } from '@/api/generated'; // Generated client  
  import { CalculationRequestSchema, CalculationResultSchema } from '@/types/api'; // Generated types  
    
  interface UseProjectCalculationsResult {  
  triggerCalculation: (request: CalculationRequestSchema) =\> void;  
  isCalculating: boolean;  
  calculationResult: CalculationResultSchema \| undefined;  
  calculationError: Error \| null;  
  }  
    
  export function useProjectCalculations(): UseProjectCalculationsResult {  
  const {  
  mutate: triggerCalculation,  
  isLoading: isCalculating,  
  data: calculationResult,  
  error: calculationError  
  } = useMutation\<CalculationResultSchema, Error, CalculationRequestSchema\>(  
  (request) =\> projectCalculationsApi.performCalculation(request)  
  );  
    
  return { triggerCalculation, isCalculating, calculationResult, calculationError };  
  }

- **Why:** Ensures that custom hooks are reusable and type-safe across various components, leading to less duplicated logic and more predictable behavior. This is foundational for promoting DRY code.
