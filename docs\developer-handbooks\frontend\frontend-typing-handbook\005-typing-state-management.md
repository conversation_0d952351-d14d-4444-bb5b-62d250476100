# **Typing State Management with Zustand**

This guide focuses on effectively typing your client-side global state management using Zustand, ensuring type safety and clarity for application-wide UI states in our Heat Tracing Design Application.

## **1. Defining Zustand Store Interfaces**

The first step is to define the TypeScript interface for your Zustand store's state and its actions. This provides a clear contract for what your store holds and how it can be modified.

- **Separate State and Actions:** It's often good practice to define state properties and action methods within the same interface.  
  // src/store/appStore.ts (Example for general UI state)  
  import { create } from 'zustand';  
    
  interface AppState {  
  // State properties  
  isSidebarOpen: boolean;  
  theme: 'light' \| 'dark';  
  notifications: string\[\]; // Simple array of messages  
    
  // Actions  
  toggleSidebar: () =\> void;  
  setTheme: (newTheme: 'light' \| 'dark') =\> void;  
  addNotification: (message: string) =\> void;  
  clearNotifications: () =\> void;  
  }

## **2. Creating the Zustand Store with Type Safety**

Use the create\<T\>() generic provided by <PERSON>ustand to define your store, ensuring it adheres to the AppState interface.

// src/store/appStore.ts (Continued)  
  
export const useAppStore = create\<AppState\>((set) =\> ({  
// Initial State  
isSidebarOpen: false,  
theme: 'light', // Default theme  
notifications: \[\],  
  
// Actions  
toggleSidebar: () =\> set((state) =\> ({ isSidebarOpen: !state.isSidebarOpen })),  
setTheme: (newTheme) =\> {  
set({ theme: newTheme });  
// Optionally, persist theme to localStorage here  
localStorage.setItem('app-theme', newTheme);  
},  
addNotification: (message) =\> set((state) =\> ({ notifications: \[...state.notifications, message\] })),  
clearNotifications: () =\> set({ notifications: \[\] }),  
}));

- **Why:** The create\<AppState\> ensures that the store's state and actions conform to the defined interface. This catches type errors at compile time, preventing runtime issues.

## **3. Consuming State and Actions in Components**

When consuming the store in your React components, TypeScript will provide full type inference for both state properties and action methods.

- **Selecting Parts of the State:** Use selectors to pick only the necessary parts of the state. This optimizes re-renders, as the component will only re-render if the selected parts change.  
  // src/components/common/SidebarToggle.tsx  
  import React from 'react';  
  import { useAppStore } from '@/store/appStore'; // Assuming appStore.ts location  
    
  const SidebarToggle: React.FC = () =\> {  
  // Select only the parts of the state/actions needed  
  const { isSidebarOpen, toggleSidebar } = useAppStore(  
  (state) =\> ({  
  isSidebarOpen: state.isSidebarOpen,  
  toggleSidebar: state.toggleSidebar,  
  }),  
  // Optional: deep equality check for object selectors to prevent unnecessary re-renders  
  // shallow // import { shallow } from 'zustand/shallow'  
  );  
    
  return (  
  \<button  
  className="p-2 rounded-md"  
  onClick={toggleSidebar}  
  \>  
  {isSidebarOpen ? 'Close Sidebar' : 'Open Sidebar'}  
  \</button\>  
  );  
  };  
    
  export default SidebarToggle;

- **Accessing All State/Actions:** You can also access the entire state object if preferred, though selecting specific parts is generally more performant.  
  // src/components/common/NotificationsDisplay.tsx  
  import React from 'react';  
  import { useAppStore } from '@/store/appStore';  
    
  const NotificationsDisplay: React.FC = () =\> {  
  const { notifications, clearNotifications } = useAppStore(); // Access all directly  
    
  if (notifications.length === 0) {  
  return null;  
  }  
    
  return (  
  \<div className="bg-blue-100 p-3 rounded-md"\>  
  \<h3 className="font-semibold"\>Notifications ({notifications.length})\</h3\>  
  \<ul\>  
  {notifications.map((msg, index) =\> (  
  \<li key={index}\>{msg}\</li\>  
  ))}  
  \</ul\>  
  \<button onClick={clearNotifications} className="text-blue-600 underline mt-2"\>  
  Clear All  
  \</button\>  
  \</div\>  
  );  
  };  
    
  export default NotificationsDisplay;

- **Why:** Ensures components receive exactly what they need from the store, with full type safety and optimized re-renders.

## **4. Middleware & Store Enhancers**

When using Zustand middleware (like persist, devtools), you apply them around the create call. Types need to be composed.

// src/store/persistedUserPreferences.ts (Example with persist middleware)  
import { create } from 'zustand';  
import { persist, createJSONStorage } from 'zustand/middleware';  
  
interface UserPreferencesState {  
preferredLanguage: string;  
enableDarkMode: boolean;  
setLanguage: (lang: string) =\> void;  
toggleDarkMode: () =\> void;  
}  
  
export const useUserPreferencesStore = create\<UserPreferencesState\>()(  
persist(  
(set) =\> ({  
preferredLanguage: 'en',  
enableDarkMode: false,  
setLanguage: (lang) =\> set({ preferredLanguage: lang }),  
toggleDarkMode: () =\> set((state) =\> ({ enableDarkMode: !state.enableDarkMode })),  
}),  
{  
name: 'user-preferences-storage', // name of the item in localStorage  
storage: createJSONStorage(() =\> localStorage), // (optional) by default, 'localStorage' is used  
}  
)  
);

- **Why:** Middleware enhances store functionality (e.g., persistence, debugging). TypeScript ensures that the combined types of your state and the middleware's additions are correctly handled.
