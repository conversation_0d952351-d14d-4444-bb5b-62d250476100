/**
 * ComponentDetails Component Tests
 * Tests the ComponentDetails component including data display, navigation,
 * action buttons, responsive layout, loading/error states, and deep linking
 */

import {
    createMockComponent,
    mockComponent
} from '@/test/factories/componentFactories';
import { renderWithProviders } from '@/test/utils';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ComponentDetails } from '../../components/ComponentDetails';

// Mock the utility functions
vi.mock('../../utils', () => ({
  formatComponentName: vi.fn((component) => component.display_name || component.name),
  formatPrice: vi.fn((price, currency) => `$${price} ${currency}`),
  formatWeight: vi.fn((weight) => weight ? `${weight} kg` : 'N/A'),
  formatDimensions: vi.fn((dimensions) => 
    dimensions ? `${dimensions.length}×${dimensions.width}×${dimensions.height} mm` : 'N/A'
  ),
  getComponentStatusColor: vi.fn(() => 'text-green-600'),
  getComponentStatusText: vi.fn(() => 'Available'),
}));

describe('ComponentDetails', () => {
  const mockHandlers = {
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onTogglePreferred: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders component information correctly', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(screen.getByText(mockComponent.name)).toBeInTheDocument();
      expect(screen.getByText(mockComponent.manufacturer)).toBeInTheDocument();
      expect(screen.getByText(mockComponent.model_number)).toBeInTheDocument();
      expect(screen.getByText(mockComponent.part_number)).toBeInTheDocument();
    });

    it('displays component status and badges correctly', () => {
      const preferredComponent = createMockComponent({
        is_preferred: true,
        is_active: true,
      });

      renderWithProviders(
        <ComponentDetails component={preferredComponent} {...mockHandlers} />
      );

      expect(screen.getByText('Available')).toBeInTheDocument();
      expect(screen.getByText('Preferred')).toBeInTheDocument();
    });

    it('renders pricing information', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(screen.getByText('Pricing')).toBeInTheDocument();
      expect(screen.getByText(`$${mockComponent.unit_price} ${mockComponent.currency}`)).toBeInTheDocument();
    });

    it('displays physical properties section', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(screen.getByText('Physical Properties')).toBeInTheDocument();
      expect(screen.getByText('Weight')).toBeInTheDocument();
      expect(screen.getByText('Dimensions')).toBeInTheDocument();
    });

    it('shows specifications when available', () => {
      const componentWithSpecs = createMockComponent({
        specifications: {
          resistance: '1000Ω',
          tolerance: '±5%',
          power_rating: '0.25W',
        },
      });

      renderWithProviders(
        <ComponentDetails component={componentWithSpecs} {...mockHandlers} />
      );

      expect(screen.getByText('Specifications')).toBeInTheDocument();
      expect(screen.getByText('resistance')).toBeInTheDocument();
      expect(screen.getByText('1000Ω')).toBeInTheDocument();
    });

    it('displays metadata when available', () => {
      const componentWithMetadata = createMockComponent({
        metadata: {
          supplier_code: 'SUP123',
          lead_time: '2 weeks',
        },
      });

      renderWithProviders(
        <ComponentDetails component={componentWithMetadata} {...mockHandlers} />
      );

      expect(screen.getByText('Additional Metadata')).toBeInTheDocument();
      expect(screen.getByText(/"supplier_code"/)).toBeInTheDocument();
      expect(screen.getByText(/"SUP123"/)).toBeInTheDocument();
    });

    it('applies custom className', () => {
      const customClass = 'custom-details-class';
      const { container } = renderWithProviders(
        <ComponentDetails 
          component={mockComponent} 
          className={customClass}
          {...mockHandlers} 
        />
      );

      expect(container.firstChild).toHaveClass(customClass);
    });
  });

  describe('Loading States', () => {
    it('renders loading skeleton when isLoading is true', () => {
      renderWithProviders(
        <ComponentDetails 
          component={mockComponent} 
          isLoading={true}
          {...mockHandlers} 
        />
      );

      const skeletonElements = screen.getAllByRole('generic');
      const animatedElements = skeletonElements.filter(el => 
        el.className.includes('animate-pulse')
      );
      expect(animatedElements.length).toBeGreaterThan(0);
    });

    it('shows proper loading skeleton structure', () => {
      renderWithProviders(
        <ComponentDetails 
          component={mockComponent} 
          isLoading={true}
          {...mockHandlers} 
        />
      );

      // Should render multiple skeleton cards
      const cards = screen.getAllByRole('generic').filter(el => 
        el.className.includes('bg-gray-200')
      );
      expect(cards.length).toBeGreaterThan(3);
    });
  });

  describe('Action Buttons', () => {
    it('renders all action buttons', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /add to preferred|remove from preferred/i })).toBeInTheDocument();
    });

    it('calls onEdit when edit button is clicked', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      fireEvent.click(editButton);

      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockComponent);
    });

    it('calls onDelete when delete button is clicked', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const deleteButton = screen.getByRole('button', { name: /delete/i });
      fireEvent.click(deleteButton);

      expect(mockHandlers.onDelete).toHaveBeenCalledWith(mockComponent);
    });

    it('calls onTogglePreferred when preferred button is clicked', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const preferredButton = screen.getByRole('button', { name: /add to preferred/i });
      fireEvent.click(preferredButton);

      expect(mockHandlers.onTogglePreferred).toHaveBeenCalledWith(mockComponent);
    });

    it('shows correct preferred button text for preferred component', () => {
      const preferredComponent = createMockComponent({ is_preferred: true });

      renderWithProviders(
        <ComponentDetails component={preferredComponent} {...mockHandlers} />
      );

      expect(screen.getByRole('button', { name: /remove from preferred/i })).toBeInTheDocument();
    });

    it('shows correct preferred button text for non-preferred component', () => {
      const nonPreferredComponent = createMockComponent({ is_preferred: false });

      renderWithProviders(
        <ComponentDetails component={nonPreferredComponent} {...mockHandlers} />
      );

      expect(screen.getByRole('button', { name: /add to preferred/i })).toBeInTheDocument();
    });

    it('handles missing action handlers gracefully', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} />
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      fireEvent.click(editButton);

      // Should not throw error
      expect(editButton).toBeInTheDocument();
    });
  });

  describe('Data Display and Formatting', () => {
    it('formats component name using utility function', () => {
      const { formatComponentName } = require('../../utils');
      
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(formatComponentName).toHaveBeenCalledWith(mockComponent);
    });

    it('formats price using utility function', () => {
      const { formatPrice } = require('../../utils');
      
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(formatPrice).toHaveBeenCalledWith(mockComponent.unit_price, mockComponent.currency);
    });

    it('formats weight using utility function', () => {
      const { formatWeight } = require('../../utils');
      
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(formatWeight).toHaveBeenCalledWith(mockComponent.weight_kg);
    });

    it('formats dimensions using utility function', () => {
      const { formatDimensions } = require('../../utils');
      
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(formatDimensions).toHaveBeenCalledWith(mockComponent.dimensions);
    });

    it('displays component status using utility functions', () => {
      const { getComponentStatusColor, getComponentStatusText } = require('../../utils');
      
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(getComponentStatusColor).toHaveBeenCalledWith(mockComponent);
      expect(getComponentStatusText).toHaveBeenCalledWith(mockComponent);
    });

    it('handles null or undefined values gracefully', () => {
      const componentWithNulls = createMockComponent({
        description: null,
        unit_price: null,
        weight_kg: null,
        dimensions: null,
        specifications: null,
        metadata: null,
      });

      renderWithProviders(
        <ComponentDetails component={componentWithNulls} {...mockHandlers} />
      );

      expect(screen.getByText(componentWithNulls.name)).toBeInTheDocument();
    });
  });

  describe('Responsive Layout', () => {
    it('renders with responsive grid layout', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      // Check for responsive grid classes in the layout
      const gridElements = screen.getAllByRole('generic').filter(el => 
        el.className.includes('grid') || el.className.includes('md:') || el.className.includes('lg:')
      );
      expect(gridElements.length).toBeGreaterThan(0);
    });

    it('adapts button layout for mobile view', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const buttonContainer = screen.getByRole('button', { name: /edit/i }).closest('div');
      expect(buttonContainer).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper semantic structure', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      // Check for proper heading hierarchy
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });

    it('has accessible button labels', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      const deleteButton = screen.getByRole('button', { name: /delete/i });
      const preferredButton = screen.getByRole('button', { name: /add to preferred/i });

      expect(editButton).toHaveAccessibleName();
      expect(deleteButton).toHaveAccessibleName();
      expect(preferredButton).toHaveAccessibleName();
    });

    it('supports keyboard navigation', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const editButton = screen.getByRole('button', { name: /edit/i });
      const deleteButton = screen.getByRole('button', { name: /delete/i });

      editButton.focus();
      expect(document.activeElement).toBe(editButton);

      fireEvent.keyDown(editButton, { key: 'Tab' });
      expect(document.activeElement).toBe(deleteButton);
    });

    it('has proper ARIA attributes for status badges', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const statusBadge = screen.getByText('Available');
      expect(statusBadge).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles component with minimal data', () => {
      const minimalComponent = createMockComponent({
        description: undefined,
        unit_price: undefined,
        weight_kg: undefined,
        dimensions: undefined,
        specifications: undefined,
        metadata: undefined,
        supplier: undefined,
      });

      renderWithProviders(
        <ComponentDetails component={minimalComponent} {...mockHandlers} />
      );

      expect(screen.getByText(minimalComponent.name)).toBeInTheDocument();
      expect(screen.getByText(minimalComponent.manufacturer)).toBeInTheDocument();
    });

    it('handles inactive component display', () => {
      const inactiveComponent = createMockComponent({
        is_active: false,
        stock_status: 'discontinued',
      });

      renderWithProviders(
        <ComponentDetails component={inactiveComponent} {...mockHandlers} />
      );

      expect(screen.getByText(inactiveComponent.name)).toBeInTheDocument();
      // Status should reflect inactive state
    });

    it('handles component with empty specifications object', () => {
      const componentWithEmptySpecs = createMockComponent({
        specifications: {},
      });

      renderWithProviders(
        <ComponentDetails component={componentWithEmptySpecs} {...mockHandlers} />
      );

      expect(screen.getByText(componentWithEmptySpecs.name)).toBeInTheDocument();
      // Should not show specifications section for empty object
    });

    it('handles component with empty metadata object', () => {
      const componentWithEmptyMetadata = createMockComponent({
        metadata: {},
      });

      renderWithProviders(
        <ComponentDetails component={componentWithEmptyMetadata} {...mockHandlers} />
      );

      expect(screen.getByText(componentWithEmptyMetadata.name)).toBeInTheDocument();
      // Should not show metadata section for empty object
    });

    it('handles very long component names and descriptions', () => {
      const componentWithLongData = createMockComponent({
        name: 'A'.repeat(200),
        description: 'B'.repeat(1000),
        manufacturer: 'C'.repeat(100),
      });

      renderWithProviders(
        <ComponentDetails component={componentWithLongData} {...mockHandlers} />
      );

      expect(screen.getByText(componentWithLongData.name)).toBeInTheDocument();
    });

    it('handles special characters in component data', () => {
      const componentWithSpecialChars = createMockComponent({
        name: 'Component with "quotes" & <tags>',
        description: 'Description with émojis 🔧 and unicode ñ',
        specifications: {
          'special-key': 'value with & symbols',
          'unicode-key': 'value with ñ and émojis 🔧',
        },
      });

      renderWithProviders(
        <ComponentDetails component={componentWithSpecialChars} {...mockHandlers} />
      );

      expect(screen.getByText(/Component with "quotes" & <tags>/)).toBeInTheDocument();
    });

    it('handles component with zero values', () => {
      const componentWithZeros = createMockComponent({
        unit_price: 0,
        weight_kg: 0,
        dimensions: {
          length: 0,
          width: 0,
          height: 0,
        },
      });

      renderWithProviders(
        <ComponentDetails component={componentWithZeros} {...mockHandlers} />
      );

      expect(screen.getByText(componentWithZeros.name)).toBeInTheDocument();
    });
  });

  describe('Deep Linking and URL State Management', () => {
    it('displays component ID for deep linking', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(screen.getByText('Component ID')).toBeInTheDocument();
      expect(screen.getByText(mockComponent.id.toString())).toBeInTheDocument();
    });

    it('renders component ID in monospace font', () => {
      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      const componentIdElement = screen.getByText(mockComponent.id.toString());
      expect(componentIdElement).toHaveClass('font-mono');
    });

    it('handles component updates without losing state', () => {
      const { rerender } = renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(screen.getByText(mockComponent.name)).toBeInTheDocument();

      const updatedComponent = createMockComponent({
        id: mockComponent.id,
        name: 'Updated Component Name',
      });

      rerender(
        <ComponentDetails component={updatedComponent} {...mockHandlers} />
      );

      expect(screen.getByText('Updated Component Name')).toBeInTheDocument();
      expect(screen.getByText(mockComponent.id.toString())).toBeInTheDocument();
    });
  });

  describe('Performance and Optimization', () => {
    it('renders efficiently with large specifications object', () => {
      const largeSpecifications = {};
      for (let i = 0; i < 50; i++) {
        largeSpecifications[`spec_${i}`] = `value_${i}`;
      }

      const componentWithLargeSpecs = createMockComponent({
        specifications: largeSpecifications,
      });

      const startTime = performance.now();
      renderWithProviders(
        <ComponentDetails component={componentWithLargeSpecs} {...mockHandlers} />
      );
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should render in under 100ms
      expect(screen.getByText('Specifications')).toBeInTheDocument();
    });

    it('handles rapid prop changes without errors', () => {
      const { rerender } = renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      // Rapidly change props
      for (let i = 0; i < 10; i++) {
        const updatedComponent = createMockComponent({
          id: i,
          name: `Component ${i}`,
        });

        rerender(
          <ComponentDetails component={updatedComponent} {...mockHandlers} />
        );
      }

      expect(screen.getByText('Component 9')).toBeInTheDocument();
    });
  });

  describe('Integration with Utility Functions', () => {
    it('calls all formatting utilities with correct parameters', () => {
      const {
        formatComponentName,
        formatPrice,
        formatWeight,
        formatDimensions,
        getComponentStatusColor,
        getComponentStatusText
      } = require('../../utils');

      renderWithProviders(
        <ComponentDetails component={mockComponent} {...mockHandlers} />
      );

      expect(formatComponentName).toHaveBeenCalledWith(mockComponent);
      expect(formatPrice).toHaveBeenCalledWith(mockComponent.unit_price, mockComponent.currency);
      expect(formatWeight).toHaveBeenCalledWith(mockComponent.weight_kg);
      expect(formatDimensions).toHaveBeenCalledWith(mockComponent.dimensions);
      expect(getComponentStatusColor).toHaveBeenCalledWith(mockComponent);
      expect(getComponentStatusText).toHaveBeenCalledWith(mockComponent);
    });

    it('handles utility function errors gracefully', () => {
      const { formatComponentName } = require('../../utils');
      vi.mocked(formatComponentName).mockImplementation(() => {
        throw new Error('Formatting error');
      });

      // Should not crash the component
      expect(() => {
        renderWithProviders(
          <ComponentDetails component={mockComponent} {...mockHandlers} />
        );
      }).not.toThrow();
    });
  });
});
