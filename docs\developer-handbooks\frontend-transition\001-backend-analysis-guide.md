# **Backend Analysis Guide for Frontend Development**

This guide outlines a focused analysis of the backend, specifically designed to provide all necessary information for the seamless development of the frontend. The goal is to equip frontend developers with a clear understanding of the backend's capabilities, data structures, communication protocols, and operational aspects.

## **1. API Contract & Endpoint Specification (Most Critical)**

This section is the cornerstone for frontend development. Frontend developers need an unambiguous contract for every interaction.

- **1.1. OpenAPI (Swagger UI) Review:**

  - **Location:** Access the auto-generated API documentation by starting the backend development server (make run-dev) and navigating to the /docs endpoint (e.g., http://localhost:8000/docs).

  - **Key Information for Frontend:**

    - **Available Endpoints:** A comprehensive list of all HTTP methods (GET, POST, PUT, DELETE, PATCH) and their corresponding paths (e.g., /api/v1/projects, /api/v1/users/{id}).

    - **Request Schemas:** For POST, PUT, PATCH operations, examine the expected JSON body structure (e.g., what fields are required, their data types, and any specific formats like UUIDs or dates).

    - **Response Schemas:** For all operations, understand the expected successful (e.g., 200 OK, 201 Created) JSON response structure, including data types and nested objects.

    - **Path & Query Parameters:** Identify any required or optional parameters passed in the URL path or as query strings.

    - **Error Responses:** Understand the structure of API error responses (e.g., 400 Bad Request, 401 Unauthorized, 404 Not Found, 500 Internal Server Error). Confirm they adhere to the ErrorResponseSchema (src/core/schemas/error.py) and are consistent.

    - **Authentication Requirements:** Which endpoints require authentication, and what security schemes are applied (e.g., Bearer Token).

  - **Verification for Frontend:** Ensure the OpenAPI documentation is up-to-date, accurate, and reflects the actual implemented API behavior. Any discrepancies here will lead to integration friction.

- **1.2. API Versioning:**

  - **Information:** Confirm the API versioning strategy (e.g., /api/v1/). This informs frontend on how to construct API calls and manage potential future breaking changes.

  - **Location:** Refer to src/api/v1/ and the base API router definition.

## **2. Authentication & Authorization Flow**

Frontend needs to understand how users will log in, remain authenticated, and what permissions they will have.

- **2.1. Authentication Mechanism:**

  - **Information:** Detail the primary authentication method (e.g., OAuth2 with Bearer Tokens).

  - **Location:** src/core/auth/ and API endpoints under /api/v1/auth.

  - **Key Information for Frontend:**

    - **Login Endpoint:** Path, expected request body (username/password), and successful response structure (e.g., access token, refresh token).

    - **Token Handling:** How access tokens are issued, how long they are valid, and how they should be sent with subsequent requests (e.g., Authorization: Bearer \<token\>).

    - **Token Refresh:** If refresh tokens are used, detail the process for obtaining new access tokens.

    - **Logout Endpoint:** How to invalidate sessions/tokens.

- **2.2. Authorization (Permissions):**

  - **Information:** How the backend determines user permissions and if certain API endpoints or data fields are restricted based on user roles/scopes.

  - **Location:** src/core/auth/dependencies.py, src/core/auth/roles.py.

  - **Key Information for Frontend:**

    - Which roles or permissions exist (e.g., admin, user, viewer).

    - How the frontend can check if the current user has specific permissions to enable/disable UI elements or features.

    - How the backend responds when an unauthorized request is made (e.g., 403 Forbidden).

## **3. Data Models & Relationships**

Frontend developers need a clear picture of the data entities they will be working with.

- **3.1. Core Entities and Schemas:**

  - **Information:** Provide a clear list of all primary data entities (e.g., Project, Circuit, HeatTracing, Component, User).

  - **Location:** src/core/schemas/ (Pydantic models for request/response) and src/core/models/ (SQLAlchemy ORM models defining the database structure).

  - **Key Information for Frontend:**

    - For each major entity: its fields, their data types (string, integer, boolean, UUID, float, datetime, array/list), and whether they are optional or required.

    - Relationships between entities (e.g., a Project has many Circuits, a Circuit has many Components).

    - Enum definitions (e.g., PipeType, CableType) and their possible values.

  - **Verification for Frontend:** Ensure Pydantic Read schemas provide the exact structure the frontend expects to consume.

## **4. Backend Stability & Development Environment**

Frontend developers often need to run the backend locally or interact with a stable development environment.

- **4.1. Local Backend Setup:**

  - **Information:** Provide precise, step-by-step instructions for getting the backend running locally.

  - **Location:** README.md and potentially docs/how-to/.

  - **Key Information for Frontend:**

    - Python version requirements (make install).

    - Dependency installation (make install).

    - Database setup (e.g., make migrate-db).

    - Running the development server (make run-dev).

    - How to access the OpenAPI docs (/docs).

  - **Verification:** Confirm instructions are up-to-date and allow a frontend developer to bring up the backend easily.

- **4.2. Environment Management:**

  - **Information:** Clearly state how environment variables are managed (e.g., .env file, recommended virtual environment setup).

  - **Location:** README.md, .env.example.

  - **Key Information for Frontend:** Which environment variables are crucial for local setup (e.g., database URL, SECRET_KEY).

- **4.3. API Stability:**

  - **Information:** Provide a commitment regarding the stability of the API.

  - **Key Information for Frontend:** State that the API endpoints and their schemas are considered stable and will not undergo breaking changes without prior communication and versioning. This builds confidence for frontend development.

## **5. Error Handling & Feedback**

How the backend communicates errors impacts the frontend's ability to provide useful user feedback.

- **5.1. Standard Error Responses:**

  - **Information:** Reiterate the common error response structure (ErrorResponseSchema) and standard HTTP status codes used for different error types (e.g., 400 for validation, 401 for auth, 403 for forbidden, 404 for not found, 500 for server errors).

  - **Location:** src/core/schemas/error.py, src/core/errors/exceptions.py.

  - **Key Information for Frontend:**

    - How to parse backend error messages and display them meaningfully to the user.

    - Any specific error codes or types that the frontend should handle differently.

## **6. Performance Expectations & Real-Time Considerations**

Understanding performance characteristics helps frontend developers manage loading states and user experience.

- **6.1. General Response Times:**

  - **Information:** Provide general expectations for API response times for typical operations (e.g., fetching a list of projects, performing a calculation).

  - **Key Information for Frontend:** This guides frontend in implementing loading spinners, skeleton screens, or optimistic UI updates.

- **6.2. Handling Long-Running Tasks:**

  - **Information:** If there are any very long-running backend tasks (e.g., complex calculations, large data imports/exports), explain how the frontend should handle them (e.g., polling for status, WebSockets for updates, background jobs).

  - **Location:** Potentially src/core/calculations/, src/core/data_import/, src/core/reports/.

- **6.3. Real-time Capabilities (if applicable):**

  - **Information:** If the backend provides any real-time communication (e.g., WebSockets for collaborative editing, progress updates), detail the endpoints and message formats.

## **7. Next Steps for Frontend Development**

- **7.1. Preferred Communication Channel:**

  - **Action:** Establish a clear communication channel for frontend developers to ask questions, report backend issues, or propose API changes (e.g., a specific Slack channel, dedicated JIRA component, or direct contact person).

- **7.2. Access to Backend Code:**

  - **Action:** Ensure frontend developers have appropriate access to the backend Git repository for reference.

By analyzing and documenting these areas, the backend team can effectively hand over a clear, stable, and well-understood API to the frontend team, enabling them to proceed with confidence and minimal blockers.
