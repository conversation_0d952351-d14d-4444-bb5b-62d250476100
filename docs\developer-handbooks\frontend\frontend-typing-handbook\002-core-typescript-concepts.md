# **Core TypeScript Concepts for React/Next.js**

This guide covers the fundamental TypeScript concepts essential for building robust and type-safe React and Next.js applications, especially for our Heat Tracing Design Application.

## **1. Basic Types**

TypeScript extends JavaScript by adding static types.

- **string**: For text values.  
  let projectName: string = "Heat Tracing System";  
  let userName: string = "Alice Engineer";

- **number**: For numerical values (integers and floats).  
  let pipeLength: number = 150.75; // meters  
  let cableAmpacity: number = 120; // Amperes

- **boolean**: For true/false values.  
  let isProjectActive: boolean = true;  
  let isCalculationRunning: boolean = false;

- **any**: Avoid this! It bypasses TypeScript's type checking. Use only when absolutely necessary (e.g., when dealing with truly unpredictable third-party data).  
  let unkownData: any = "This can be anything"; // Avoid!

- **unknown**: Prefer this over any. It means the type is unknown, but you *must* perform type checking (narrowing) before using it.  
  let rawApiResponse: unknown = JSON.parse(someString);  
  if (typeof rawApiResponse === 'object' && rawApiResponse !== null && 'projectName' in rawApiResponse) {  
  console.log((rawApiResponse as { projectName: string }).projectName); // Type assertion after narrowing  
  }

- **void**: Represents a function that does not return any value.  
  function logMessage(message: string): void {  
  console.log(message);  
  }

## **2. Arrays, Objects, Tuples**

These allow you to type collections of data.

- **Arrays**: A collection of elements of a specific type.  
  let projectIds: string\[\] = \["proj-abc", "proj-xyz"\];  
  let temperatures: number\[\] = \[20, 25.5, 18\];  
    
  // Alternative syntax using generics (common in React)  
  let componentNames: Array\<string\> = \["Resistor", "Heater"\];

- **Objects**: Key-value pairs. Use interface or type for complex objects.  
  let projectDetails: { name: string; length: number; isActive: boolean } = {  
  name: "Building A HVAC",  
  length: 200,  
  isActive: true,  
  };

- **Tuples**: An ordered fixed-size array where each element can have a different type.  
  // \[cableName, cableLength, isSelected\]  
  let cableTuple: \[string, number, boolean\] = \["Cu/XLPE 3x95", 150.75, true\];

## **3. Type Aliases vs. Interfaces**

Both define custom types, but have subtle differences.

- **Type Aliases (type)**: Can define aliases for primitive types, unions, tuples, and objects. More flexible for complex type compositions.  
  type ProjectId = string; // Alias for a primitive  
  type StatusCode = 200 \| 400 \| 500; // Union type  
    
  type ProjectSummary = {  
  id: ProjectId;  
  name: string;  
  status: "Active" \| "Completed" \| "Pending";  
  };  
    
  let summary: ProjectSummary = {  
  id: "proj-123",  
  name: "Office Tower Revamp",  
  status: "Active",  
  };

- **Interfaces (interface)**: Primarily for defining the shape of objects. Can be extended (extends) and implemented by classes. They can also be "merged" (declaration merging), which is useful for augmenting existing library types.  
  interface HeatTracingSection {  
  id: string;  
  pipeLength: number;  
  heaterType: string;  
  }  
    
  interface DesignProject extends ProjectSummary { // Extending another interface  
  engineers: string\[\];  
  sections: HeatTracingSection\[\];  
  }  
    
  let myDesign: DesignProject = {  
  id: "design-456",  
  name: "Warehouse Cooling",  
  status: "Pending",  
  engineers: \["John Doe", "Jane Smith"\],  
  sections: \[{ id: "sec-001", pipeLength: 50, heaterType: "Self-Regulating" }\],  
  };

- **When to Use Which?**

  - **interface**: Generally preferred for defining object shapes, especially if you anticipate extensions or want to leverage declaration merging (e.g., for global types).

  - **type**: More flexible for unions, intersections, tuple types, and aliases for primitive types. Often preferred for consistency in modern React projects.

  - **Rule of Thumb:** For object shapes, either is fine. Choose one and stick to it within your project for consistency. Our project leans towards type for its flexibility.

## **4. Enums for Domain-Specific Values**

Enums allow you to define a set of named constants.

- **Numeric Enums (default):**  
  enum ProjectStatus {  
  ACTIVE, // 0  
  COMPLETED, // 1  
  PENDING // 2  
  }  
  let currentStatus: ProjectStatus = ProjectStatus.ACTIVE;

- **String Enums (Recommended for readability and serialization):**  
  enum CableConductorMaterial {  
  COPPER = "Copper",  
  ALUMINUM = "Aluminum",  
  }  
    
  enum InstallationMethod {  
  DIRECT_BURIED = "DirectBuried",  
  IN_AIR_SINGLE_TRAY = "InAirSingleTray",  
  CONDUIT = "Conduit",  
  }  
    
  let material: CableConductorMaterial = CableConductorMaterial.COPPER;  
  let method: InstallationMethod = InstallationMethod.DIRECT_BURIED;

- **Why:** Provides clear, type-safe choices for predefined sets of values (e.g., component_type or installation_method_type from backend enums), improving readability and preventing typos. String enums are better for debugging and when interacting with backend string values.

## **5. Union and Intersection Types**

Powerful ways to combine existing types.

- **Union Types (\|)**: A value can be *one of* several types.  
  type IdType = string \| number; // An ID can be a string or a number  
  let userId: IdType = "user-123";  
  let tempId: IdType = 456;  
    
  type HeatTracingErrorType = "Validation" \| "Calculation" \| "Auth" \| "Unknown";  
  function displayError(type: HeatTracingErrorType): void {  
  // ...  
  }

- **Intersection Types (&)**: A value must conform to *all* properties of multiple types.  
  interface Auditable {  
  createdAt: string;  
  updatedAt: string;  
  }  
    
  interface Project extends ProjectSummary { // using type alias extends interface  
  engineers: string\[\];  
  }  
    
  type DetailedProject = Project & Auditable; // Combines Project and Auditable properties  
    
  let detailedProj: DetailedProject = {  
  id: "proj-789",  
  name: "New Factory Build",  
  status: "Active",  
  engineers: \["Eve"\],  
  createdAt: "2023-01-15T10:00:00Z",  
  updatedAt: "2023-05-20T14:30:00Z",  
  };

- **Why:** Enables flexible and precise type definitions when data can vary (unions) or when combining features from different interfaces (intersections).

## **6. Type Inference and Type Assertions (as Type)**

TypeScript often figures out types for you. When it can't, or you know more than it does, use assertions.

- **Type Inference:** TypeScript automatically deduces the type of a variable from its initial value.  
  let customerName = "Global Corp"; // Type: string  
  let designCost = 150000; // Type: number

- **Type Assertions (as Type)**: Tells TypeScript, "Trust me, I know this is of this type." Use sparingly and only when you are certain, as it bypasses type checking.  
  const someValue: unknown = "this is a string";  
  const strLength: number = (someValue as string).length; // Asserting unknown as string  
    
  // When dealing with DOM elements  
  const myInput = document.getElementById('project-name') as HTMLInputElement;  
  console.log(myInput.value);

- **Why:** Inference simplifies code. Assertions are useful when dealing with dynamic data (e.g., from JSON.parse or document.getElementById) where you have more context than TypeScript can infer. However, overuse can hide actual type mismatches.

## **7. Generics: Writing Reusable Code**

Generics allow you to write functions, classes, and interfaces that work with different types while maintaining type safety.

- **What it is:** Using type variables (e.g., \<T\>, \<U\>) as placeholders for actual types that will be specified when the generic is used.  
  // Generic function to fetch and parse data  
  function fetchData\<T\>(url: string): Promise\<T\> {  
  return fetch(url).then(response =\> response.json()) as Promise\<T\>;  
  }  
    
  // Usage for project data  
  interface ProjectData { id: string; name: string; }  
  async function getProject(): Promise\<ProjectData\> {  
  const project = await fetchData\<ProjectData\>('/api/v1/projects/123');  
  return project;  
  }  
    
  // Generic function for finding an item in an array  
  function findItemById\<T extends { id: string }\>(items: T\[\], id: string): T \| undefined {  
  return items.find(item =\> item.id === id);  
  }  
    
  interface Circuit { id: string; name: string; length: number; }  
  const circuits: Circuit\[\] = \[{ id: 'c1', name: 'Main Feeder', length: 100 }\];  
  const foundCircuit = findItemById(circuits, 'c1');

- **Why it helps:** Promotes DRY by allowing you to write flexible components, hooks, or functions that operate on various data types without sacrificing type safety or duplicating code for each type. Essential for robust data fetching and utility functions.
