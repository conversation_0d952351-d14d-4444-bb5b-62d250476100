'use client';

/**
 * ComponentCard - Molecule component for displaying component summary
 * Follows atomic design principles and displays key component information
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Heart, 
  Edit, 
  Trash2, 
  Eye, 
  Star,
  Package,
  DollarSign,
  Weight,
  Ruler
} from 'lucide-react';
import type { ComponentRead } from '../types';
import { 
  formatComponentName, 
  formatPrice, 
  formatWeight, 
  formatDimensions,
  getComponentStatusColor,
  getComponentStatusText 
} from '../utils';

export interface ComponentCardProps {
  component: ComponentRead;
  isSelected?: boolean;
  showActions?: boolean;
  compact?: boolean;
  onSelect?: (component: ComponentRead) => void;
  onEdit?: (component: ComponentRead) => void;
  onDelete?: (component: ComponentRead) => void;
  onView?: (component: ComponentRead) => void;
  onTogglePreferred?: (component: ComponentRead) => void;
}

export function ComponentCard({
  component,
  isSelected = false,
  showActions = true,
  compact = false,
  onSelect,
  onEdit,
  onDelete,
  onView,
  onTogglePreferred,
}: ComponentCardProps) {
  const handleCardClick = () => {
    if (onSelect) {
      onSelect(component);
    } else if (onView) {
      onView(component);
    }
  };

  const statusColor = getComponentStatusColor(component);
  const statusText = getComponentStatusText(component);

  return (
    <Card 
      className={`
        transition-all duration-200 hover:shadow-md cursor-pointer
        ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''}
        ${!component.is_active ? 'opacity-60' : ''}
        ${compact ? 'p-2' : ''}
      `}
      onClick={handleCardClick}
    >
      <CardHeader className={`pb-2 ${compact ? 'p-2' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className={`font-semibold text-gray-900 truncate ${compact ? 'text-sm' : 'text-base'}`}>
              {formatComponentName(component)}
            </h3>
            <p className={`text-gray-600 truncate ${compact ? 'text-xs' : 'text-sm'}`}>
              {component.manufacturer} • {component.model_number}
            </p>
          </div>
          
          <div className="flex items-center gap-1 ml-2">
            {component.is_preferred && (
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
            )}
            <Badge 
              variant={component.is_active ? 'default' : 'secondary'}
              className={`text-xs ${statusColor}`}
            >
              {statusText}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className={`pt-0 ${compact ? 'p-2 pt-0' : ''}`}>
        {!compact && (
          <>
            {/* Component Type and Category */}
            <div className="flex flex-wrap gap-1 mb-2">
              {component.component_type && (
                <Badge variant="outline" className="text-xs">
                  <Package className="h-3 w-3 mr-1" />
                  {component.component_type}
                </Badge>
              )}
              {component.category && (
                <Badge variant="outline" className="text-xs">
                  {component.category}
                </Badge>
              )}
            </div>

            {/* Description */}
            {component.description && (
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {component.description}
              </p>
            )}
          </>
        )}

        {/* Key Specifications */}
        <div className={`grid gap-2 mb-3 ${compact ? 'grid-cols-2' : 'grid-cols-2 sm:grid-cols-3'}`}>
          {component.unit_price && (
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3 text-gray-400" />
              <span className={`font-medium ${compact ? 'text-xs' : 'text-sm'}`}>
                {formatPrice(component.unit_price, component.currency)}
              </span>
            </div>
          )}
          
          {component.weight_kg && (
            <div className="flex items-center gap-1">
              <Weight className="h-3 w-3 text-gray-400" />
              <span className={`text-gray-600 ${compact ? 'text-xs' : 'text-sm'}`}>
                {formatWeight(component.weight_kg)}
              </span>
            </div>
          )}
          
          {component.dimensions && (
            <div className="flex items-center gap-1">
              <Ruler className="h-3 w-3 text-gray-400" />
              <span className={`text-gray-600 ${compact ? 'text-xs' : 'text-sm'}`}>
                {formatDimensions(component.dimensions)}
              </span>
            </div>
          )}
        </div>

        {/* Supplier Information */}
        {!compact && component.supplier && (
          <div className="text-xs text-gray-500 mb-3">
            Supplier: {component.supplier}
            {component.part_number && ` • Part: ${component.part_number}`}
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onView?.(component);
                }}
                className="h-8 w-8 p-0"
              >
                <Eye className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit?.(component);
                }}
                className="h-8 w-8 p-0"
              >
                <Edit className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onTogglePreferred?.(component);
                }}
                className={`h-8 w-8 p-0 ${component.is_preferred ? 'text-yellow-500' : ''}`}
              >
                <Heart className={`h-4 w-4 ${component.is_preferred ? 'fill-current' : ''}`} />
              </Button>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete?.(component);
              }}
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Skeleton component for loading states
export function ComponentCardSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <Card className={compact ? 'p-2' : ''}>
      <CardHeader className={`pb-2 ${compact ? 'p-2' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className={`bg-gray-200 rounded animate-pulse ${compact ? 'h-4 mb-1' : 'h-5 mb-2'}`} />
            <div className={`bg-gray-200 rounded animate-pulse w-3/4 ${compact ? 'h-3' : 'h-4'}`} />
          </div>
          <div className="bg-gray-200 rounded animate-pulse h-6 w-16 ml-2" />
        </div>
      </CardHeader>
      
      <CardContent className={`pt-0 ${compact ? 'p-2 pt-0' : ''}`}>
        {!compact && (
          <>
            <div className="flex gap-2 mb-2">
              <div className="bg-gray-200 rounded animate-pulse h-6 w-20" />
              <div className="bg-gray-200 rounded animate-pulse h-6 w-24" />
            </div>
            <div className="bg-gray-200 rounded animate-pulse h-10 mb-3" />
          </>
        )}
        
        <div className={`grid gap-2 mb-3 ${compact ? 'grid-cols-2' : 'grid-cols-3'}`}>
          <div className="bg-gray-200 rounded animate-pulse h-4" />
          <div className="bg-gray-200 rounded animate-pulse h-4" />
          <div className="bg-gray-200 rounded animate-pulse h-4" />
        </div>
        
        <div className="flex justify-between pt-2 border-t">
          <div className="flex gap-1">
            <div className="bg-gray-200 rounded animate-pulse h-8 w-8" />
            <div className="bg-gray-200 rounded animate-pulse h-8 w-8" />
            <div className="bg-gray-200 rounded animate-pulse h-8 w-8" />
          </div>
          <div className="bg-gray-200 rounded animate-pulse h-8 w-8" />
        </div>
      </CardContent>
    </Card>
  );
}
