# Ultimate Electrical Designer - Development Makefile
# Type Safety and Development Workflow Commands

.PHONY: help install test type-check type-check-critical type-check-full lint format clean dev-setup

# Default target
help:
	@echo "🔧 Ultimate Electrical Designer - Development Commands"
	@echo "=================================================="
	@echo ""
	@echo "📦 Setup Commands:"
	@echo "  install          Install all dependencies"
	@echo "  dev-setup        Complete development environment setup"
	@echo ""
	@echo "🔍 Type Safety Commands:"
	@echo "  type-check       Run comprehensive type safety validation"
	@echo "  type-check-critical  Run critical modules type checking only"
	@echo "  type-check-full  Run full type checking (may fail due to SQLAlchemy)"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo ""
	@echo "🎨 Code Quality Commands:"
	@echo "  lint             Run all linting checks"
	@echo "  format           Format code with ruff"
	@echo "  security-check   Run security scanning with bandit"
	@echo ""
	@echo "🧹 Utility Commands:"
	@echo "  clean            Clean up temporary files and caches"
	@echo "  pre-commit-install Install pre-commit hooks"

# Installation and setup
install:
	@echo "📦 Installing dependencies..."
	cd server && poetry install

dev-setup: install pre-commit-install
	@echo "🔧 Setting up development environment..."
	@echo "✅ Development environment ready!"

# Type safety validation
type-check:
	@echo "🔍 Running comprehensive type safety validation..."
	./scripts/type_safety_check.sh

type-check-critical:
	@echo "🎯 Running critical modules type checking..."
	cd server && poetry run mypy src/core/utils/performance_optimizer.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/core/utils/memory_manager.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/core/utils/json_validation.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/core/utils/file_io_utils.py --show-error-codes --ignore-missing-imports
	cd server && poetry run mypy src/config/settings.py --show-error-codes --ignore-missing-imports
	@echo "✅ Critical modules type checking completed"

type-check-full:
	@echo "⚠️  Running full type checking (may fail due to SQLAlchemy issue)..."
	cd server && poetry run mypy src/ --show-error-codes --ignore-missing-imports || echo "❌ Full type checking blocked by SQLAlchemy compatibility issue"

# Testing
test:
	@echo "🧪 Running all tests..."
	cd server && poetry run pytest tests/ -v

test-unit:
	@echo "🧪 Running unit tests..."
	cd server && poetry run pytest -v -m unit

test-integration:
	@echo "🧪 Running integration tests..."
	cd server && poetry run pytest -v -m integration

# Code quality
lint:
	@echo "🔍 Running linting checks..."
	cd server && poetry run ruff check .

format:
	@echo "🎨 Formatting code..."
	cd server && poetry run ruff format .
	@echo "✅ Code formatting completed"

security-check:
	@echo "🔒 Running security checks..."
	cd server && poetry run bandit -r src/ -f json -o bandit-report.json
	@echo "✅ Security check completed - see bandit-report.json"

# Pre-commit hooks
pre-commit-install:
	@echo "🪝 Installing pre-commit hooks..."
	pre-commit install
	@echo "✅ Pre-commit hooks installed"

pre-commit-run:
	@echo "🪝 Running pre-commit hooks..."
	pre-commit run --all-files

# Cleanup
clean:
	@echo "🧹 Cleaning up temporary files..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	find . -type f -name "*.pyd" -delete 2>/dev/null || true
	find . -type f -name ".coverage" -delete 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".ruff_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	rm -f server/type_safety_report.txt 2>/dev/null || true
	rm -f server/bandit-report.json 2>/dev/null || true
	@echo "✅ Cleanup completed"

# Development server
dev:
	@echo "🚀 Starting development server..."
	cd server && poetry run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# Database operations
db-migrate:
	@echo "🗄️  Running database migrations..."
	cd server && poetry run alembic upgrade head

db-reset:
	@echo "🗄️  Resetting database..."
	cd server && rm -f data/app_dev.db
	cd server && poetry run alembic upgrade head

# Documentation
docs-build:
	@echo "📚 Building documentation..."
	@echo "Documentation build not yet implemented"

# CI/CD simulation
ci-check: type-check-critical lint test
	@echo "✅ CI/CD checks completed successfully"
