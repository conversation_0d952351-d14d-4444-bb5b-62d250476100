# 04 - Unified Patterns

**Section:** 04-unified-patterns  
**Version:** 1.0  
**Last Updated:** July 2025  
**Prerequisites:** [Development Standards](030-development-standards.md) completed  
**Estimated Reading Time:** 25 minutes  

## Overview

The Unified Patterns system is the cornerstone of the Ultimate Electrical Designer's engineering-grade architecture. It provides consistent error handling, performance monitoring, and security validation across all layers of the application, replacing legacy try-catch blocks with robust, standardized decorators that ensure reliability and maintainability.

## Table of Contents

- [Unified Patterns Philosophy](#unified-patterns-philosophy)
- [Error Handling Decorators](#error-handling-decorators)
- [Performance Monitoring Decorators](#performance-monitoring-decorators)
- [Security Validation Patterns](#security-validation-patterns)
- [Migration from Legacy Patterns](#migration-from-legacy-patterns)
- [Compliance Verification](#compliance-verification)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Unified Patterns Philosophy

### Core Principles

1. **Consistency Across Layers:** Every layer uses the same error handling and monitoring patterns
2. **Zero Legacy Code:** Complete migration from manual try-catch blocks to unified decorators
3. **Engineering-Grade Robustness:** Professional-quality error handling suitable for mission-critical applications
4. **Performance by Design:** Built-in monitoring and optimization for all operations
5. **Security by Default:** Comprehensive validation and protection at all entry points

### Architecture Integration

The unified patterns system integrates seamlessly with the 5-layer architecture:

```
API Layer        → @handle_api_errors + @require_permissions
Service Layer    → @handle_service_errors + @monitor_service_performance  
Repository Layer → @handle_repository_errors + @handle_database_errors
Calculation Layer → @handle_calculation_errors + @monitor_calculation_performance
Validation Layer → @handle_validation_errors + @validate_input
```

## Error Handling Decorators

### @handle_service_errors
**Purpose:** Standardize error handling in the service layer  
**Usage:** All service methods must use this decorator  
**Location:** `core.errors.unified_error_handler`

```python
from src.core.errors.unified_error_handler import handle_service_errors

@handle_service_errors("project_creation")
def create_project(self, project_data: dict) -> Project:
    """Create a new project with unified error handling.
    
    Args:
        project_data: Project creation parameters
        
    Returns:
        Created project instance
        
    Raises:
        ServiceError: If project creation fails
        InvalidInputError: If input data is invalid
    """
    # Service logic with automatic error handling
    validated_data = self._validate_project_data(project_data)
    project = self.repository.create(validated_data)
    self._initialize_project_defaults(project)
    return project
```

**Benefits:**
- Automatic error logging with context
- Consistent error response formatting
- Performance metrics collection
- Graceful error propagation

### @handle_repository_errors
**Purpose:** Translate database errors to application exceptions  
**Usage:** All repository methods must use this decorator  
**Location:** `core.errors.unified_error_handler`

```python
from src.core.errors.unified_error_handler import handle_repository_errors

@handle_repository_errors("project")
def create_project(self, project_data: dict) -> Project:
    """Create project in database with unified error handling.
    
    Args:
        project_data: Validated project data
        
    Returns:
        Created project instance
        
    Raises:
        RepositoryError: If database operation fails
        DuplicateError: If project name already exists
    """
    try:
        project = Project(**project_data)
        self.db.add(project)
        self.db.commit()
        self.db.refresh(project)
        return project
    except IntegrityError as e:
        if "unique constraint" in str(e).lower():
            raise DuplicateError(f"Project name '{project_data['name']}' already exists")
        raise
```

### @handle_api_errors
**Purpose:** Standardize API error responses  
**Usage:** All API endpoint functions  
**Location:** `core.errors.unified_error_handler`

```python
from src.core.errors.unified_error_handler import handle_api_errors

@router.post("/projects", response_model=ProjectResponse)
@handle_api_errors("create_project")
async def create_project(
    project_data: ProjectCreateSchema,
    current_user: User = Depends(get_current_user),
    project_service: ProjectService = Depends(get_project_service)
) -> ProjectResponse:
    """Create a new project via API.
    
    Args:
        project_data: Project creation data
        current_user: Authenticated user
        project_service: Project service instance
        
    Returns:
        Created project response
    """
    project = project_service.create_project(project_data.dict())
    return ProjectResponse.from_orm(project)
```

### @handle_calculation_errors
**Purpose:** Robust error handling for engineering calculations  
**Usage:** All calculation methods  
**Location:** `core.errors.unified_error_handler`

```python
from src.core.errors.unified_error_handler import handle_calculation_errors

@handle_calculation_errors("heat_loss_calculation")
def calculate_heat_loss(
    self,
    pipe_params: PipeParameters,
    ambient_conditions: AmbientConditions,
    target_temperature: float
) -> HeatLossResult:
    """Calculate heat loss with unified error handling.
    
    Args:
        pipe_params: Pipe physical parameters
        ambient_conditions: Environmental conditions
        target_temperature: Target maintenance temperature
        
    Returns:
        Heat loss calculation results
        
    Raises:
        CalculationError: If calculation fails
        InvalidInputError: If parameters are invalid
    """
    # Validate input parameters
    self._validate_pipe_parameters(pipe_params)
    self._validate_ambient_conditions(ambient_conditions)
    
    # Perform heat loss calculation
    heat_transfer_coefficient = self._calculate_heat_transfer_coefficient(
        pipe_params, ambient_conditions
    )
    heat_loss_per_meter = self._calculate_heat_loss_per_meter(
        pipe_params, ambient_conditions, target_temperature, heat_transfer_coefficient
    )
    
    return HeatLossResult(
        heat_loss_per_meter=heat_loss_per_meter,
        heat_transfer_coefficient=heat_transfer_coefficient,
        calculation_method="IEC-60287",
        ambient_conditions=ambient_conditions
    )
```

### @handle_validation_errors
**Purpose:** Consistent validation error handling  
**Usage:** All validation functions  
**Location:** `core.errors.unified_error_handler`

```python
from src.core.errors.unified_error_handler import handle_validation_errors

@handle_validation_errors("project_validation")
def validate_project_data(self, project_data: dict) -> ProjectCreateSchema:
    """Validate project data with unified error handling.
    
    Args:
        project_data: Raw project data
        
    Returns:
        Validated project schema
        
    Raises:
        DataValidationError: If validation fails
    """
    # Pydantic validation
    schema = ProjectCreateSchema(**project_data)
    
    # Business rule validation
    if schema.voltage_level > 1000 and not schema.high_voltage_certified:
        raise ValidationError("High voltage projects require certification")
    
    return schema
```

## Performance Monitoring Decorators

### @monitor_service_performance
**Purpose:** Track service layer performance metrics  
**Usage:** Critical service methods  
**Location:** `core.monitoring.performance_decorators`

```python
from src.core.monitoring.performance_decorators import monitor_service_performance

@handle_service_errors("complex_calculation")
@monitor_service_performance("complex_calculation")
def perform_complex_calculation(self, parameters: CalculationParameters) -> CalculationResult:
    """Perform complex calculation with performance monitoring.
    
    Automatically tracks:
    - Execution time
    - Memory usage
    - CPU utilization
    - Success/failure rates
    """
    # Complex calculation logic
    result = self._execute_calculation(parameters)
    return result
```

### @monitor_calculation_performance
**Purpose:** Specialized monitoring for engineering calculations  
**Usage:** All calculation methods  
**Location:** `core.monitoring.performance_decorators`

```python
from src.core.monitoring.performance_decorators import monitor_calculation_performance

@handle_calculation_errors("thermal_analysis")
@monitor_calculation_performance("thermal_analysis")
def perform_thermal_analysis(self, thermal_params: ThermalParameters) -> ThermalResult:
    """Perform thermal analysis with specialized monitoring.
    
    Tracks calculation-specific metrics:
    - Numerical convergence time
    - Iteration count
    - Accuracy achieved
    - Memory usage patterns
    """
    # Thermal analysis implementation
    pass
```

### @memory_optimized
**Purpose:** Automatic memory management for large calculations  
**Usage:** Memory-intensive operations  
**Location:** `core.monitoring.memory_decorators`

```python
from src.core.monitoring.memory_decorators import memory_optimized

@memory_optimized(auto_cleanup=True, threshold_mb=5.0)
@handle_calculation_errors("large_thermal_model")
def calculate_large_thermal_model(self, model_params: LargeModelParameters) -> ModelResult:
    """Calculate large thermal model with memory optimization.
    
    Features:
    - Automatic garbage collection when memory threshold exceeded
    - Memory usage tracking and alerting
    - Automatic cleanup of intermediate results
    """
    # Large calculation with automatic memory management
    pass
```

## Security Validation Patterns

### @require_permissions
**Purpose:** Role-based access control  
**Usage:** All protected API endpoints  
**Location:** `core.security.enhanced_dependencies`

```python
from src.core.security.enhanced_dependencies import require_permissions

@router.get("/projects/{project_id}")
@require_permissions("project:read")
async def get_project(
    project_id: int,
    current_user: User = Depends(get_current_user)
) -> ProjectResponse:
    """Get project with permission validation."""
    # Automatic permission checking before method execution
    pass
```

### @validate_input
**Purpose:** Comprehensive input validation  
**Usage:** All data processing functions  
**Location:** `core.security.unified_security_validator`

```python
from src.core.security.unified_security_validator import validate_input

@validate_input("electrical_parameters")
def process_electrical_parameters(self, params: dict) -> ElectricalParameters:
    """Process electrical parameters with security validation.
    
    Validates:
    - Input sanitization
    - Range checking
    - Type validation
    - Business rule compliance
    """
    # Secure parameter processing
    pass
```

## Migration from Legacy Patterns

### Legacy Pattern Identification
The unified patterns analyzer identifies legacy patterns that need migration:

```bash
# Check unified patterns compliance
python scripts/inventory_analyzer.py unified-patterns

# Example output:
# Error Handling: 28/133 modules migrated (21.1%)
# Performance Monitoring: 69/99 modules migrated (69.7%)
# Security Validation: 22/29 modules migrated (75.9%)
```

### Migration Process

#### Step 1: Identify Legacy Code
```python
# ❌ Legacy pattern - Manual try-catch
def legacy_service_method(self, data):
    try:
        result = self.repository.create(data)
        return result
    except SQLAlchemyError as e:
        logger.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal error")
```

#### Step 2: Apply Unified Patterns
```python
# ✅ Unified pattern - Decorator-based
@handle_service_errors("service_operation")
def unified_service_method(self, data: dict) -> ServiceResult:
    """Service method with unified error handling."""
    result = self.repository.create(data)
    return result
```

#### Step 3: Remove Legacy Code
```python
# Remove manual error handling, logging, and exception translation
# The unified decorators handle all of this automatically
```

### Migration Verification
```bash
# Verify migration completion
make test-unified-patterns

# Check specific modules
python scripts/inventory_analyzer.py unified-patterns --module core.services.project_service
```

## Compliance Verification

### Automated Compliance Checking
```bash
# Full compliance check
python scripts/inventory_analyzer.py unified-patterns

# Specific pattern checks
python scripts/inventory_analyzer.py unified-patterns --pattern error_handling
python scripts/inventory_analyzer.py unified-patterns --pattern performance_monitoring
python scripts/inventory_analyzer.py unified-patterns --pattern security_validation
```

### Compliance Reports
The system generates detailed compliance reports:

```
Unified Patterns Migration Analysis Report
Generated: 2025-07-05 11:00:19
Total Modules Analyzed: 197

Security Validation: 22/29 modules migrated (75.9%)
Error Handling: 28/133 modules migrated (21.1%)
Performance Monitoring: 69/99 modules migrated (69.7%)

Modules Requiring Migration:
- api/main_router.py
- core/services/legacy_service.py
- core/calculations/old_calculation.py
```

### CI/CD Integration
```bash
# Add to CI/CD pipeline
make validate-unified-patterns

# Fail build if compliance below threshold
python scripts/inventory_analyzer.py unified-patterns --fail-threshold 90
```

## Best Practices

### Decorator Ordering
**Rule:** Always apply decorators in the correct order for optimal functionality.

```python
# ✅ Correct order
@memory_optimized(auto_cleanup=True, threshold_mb=5.0)
@monitor_calculation_performance("thermal_analysis")
@handle_calculation_errors("thermal_analysis")
def calculation_method(self, params):
    """Calculation with proper decorator ordering."""
    pass

# ❌ Incorrect order - monitoring won't capture errors properly
@handle_calculation_errors("thermal_analysis")
@monitor_calculation_performance("thermal_analysis")
def calculation_method(self, params):
    pass
```

### Error Context
**Rule:** Provide meaningful operation names for better error tracking.

```python
# ✅ Descriptive operation names
@handle_service_errors("project_thermal_analysis")
@handle_service_errors("cable_selection_optimization")
@handle_service_errors("switchboard_load_calculation")

# ❌ Generic operation names
@handle_service_errors("operation")
@handle_service_errors("service_method")
```

### Performance Monitoring
**Rule:** Monitor all critical operations but avoid over-monitoring.

```python
# ✅ Monitor critical operations
@monitor_service_performance("project_creation")  # Critical business operation
@monitor_calculation_performance("heat_loss")     # Engineering calculation

# ❌ Don't monitor simple getters
@monitor_service_performance("get_project_name")  # Too granular
```

## Troubleshooting

### Common Issues

#### Issue: Decorator Import Errors
**Symptoms:** `ImportError` when importing unified decorators  
**Solution:**
```python
# Ensure correct import paths
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.performance_decorators import monitor_service_performance

# Check if module is in Python path
import sys
print(sys.path)
```

#### Issue: Performance Monitoring Not Working
**Symptoms:** No performance metrics in logs  
**Solution:**
```python
# Verify decorator order
@monitor_service_performance("operation")  # Must be before error handler
@handle_service_errors("operation")
def method(self):
    pass

# Check logging configuration
import logging
logger = logging.getLogger("ultimate_electrical_designer.performance")
logger.setLevel(logging.INFO)
```

#### Issue: Error Handling Not Catching Exceptions
**Symptoms:** Unhandled exceptions reaching the API layer  
**Solution:**
```python
# Ensure decorator is applied
@handle_service_errors("operation_name")  # Required
def service_method(self):
    pass

# Check for specific exception types that bypass the handler
from src.core.errors.exceptions import BaseApplicationException
# These are re-raised automatically
```

---

**Navigation:**  
← [Previous: Development Standards](030-development-standards.md) | [Handbook Home](001-cover.md) | [Next: Backend Development](050-backend-development.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Unified Error Handler Implementation](../../backend/core/errors/unified_error_handler.py)
- [Performance Monitoring Documentation](../../backend/docs/architecture-specifications/performance/)
- [Security Validation Patterns](../../backend/docs/architecture-specifications/security/)
