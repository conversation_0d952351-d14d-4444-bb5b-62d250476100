'use client';

/**
 * Create Component Page
 * Form for creating new components
 */

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { ComponentForm } from '@/modules/components';
import type { ComponentCreate } from '@/modules/components/types';
import { useCreateComponent } from '@/modules/components/api/componentMutations';

export default function CreateComponentPage() {
  const router = useRouter();

  // Create mutation
  const createMutation = useCreateComponent({
    onSuccess: (component) => {
      router.push(`/components/${component.id}`);
    },
    onError: (error) => {
      alert(`Failed to create component: ${error.message}`);
    },
  });

  // Handle form submission
  const handleSubmit = (data: ComponentCreate) => {
    createMutation.mutate(data);
  };

  // Handle cancel
  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              
              <div>
                <h1 className="text-xl font-bold text-gray-900">Create New Component</h1>
                <p className="text-sm text-gray-600">
                  Add a new electrical component to the catalog
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <ComponentForm
          isLoading={createMutation.isPending}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
}
