# 07 - Testing Framework

**Section:** 07-testing-framework  
**Version:** 1.0  
**Last Updated:** July 2025  
**Prerequisites:** [Frontend Transition](060-frontend-transition.md) completed  
**Estimated Reading Time:** 40 minutes  

## Overview

The Ultimate Electrical Designer employs a comprehensive 5-phase testing methodology that ensures engineering-grade reliability and maintainability. This section details the testing framework, coverage requirements, real database testing approach, and quality standards that maintain the project's zero-tolerance policies for incomplete implementations.

## Table of Contents

- [5-Phase Testing Methodology](#5-phase-testing-methodology)
- [Testing Architecture](#testing-architecture)
- [Coverage Requirements](#coverage-requirements)
- [Real Database Testing](#real-database-testing)
- [Test Categories](#test-categories)
- [Testing Standards](#testing-standards)
- [Automated Testing Pipeline](#automated-testing-pipeline)
- [Performance Testing](#performance-testing)
- [Security Testing](#security-testing)
- [Standards Compliance Testing](#standards-compliance-testing)

## 5-Phase Testing Methodology

The project follows a systematic 5-phase testing methodology that ensures comprehensive coverage and engineering-grade quality:

### Phase 1: Discovery & Analysis
**Objective:** Understand testing requirements and analyze current coverage

**Activities:**
- Analyze existing test coverage and identify gaps
- Review business requirements and acceptance criteria
- Identify critical paths and high-risk areas
- Assess testing infrastructure and tooling needs
- Document testing scope and priorities

**Deliverables:**
- Test coverage analysis report
- Risk assessment matrix
- Testing requirements specification
- Resource allocation plan

### Phase 2: Task Planning
**Objective:** Break down testing work into manageable units

**Activities:**
- Create detailed test plans for each component
- Break down testing tasks into ~20-minute units
- Prioritize testing tasks by criticality and dependencies
- Assign testing responsibilities and timelines
- Plan test data and environment requirements

**Deliverables:**
- Detailed test execution plan
- Task breakdown with time estimates
- Test environment specifications
- Test data requirements

### Phase 3: Implementation
**Objective:** Execute comprehensive testing with unified patterns compliance

**Activities:**
- Implement unit tests with real database connections
- Develop integration tests for service interactions
- Create API tests for endpoint validation
- Build calculation tests for engineering accuracy
- Implement security and standards compliance tests

**Deliverables:**
- Complete test suite implementation
- Test data setup and teardown procedures
- Automated test execution scripts
- Test documentation and examples

### Phase 4: Verification
**Objective:** Validate test coverage and effectiveness

**Activities:**
- Execute full test suite and analyze results
- Verify coverage targets are met (90%+ critical, 85%+ high priority)
- Validate test quality and maintainability
- Review test performance and optimization opportunities
- Conduct test result analysis and reporting

**Deliverables:**
- Test execution reports
- Coverage analysis reports
- Performance benchmarks
- Quality metrics dashboard

### Phase 5: Documentation
**Objective:** Document testing approach and maintain knowledge base

**Activities:**
- Update testing documentation and procedures
- Create testing guides and best practices
- Document lessons learned and improvements
- Maintain test case documentation
- Update handbook and training materials

**Deliverables:**
- Updated testing documentation
- Best practices guide
- Training materials
- Continuous improvement recommendations

## Testing Architecture

### Test Structure Organization

```
server/tests/
├── unit/                       # Unit tests (90%+ coverage for critical)
│   ├── test_models/           # Model validation and business logic
│   ├── test_schemas/          # Pydantic schema validation
│   ├── test_services/         # Service layer business logic
│   ├── test_repositories/     # Repository layer data access
│   ├── test_calculations/     # Engineering calculation accuracy
│   └── test_utils/            # Utility function testing
├── integration/               # Integration tests (85%+ coverage)
│   ├── test_api_integration/  # API endpoint integration
│   ├── test_service_integration/ # Service layer integration
│   ├── test_database_integration/ # Database operation integration
│   └── test_workflow_integration/ # End-to-end workflow testing
├── api/                       # API endpoint tests
│   ├── test_project_routes/   # Project management endpoints
│   ├── test_component_routes/ # Component management endpoints
│   ├── test_calculation_routes/ # Calculation endpoints
│   └── test_auth_routes/      # Authentication endpoints
├── calculations/              # Engineering calculation tests
│   ├── test_heat_tracing/     # Heat tracing calculations
│   ├── test_electrical/       # Electrical system calculations
│   ├── test_thermal/          # Thermal analysis
│   └── test_standards/        # Standards compliance validation
├── security/                  # Security validation tests
│   ├── test_authentication/   # Authentication security
│   ├── test_authorization/    # Authorization controls
│   ├── test_input_validation/ # Input sanitization
│   └── test_data_protection/  # Data protection measures
├── performance/               # Performance benchmarking
│   ├── test_api_performance/  # API response times
│   ├── test_calculation_performance/ # Calculation efficiency
│   └── test_database_performance/ # Database query optimization
├── standards/                 # Standards compliance tests
│   ├── test_ieee_compliance/  # IEEE standards validation
│   ├── test_iec_compliance/   # IEC standards validation
│   └── test_en_compliance/    # EN standards validation
├── fixtures/                  # Test data and fixtures
├── conftest.py               # Pytest configuration and fixtures
└── test_config.py            # Test configuration settings
```

### Test Configuration

#### Pytest Configuration
```python
# conftest.py - Global test configuration
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient

from src.core.database.session import get_db
from src.core.models.base import Base
from main import app

# Test database configuration
TEST_DATABASE_URL = "sqlite:///./test_app.db"

@pytest.fixture(scope="session")
def engine():
    """Create test database engine."""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={
            "check_same_thread": False,
            "isolation_level": None,
        },
        poolclass=StaticPool,
        echo=False  # Set to True for SQL debugging
    )
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def db_session(engine) -> Generator[Session, None, None]:
    """Create database session for each test."""
    TestingSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.rollback()
        session.close()

@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """Create test client with database session override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()

@pytest.fixture(scope="function")
def authenticated_client(client: TestClient, test_user) -> TestClient:
    """Create authenticated test client."""
    # Login and get token
    login_data = {
        "username": test_user.email,
        "password": "testpassword123"
    }
    response = client.post("/api/v1/auth/login", data=login_data)
    token = response.json()["access_token"]
    
    # Set authorization header
    client.headers.update({"Authorization": f"Bearer {token}"})
    
    return client

@pytest.fixture
def test_user(db_session: Session):
    """Create test user for authentication tests."""
    from src.core.models.user import User
    from src.core.security.password import get_password_hash
    
    user = User(
        email="<EMAIL>",
        name="Test User",
        hashed_password=get_password_hash("testpassword123"),
        is_active=True
    )
    
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    
    return user

@pytest.fixture
def test_project_data():
    """Standard test project data."""
    return {
        "name": "Test Electrical Project",
        "description": "Test project for unit testing",
        "voltage_level": 400.0,
        "environment_type": "indoor",
        "ambient_temperature_min": -10.0,
        "ambient_temperature_max": 50.0,
        "client_name": "Test Client Corp",
        "project_location": "Test Location",
        "design_standards": ["IEEE-519", "IEC-60364"]
    }

# Pytest markers for test categorization
pytest_plugins = ["pytest_asyncio"]

def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "unit: Unit tests for individual components"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests for component interaction"
    )
    config.addinivalue_line(
        "markers", "api: API endpoint tests"
    )
    config.addinivalue_line(
        "markers", "database: Database operation tests"
    )
    config.addinivalue_line(
        "markers", "calculations: Engineering calculation tests"
    )
    config.addinivalue_line(
        "markers", "standards: Standards compliance tests"
    )
    config.addinivalue_line(
        "markers", "security: Security validation tests"
    )
    config.addinivalue_line(
        "markers", "performance: Performance benchmarking tests"
    )
    config.addinivalue_line(
        "markers", "slow: Tests that take longer than 1 second"
    )
```

## Coverage Requirements

### Coverage Targets by Priority

#### Critical Modules (90%+ Coverage Required)
- **Calculation Engines:** Heat tracing, electrical calculations, cable sizing
- **Safety Systems:** ATEX compliance, electrical safety, hazardous area classification
- **Standards Validation:** IEEE/IEC/EN compliance checking
- **Authentication/Authorization:** User management and access control
- **Data Integrity:** Database constraints and validation

#### High Priority Modules (85%+ Coverage Required)
- **Service Layer:** Business logic and workflow orchestration
- **Repository Layer:** Data access and database operations
- **API Endpoints:** HTTP interface and request handling
- **Schema Validation:** Input/output data validation
- **Error Handling:** Unified error handling patterns

#### Standard Modules (75%+ Coverage Required)
- **Utility Functions:** Helper functions and common operations
- **Configuration Management:** Settings and environment handling
- **Logging and Monitoring:** Observability and debugging
- **Migration Scripts:** Database schema evolution

### Coverage Measurement

#### Coverage Commands
```bash
# Generate coverage report
make test-coverage

# Generate HTML coverage report
make test-coverage-html

# Check coverage thresholds
make test-coverage-check

# Coverage by module type
pytest --cov=core/calculations --cov-report=term-missing --cov-fail-under=90
pytest --cov=core/services --cov-report=term-missing --cov-fail-under=85
pytest --cov=core/repositories --cov-report=term-missing --cov-fail-under=85
```

#### Coverage Configuration
```ini
# .coveragerc - Coverage configuration
[run]
source = .
omit = 
    */tests/*
    */venv/*
    */migrations/*
    */scripts/*
    */conftest.py
    */test_*.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[html]
directory = htmlcov

[xml]
output = coverage.xml
```

## Real Database Testing

### No Mocks Policy

The project maintains a strict "NO MOCKS" policy for database testing to ensure:
- **Real Integration Testing:** Actual database interactions are tested
- **Data Integrity Validation:** Database constraints and relationships are verified
- **Performance Reality:** Real query performance is measured
- **Migration Validation:** Database schema changes are tested with real data

### Database Test Patterns

#### Repository Testing with Real Database
```python
# tests/integration/test_project_repository.py
import pytest
from sqlalchemy.orm import Session
from src.core.repositories.project_repository import ProjectRepository
from src.core.models.project import Project

@pytest.mark.database
@pytest.mark.integration
class TestProjectRepository:
    """Test project repository with real database operations."""
    
    def test_create_project_with_real_database(
        self, 
        db_session: Session, 
        test_project_data: dict
    ):
        """Test project creation with actual database."""
        repository = ProjectRepository(db_session)
        
        # Create project
        project = repository.create(test_project_data)
        
        # Verify in database
        assert project.id is not None
        assert project.name == test_project_data["name"]
        
        # Verify database persistence
        db_project = db_session.query(Project).filter_by(id=project.id).first()
        assert db_project is not None
        assert db_project.name == test_project_data["name"]
        assert db_project.voltage_level == test_project_data["voltage_level"]
    
    def test_project_name_uniqueness_constraint(
        self, 
        db_session: Session, 
        test_project_data: dict
    ):
        """Test database uniqueness constraint enforcement."""
        repository = ProjectRepository(db_session)
        
        # Create first project
        project1 = repository.create(test_project_data)
        assert project1.id is not None
        
        # Attempt to create duplicate name - should raise exception
        with pytest.raises(DuplicateError) as exc_info:
            repository.create(test_project_data)
        
        assert "already exists" in str(exc_info.value)
    
    def test_soft_delete_functionality(
        self, 
        db_session: Session, 
        test_project_data: dict
    ):
        """Test soft delete with real database."""
        repository = ProjectRepository(db_session)
        
        # Create and soft delete project
        project = repository.create(test_project_data)
        project_id = project.id
        
        repository.soft_delete(project_id, deleted_by=1)
        
        # Verify soft delete in database
        db_project = db_session.query(Project).filter_by(id=project_id).first()
        assert db_project.deleted_at is not None
        assert db_project.deleted_by == 1
        
        # Verify project not returned in normal queries
        active_project = repository.get_by_id(project_id)
        assert active_project is None
    
    def test_complex_filtering_with_real_data(
        self, 
        db_session: Session
    ):
        """Test complex filtering with multiple real database records."""
        repository = ProjectRepository(db_session)
        
        # Create multiple projects with different characteristics
        projects_data = [
            {
                "name": "Indoor Project",
                "voltage_level": 400.0,
                "environment_type": "indoor",
                "ambient_temperature_min": 0.0,
                "ambient_temperature_max": 40.0,
                "status": "active"
            },
            {
                "name": "Outdoor Project", 
                "voltage_level": 13800.0,
                "environment_type": "outdoor",
                "ambient_temperature_min": -20.0,
                "ambient_temperature_max": 50.0,
                "status": "draft"
            },
            {
                "name": "Hazardous Project",
                "voltage_level": 600.0,
                "environment_type": "hazardous",
                "ambient_temperature_min": -10.0,
                "ambient_temperature_max": 60.0,
                "status": "active"
            }
        ]
        
        created_projects = []
        for project_data in projects_data:
            project = repository.create(project_data)
            created_projects.append(project)
        
        # Test filtering by environment type
        hazardous_projects = repository.list_with_filters(
            environment_type="hazardous"
        )
        assert len(hazardous_projects) == 1
        assert hazardous_projects[0].name == "Hazardous Project"
        
        # Test filtering by voltage range
        high_voltage_projects = repository.list_with_filters(
            voltage_min=1000.0
        )
        assert len(high_voltage_projects) == 1
        assert high_voltage_projects[0].name == "Outdoor Project"
        
        # Test filtering by status
        active_projects = repository.list_with_filters(status="active")
        assert len(active_projects) == 2
        
        # Test search functionality
        search_results = repository.list_with_filters(search="Indoor")
        assert len(search_results) == 1
        assert search_results[0].name == "Indoor Project"
```

#### Service Layer Integration Testing
```python
# tests/integration/test_project_service.py
import pytest
from sqlalchemy.orm import Session
from src.core.services.project_service import ProjectService
from src.core.repositories.project_repository import ProjectRepository

@pytest.mark.integration
@pytest.mark.database
class TestProjectServiceIntegration:
    """Test project service with real database integration."""
    
    def test_complete_project_lifecycle(
        self, 
        db_session: Session, 
        test_project_data: dict,
        test_user
    ):
        """Test complete project lifecycle with real database."""
        repository = ProjectRepository(db_session)
        service = ProjectService(repository)
        
        # Create project
        project = service.create_project(
            test_project_data, 
            created_by=test_user.id
        )
        
        assert project.id is not None
        assert project.created_by == test_user.id
        assert project.status.value == "draft"
        
        # Update project
        update_data = {
            "status": "active",
            "description": "Updated description"
        }
        updated_project = service.update_project(
            project.id, 
            update_data, 
            updated_by=test_user.id
        )
        
        assert updated_project.status.value == "active"
        assert updated_project.description == "Updated description"
        assert updated_project.updated_by == test_user.id
        
        # Get project statistics
        stats = service.get_project_statistics(project.id)
        assert stats["project_id"] == project.id
        assert stats["voltage_level"] == test_project_data["voltage_level"]
        assert stats["temperature_range"] == (
            test_project_data["ambient_temperature_max"] - 
            test_project_data["ambient_temperature_min"]
        )
        
        # Delete project
        service.delete_project(project.id, deleted_by=test_user.id)
        
        # Verify deletion
        with pytest.raises(NotFoundError):
            service.get_project_by_id(project.id)
```

---

**Navigation:**  
← [Previous: Frontend Transition](060-frontend-transition.md) | [Handbook Home](001-cover.md) | [Next: Script Ecosystem](080-script-ecosystem.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Testing Scripts Documentation](../../backend/scripts/testing.md)
- [Coverage Analysis Reports](../../backend/test_logs/)
- [Performance Benchmarks](../../backend/docs/performance/)
