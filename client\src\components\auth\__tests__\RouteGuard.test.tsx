import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { RouteGuard } from '../RouteGuard'
import { useAuth } from '../../../hooks/useAuth'

// Mock the useAuth hook
vi.mock('../../../hooks/useAuth')

// Mock Next.js router
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

const mockUseAuth = vi.mocked(useAuth)

describe('RouteGuard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockPush.mockClear()
  })

  it('should render children when user is authenticated', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: { id: '1', username: 'test', email: '<EMAIL>', roles: ['user'] },
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: vi.fn(() => true),
      loginError: null,
    })

    render(
      <RouteGuard>
        <div>Protected Content</div>
      </RouteGuard>
    )

    expect(screen.getByText('Protected Content')).toBeInTheDocument()
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('should redirect to login when user is not authenticated', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: vi.fn(() => false),
      loginError: null,
    })

    render(
      <RouteGuard>
        <div>Protected Content</div>
      </RouteGuard>
    )

    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
    expect(mockPush).toHaveBeenCalledWith('/login')
  })

  it('should show loading state when authentication is loading', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      isLoading: true,
      user: null,
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: vi.fn(() => false),
      loginError: null,
    })

    render(
      <RouteGuard>
        <div>Protected Content</div>
      </RouteGuard>
    )

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('should redirect to login with custom redirect path', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: vi.fn(() => false),
      loginError: null,
    })

    render(
      <RouteGuard redirectTo="/custom-login">
        <div>Protected Content</div>
      </RouteGuard>
    )

    expect(mockPush).toHaveBeenCalledWith('/custom-login')
  })

  it('should check required roles when provided', () => {
    const mockHasRole = vi.fn(() => false)
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: { id: '1', username: 'test', email: '<EMAIL>', roles: ['user'] },
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: mockHasRole,
      loginError: null,
    })

    render(
      <RouteGuard requiredRoles={['admin']}>
        <div>Admin Content</div>
      </RouteGuard>
    )

    expect(mockHasRole).toHaveBeenCalledWith('admin')
    expect(screen.queryByText('Admin Content')).not.toBeInTheDocument()
    expect(screen.getByText('Access Denied')).toBeInTheDocument()
  })

  it('should render children when user has required role', () => {
    const mockHasRole = vi.fn(() => true)
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: { id: '1', username: 'test', email: '<EMAIL>', roles: ['admin'] },
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => true),
      hasRole: mockHasRole,
      loginError: null,
    })

    render(
      <RouteGuard requiredRoles={['admin']}>
        <div>Admin Content</div>
      </RouteGuard>
    )

    expect(mockHasRole).toHaveBeenCalledWith('admin')
    expect(screen.getByText('Admin Content')).toBeInTheDocument()
  })

  it('should check multiple required roles', () => {
    const mockHasRole = vi.fn((role) => role === 'user')
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: { id: '1', username: 'test', email: '<EMAIL>', roles: ['user'] },
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: mockHasRole,
      loginError: null,
    })

    render(
      <RouteGuard requiredRoles={['user', 'admin']}>
        <div>Multi-Role Content</div>
      </RouteGuard>
    )

    expect(mockHasRole).toHaveBeenCalledWith('user')
    expect(mockHasRole).toHaveBeenCalledWith('admin')
    expect(screen.getByText('Multi-Role Content')).toBeInTheDocument()
  })

  it('should show access denied when user lacks any required role', () => {
    const mockHasRole = vi.fn(() => false)
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: { id: '1', username: 'test', email: '<EMAIL>', roles: ['user'] },
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: mockHasRole,
      loginError: null,
    })

    render(
      <RouteGuard requiredRoles={['admin', 'moderator']}>
        <div>Restricted Content</div>
      </RouteGuard>
    )

    expect(screen.queryByText('Restricted Content')).not.toBeInTheDocument()
    expect(screen.getByText('Access Denied')).toBeInTheDocument()
  })

  it('should render custom access denied component', () => {
    const mockHasRole = vi.fn(() => false)
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: { id: '1', username: 'test', email: '<EMAIL>', roles: ['user'] },
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: mockHasRole,
      loginError: null,
    })

    const CustomAccessDenied = () => <div>Custom Access Denied Message</div>

    render(
      <RouteGuard 
        requiredRoles={['admin']} 
        fallback={<CustomAccessDenied />}
      >
        <div>Admin Content</div>
      </RouteGuard>
    )

    expect(screen.getByText('Custom Access Denied Message')).toBeInTheDocument()
    expect(screen.queryByText('Access Denied')).not.toBeInTheDocument()
  })

  it('should render custom loading component', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      isLoading: true,
      user: null,
      login: vi.fn(),
      logout: vi.fn(),
      isAdmin: vi.fn(() => false),
      hasRole: vi.fn(() => false),
      loginError: null,
    })

    const CustomLoading = () => <div>Custom Loading...</div>

    render(
      <RouteGuard loadingComponent={<CustomLoading />}>
        <div>Protected Content</div>
      </RouteGuard>
    )

    expect(screen.getByText('Custom Loading...')).toBeInTheDocument()
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
  })
})
