# tests/middleware/test_rate_limiting_middleware.py
"""
Comprehensive tests for RateLimitingMiddleware following robust test patterns.

Tests cover:
- Per-IP and per-user rate limiting
- Sliding window algorithm
- Endpoint-specific rate limits
- Token bucket burst protection
- Rate limit headers
- Error responses and retry-after
- Configuration and exclusion paths
"""

import time
from unittest.mock import Mock, patch

import pytest
from fastapi import Request, Response
from fastapi.responses import JSONResponse

from src.middleware.rate_limiting_middleware import RateLimitingMiddleware
from fixtures.api_mocks import (
    mock_app,
    mock_request,
    mock_response,
    mock_call_next,
)
from fixtures.middleware_fixtures import rate_limiting_middleware
from fixtures.app_fixtures import test_app_with_full_stack

pytestmark = [pytest.mark.integration]


class TestRateLimitingMiddleware:
    """Comprehensive test suite for RateLimitingMiddleware."""

    def test_middleware_initialization_default_config(self, mock_app):
        """Test middleware initialization with default configuration."""
        middleware = RateLimitingMiddleware(mock_app)

        assert middleware.default_requests_per_minute == 60
        assert middleware.default_burst_size == 10
        assert middleware.enable_per_ip_limiting is True
        assert middleware.enable_per_user_limiting is True
        assert middleware.enable_endpoint_specific_limits is True
        assert middleware.redis_client is None

    def test_middleware_initialization_custom_config(self, mock_app):
        """Test middleware initialization with custom configuration."""
        custom_exclude_paths = {"/custom", "/test"}
        middleware = RateLimitingMiddleware(
            app=mock_app,
            default_requests_per_minute=120,
            default_burst_size=20,
            enable_per_ip_limiting=False,
            enable_per_user_limiting=False,
            enable_endpoint_specific_limits=False,
            exclude_paths=custom_exclude_paths,
        )

        assert middleware.default_requests_per_minute == 120
        assert middleware.default_burst_size == 20
        assert middleware.enable_per_ip_limiting is False
        assert middleware.enable_per_user_limiting is False
        assert middleware.enable_endpoint_specific_limits is False
        assert custom_exclude_paths.issubset(middleware.exclude_paths)

    def test_endpoint_limits_configuration(self, rate_limiting_middleware):
        """Test endpoint-specific rate limits configuration."""
        limits = rate_limiting_middleware.endpoint_limits

        # Test authentication endpoints have stricter limits
        assert limits["/api/v1/auth/login"]["requests_per_minute"] == 10
        assert limits["/api/v1/auth/login"]["burst_size"] == 3

        # Test calculation endpoints have higher limits
        assert limits["/api/v1/heat-tracing/calculate"]["requests_per_minute"] == 100
        assert limits["/api/v1/heat-tracing/calculate"]["burst_size"] == 20

        # Test admin endpoints have strict limits
        assert limits["/api/v1/admin"]["requests_per_minute"] == 20
        assert limits["/api/v1/admin"]["burst_size"] == 5

    def test_get_endpoint_limits_exact_match(self, rate_limiting_middleware):
        """Test endpoint limits retrieval for exact path matches."""
        limits = rate_limiting_middleware._get_endpoint_limits("/api/v1/auth/login")

        assert limits["requests_per_minute"] == 10
        assert limits["burst_size"] == 3

    def test_get_endpoint_limits_prefix_match(self, rate_limiting_middleware):
        """Test endpoint limits retrieval for prefix matches."""
        limits = rate_limiting_middleware._get_endpoint_limits("/api/v1/admin/users")

        assert limits["requests_per_minute"] == 20
        assert limits["burst_size"] == 5

    def test_get_endpoint_limits_default(self, rate_limiting_middleware):
        """Test endpoint limits retrieval for unknown paths."""
        limits = rate_limiting_middleware._get_endpoint_limits("/api/v1/unknown")

        assert limits["requests_per_minute"] == 60
        assert limits["burst_size"] == 10

    def test_should_exclude_path(self, rate_limiting_middleware):
        """Test path exclusion functionality."""
        assert rate_limiting_middleware._should_exclude_path("/health") is True
        assert rate_limiting_middleware._should_exclude_path("/docs") is True
        assert rate_limiting_middleware._should_exclude_path("/api/v1/test") is False

    def test_get_client_ip_forwarded_headers(
        self, rate_limiting_middleware, mock_request
    ):
        """Test client IP extraction from forwarded headers."""
        # Test X-Forwarded-For header
        mock_request.headers = {"x-forwarded-for": "***********, ********"}
        ip = rate_limiting_middleware._get_client_ip(mock_request)
        assert ip == "***********"

        # Test X-Real-IP header
        mock_request.headers = {"x-real-ip": "***********"}
        ip = rate_limiting_middleware._get_client_ip(mock_request)
        assert ip == "***********"

    def test_get_client_ip_direct_client(self, rate_limiting_middleware, mock_request):
        """Test client IP extraction from direct client."""
        mock_request.headers = {}
        mock_request.client.host = "127.0.0.1"

        ip = rate_limiting_middleware._get_client_ip(mock_request)
        assert ip == "127.0.0.1"

    def test_get_client_ip_unknown(self, rate_limiting_middleware, mock_request):
        """Test client IP extraction when no source available."""
        mock_request.headers = {}
        mock_request.client = None

        ip = rate_limiting_middleware._get_client_ip(mock_request)
        assert ip == "unknown"

    def test_check_ip_rate_limit_within_limit(self, rate_limiting_middleware):
        """Test IP rate limiting when within limits."""
        current_time = time.time()
        limits = {"requests_per_minute": 60, "burst_size": 10}

        result = rate_limiting_middleware._check_ip_rate_limit(
            "127.0.0.1", current_time, limits
        )

        assert result["allowed"] is True
        assert result["remaining"] == 59  # 60 - 1 request

    def test_check_ip_rate_limit_exceeded(self, rate_limiting_middleware):
        """Test IP rate limiting when limit is exceeded."""
        current_time = time.time()
        limits = {"requests_per_minute": 2, "burst_size": 1}
        client_ip = "127.0.0.1"

        # Fill up the rate limit
        for _ in range(2):
            rate_limiting_middleware._check_ip_rate_limit(
                client_ip, current_time, limits
            )

        # Next request should be rate limited
        result = rate_limiting_middleware._check_ip_rate_limit(
            client_ip, current_time, limits
        )

        assert result["allowed"] is False
        assert result["remaining"] == 0
        assert result["retry_after"] > 0

    def test_check_ip_rate_limit_sliding_window(self, rate_limiting_middleware):
        """Test IP rate limiting sliding window behavior."""
        limits = {"requests_per_minute": 2, "burst_size": 1}
        client_ip = "127.0.0.1"

        # Make requests at different times
        time1 = 1000.0
        time2 = 1030.0  # 30 seconds later
        time3 = 1070.0  # 70 seconds from start (outside window)

        # First request
        result1 = rate_limiting_middleware._check_ip_rate_limit(
            client_ip, time1, limits
        )
        assert result1["allowed"] is True

        # Second request (still within window)
        result2 = rate_limiting_middleware._check_ip_rate_limit(
            client_ip, time2, limits
        )
        assert result2["allowed"] is True

        # Third request (outside window, should be allowed)
        result3 = rate_limiting_middleware._check_ip_rate_limit(
            client_ip, time3, limits
        )
        assert result3["allowed"] is True

    @patch("src.middleware.rate_limiting_middleware.get_user_context")
    def test_check_user_rate_limit_authenticated(
        self, mock_get_user_context, rate_limiting_middleware
    ):
        """Test user rate limiting for authenticated users."""
        mock_get_user_context.return_value = {"id": "user123"}
        current_time = time.time()
        limits = {"requests_per_minute": 60, "burst_size": 10}

        result = rate_limiting_middleware._check_user_rate_limit(
            "user123", current_time, limits
        )

        assert result["allowed"] is True
        # User limits are doubled, so remaining should be 119 (120 - 1)
        assert result["remaining"] == 119

    def test_check_token_bucket_initial_state(self, rate_limiting_middleware):
        """Test token bucket initial state."""
        current_time = time.time()
        limits = {"requests_per_minute": 60, "burst_size": 10}
        bucket_key = "test_bucket"

        result = rate_limiting_middleware._check_token_bucket(
            bucket_key, current_time, limits
        )

        assert result["allowed"] is True
        assert result["remaining"] == 9  # 10 - 1 token consumed

    def test_check_token_bucket_exhausted(self, rate_limiting_middleware):
        """Test token bucket when tokens are exhausted."""
        current_time = time.time()
        limits = {"requests_per_minute": 60, "burst_size": 2}
        bucket_key = "test_bucket"

        # Consume all tokens
        for _ in range(2):
            rate_limiting_middleware._check_token_bucket(
                bucket_key, current_time, limits
            )

        # Next request should be rate limited
        result = rate_limiting_middleware._check_token_bucket(
            bucket_key, current_time, limits
        )

        assert result["allowed"] is False
        assert result["remaining"] == 0
        assert result["retry_after"] > 0

    def test_check_token_bucket_refill(self, rate_limiting_middleware):
        """Test token bucket refill over time."""
        limits = {"requests_per_minute": 60, "burst_size": 2}
        bucket_key = "test_bucket"

        time1 = 1000.0
        time2 = 1002.0  # 2 seconds later

        # Consume all tokens
        for _ in range(2):
            rate_limiting_middleware._check_token_bucket(bucket_key, time1, limits)

        # Check that tokens are exhausted
        result1 = rate_limiting_middleware._check_token_bucket(
            bucket_key, time1, limits
        )
        assert result1["allowed"] is False

        # After time passes, tokens should be refilled
        result2 = rate_limiting_middleware._check_token_bucket(
            bucket_key, time2, limits
        )
        assert result2["allowed"] is True

    @pytest.mark.asyncio
    @patch("src.middleware.rate_limiting_middleware.get_user_context")
    async def test_successful_request_within_limits(
        self,
        mock_get_user_context,
        rate_limiting_middleware,
        mock_request,
        mock_call_next,
        monkeypatch,
    ):
        """Test successful request processing within rate limits."""
        # Mock settings to enable rate limiting for this test
        from unittest.mock import MagicMock
        mock_settings = MagicMock()
        mock_settings.RATE_LIMIT_ENABLED = True
        monkeypatch.setattr("src.config.settings.settings", mock_settings)

        mock_get_user_context.return_value = {}

        response = await rate_limiting_middleware.dispatch(mock_request, mock_call_next)

        assert response.status_code == 200
        # Verify rate limit headers were added
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert "X-RateLimit-Reset" in response.headers

    @pytest.mark.asyncio
    async def test_excluded_path_bypass(
        self, rate_limiting_middleware, mock_request, mock_call_next
    ):
        """Test that excluded paths bypass rate limiting."""
        mock_request.url.path = "/health"

        response = await rate_limiting_middleware.dispatch(mock_request, mock_call_next)

        # Should process normally without rate limiting
        assert response.status_code == 200
        # Should not have rate limit headers
        assert "X-RateLimit-Limit" not in response.headers

    def test_create_rate_limit_response(self, rate_limiting_middleware, mock_request):
        """Test rate limit exceeded response creation."""
        rate_limit_result = {
            "allowed": False,
            "limits": {"requests_per_minute": 60},
            "retry_after": 30,
            "reset_time": time.time() + 60,
            "limit_type": "ip",
        }

        response = rate_limiting_middleware._create_rate_limit_response(
            rate_limit_result, mock_request
        )

        assert isinstance(response, JSONResponse)
        assert response.status_code == 429
        assert "Retry-After" in response.headers
        assert response.headers["Retry-After"] == "30"
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert response.headers["X-RateLimit-Remaining"] == "0"

    @pytest.mark.asyncio
    async def test_error_handling_graceful_degradation(
        self, rate_limiting_middleware, mock_request, mock_call_next
    ):
        """Test graceful error handling when rate limiting fails."""
        # Mock an error in rate limit checking
        with patch.object(
            rate_limiting_middleware,
            "_check_rate_limits",
            side_effect=Exception("Test error"),
        ):
            response = await rate_limiting_middleware.dispatch(
                mock_request, mock_call_next
            )

            # Should continue processing despite rate limiting error
            assert response.status_code == 200


class TestRateLimitingMiddlewareIntegration:
    """Integration tests for RateLimitingMiddleware with real FastAPI app."""

    def test_integration_within_rate_limit(self, test_app_with_full_stack, monkeypatch):
        """Test rate limiting integration with requests within limits."""
        from fastapi.testclient import TestClient
        from unittest.mock import MagicMock

        # Mock settings to enable rate limiting for this test
        mock_settings = MagicMock()
        mock_settings.RATE_LIMIT_ENABLED = True
        monkeypatch.setattr("src.config.settings.settings", mock_settings)

        client = TestClient(test_app_with_full_stack)

        # First request should succeed
        response = client.get("/test")
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "test"
        assert "timestamp" in response_data

        # Verify rate limit headers
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert "X-RateLimit-Reset" in response.headers

    def test_integration_rate_limit_exceeded(self, test_app_with_full_stack, monkeypatch):
        """Test rate limiting integration when limits are exceeded."""
        from fastapi.testclient import TestClient
        from unittest.mock import MagicMock

        # Mock settings to enable rate limiting for this test
        mock_settings = MagicMock()
        mock_settings.RATE_LIMIT_ENABLED = True
        monkeypatch.setattr("src.config.settings.settings", mock_settings)

        # The test_app_with_full_stack fixture should be configured with a small burst size
        # for this test to work correctly. We assume the fixture is configured with a
        # burst size of 5 for the /test endpoint.

        client = TestClient(test_app_with_full_stack)

        # Make requests until we hit the rate limit
        rate_limited = False
        successful_requests = 0

        for i in range(10):  # Try up to 10 requests
            response = client.get("/test")
            if response.status_code == 200:
                successful_requests += 1
            elif response.status_code == 429:
                rate_limited = True
                # Verify rate limit response
                assert "Retry-After" in response.headers
                break
            else:
                pytest.fail(f"Unexpected status code: {response.status_code}")

        # Should have had at least one successful request and then hit rate limit
        assert successful_requests >= 1, f"Expected at least 1 successful request, got {successful_requests}"
        assert rate_limited, "Expected to hit rate limit but didn't"

    def test_integration_excluded_path(self, test_app_with_full_stack):
        """Test that excluded paths bypass rate limiting."""
        from fastapi.testclient import TestClient

        client = TestClient(test_app_with_full_stack)

        # Make many requests to health endpoint (should be excluded)
        for _ in range(10):
            response = client.get("/health")
            assert response.status_code == 200
            # Should not have rate limit headers
            assert "X-RateLimit-Limit" not in response.headers


class TestRateLimitingMiddlewarePerformance:
    """Performance tests for RateLimitingMiddleware."""

    @pytest.mark.performance
    def test_rate_limiting_performance(self, rate_limiting_middleware):
        """Test rate limiting performance with many requests."""
        import time

        current_time = time.time()
        limits = {"requests_per_minute": 1000, "burst_size": 100}

        start_time = time.time()

        # Simulate 1000 rate limit checks
        for i in range(1000):
            client_ip = f"192.168.1.{i % 255}"
            result = rate_limiting_middleware._check_ip_rate_limit(
                client_ip, current_time, limits
            )
            assert result["allowed"] is True

        duration = time.time() - start_time

        # Should complete within reasonable time (< 1 second)
        assert duration < 1.0
        print(f"Rate limiting performance: {1000 / duration:.0f} checks/second")

    @pytest.mark.performance
    def test_memory_usage_with_many_ips(self, rate_limiting_middleware):
        """Test memory usage with many different IP addresses."""
        import time

        current_time = time.time()
        limits = {"requests_per_minute": 60, "burst_size": 10}

        initial_size = len(rate_limiting_middleware.ip_requests)

        # Simulate requests from 1000 different IPs
        for i in range(1000):
            client_ip = f"192.168.{i // 255}.{i % 255}"
            rate_limiting_middleware._check_ip_rate_limit(
                client_ip, current_time, limits
            )

        final_size = len(rate_limiting_middleware.ip_requests)

        # Should have created entries for all IPs
        assert final_size == initial_size + 1000

        # Memory usage should be reasonable (each deque should be small)
        total_requests = sum(
            len(deque) for deque in rate_limiting_middleware.ip_requests.values()
        )
        assert total_requests == 1000  # One request per IP
